version: 2.1
orbs:
  node: circleci/node@4.2.0

jobs:
  node:
    working_directory: ~/demo-react-native
    docker:
      - image: circleci/node:12.16.1
    environment:
      TERM: dumb
      JAVA_OPTS: "-Xms512m -Xmx2g"
      GRADLE_OPTS: '-Xmx3g -Dorg.gradle.daemon=false -Dorg.gradle.jvmargs="-Xmx2g -XX:+HeapDumpOnOutOfMemoryError"'
    steps:
      - checkout

      - run:
          name: Yarn Install
          command: yarn install --cache-folder ~/.cache/yarn

      - restore_cache:
          key: yarn-v1-{{ checksum "yarn.lock" }}-{{ arch }}

      - restore_cache:
          key: node-v1-{{ checksum "package.json" }}-{{ arch }}

      - run: yarn install

      - save_cache:
          key: yarn-v1-{{ checksum "yarn.lock" }}-{{ arch }}
          paths:
            - ~/.cache/yarn

      - save_cache:
          key: node-v1-{{ checksum "package.json" }}-{{ arch }}
          paths:
            - node_modules

      - persist_to_workspace:
          root: ~/demo-react-native
          paths:
            - node_modules

  android:
    working_directory: ~/demo-react-native/android
    docker:
      - image: circleci/android:api-28-node

    steps:
      - checkout:
          path: ~/demo-react-native

      - attach_workspace:
          at: ~/demo-react-native

      - restore_cache:
          key: bundle-v1-{{ checksum "Gemfile.lock" }}-{{ arch }}

      - run: bundle install
      - run: chmod a+x gradlew
      # - run: yes | sdkmanager --licenses && sdkmanager --update

      - save_cache:
          key: bundle-v1-{{ checksum "Gemfile.lock" }}-{{ arch }}
          paths:
            - vendor/bundle

      - run:
          name: fastlane android production
          command: |
            bundle exec fastlane production
      - store_artifacts:
          path: app/build/outputs/apk/release/app-release.apk

  ios-beta:
    macos:
      xcode: "12.3.0"
    working_directory: ~/demo-react-native/ios

    shell: /bin/bash --login -o pipefail

    steps:
      - checkout:
          path: ~/demo-react-native

      - attach_workspace:
          at: ~/demo-react-native

      - run:
          name: set Ruby version
          command: echo "ruby-2.4" > ~/.ruby-version

      - restore_cache:
          key: bundle-v1-{{ checksum "Gemfile.lock" }}-{{ arch }}

      - run:
          command: gem update bundler

      - run:
          command: bundle install

      - save_cache:
          key: bundle-v1-{{ checksum "Gemfile.lock" }}-{{ arch }}
          paths:
            - vendor/bundle 
      - run:
          command : pod repo update   
      - run:
          command: pod install 

      - run:
          command: bundle exec fastlane prod

      # - persist_to_workspace:
      #     root: .
      #     paths:
      #       - .

      - store_artifacts:
          path: Danapay Transfer.ipa

  build:
    docker:
      - image: circleci/openjdk:stretch

    working_directory: ~/demo-react-native/ios

    steps:
      - run: mvn -v

      - checkout

      - node/install:
          install-yarn: true
      - run: node --version

      - run:
          command: npm i -g appcenter-cli

      - run:
          command: <NAME_EMAIL>:Cartezi/appium_automation_tests.git

      - checkout:
          path: ~/demo-react-native/ios

      - attach_workspace:
          at: ~/demo-react-native/ios

      - run:
          command: ls

      - run:
          command: ./appium_automation_tests/transfer-ios/Maven/upload.sh
      - run:
          command: bundle exec fastlane prod

  build-android:
    docker:
      - image: circleci/openjdk:stretch

    working_directory: ~/demo-react-native/android

    steps:
      - run: mvn -v

      - checkout

      - node/install:
          install-yarn: true
      - run: node --version

      - run:
          command: npm i -g appcenter-cli

      - run:
          command: <NAME_EMAIL>:Cartezi/appium_automation_tests.git

      - checkout:
          path: ~/demo-react-native/android

      - attach_workspace:
          at: ~/demo-react-native/android

      - run:
          command: ls

      - run:
          command: sh ./appium_automation_tests/transfer-android/Maven/upload.sh
      - run:
          name: fastlane android production
          command: |
            bundle exec fastlane production

workflows:
  # version: 2.1
  node-android-ios:
    jobs:
      - node
      - android:
          requires:
            - node
      - ios-beta:
          requires:
            - node
      # - build:
      #     requires:
      #       - ios-beta
      #       - node
      # - build-android:
      #     requires:
      #       - android
      #       - node
