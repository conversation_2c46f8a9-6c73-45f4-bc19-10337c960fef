import React, {useCallback, useEffect} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import analytics from '@react-native-firebase/analytics';
import firebase from '@react-native-firebase/app';
import {AppState, Platform} from 'react-native';
import {MenuProvider} from 'react-native-popup-menu';
import Config from 'react-native-config';
import Icon from 'react-native-vector-icons/Feather';
import Navigation from './src/Navigation';
import * as RNLocalize from 'react-native-localize';
import {setI18nConfig, translate} from './src/utilities/Translate';
import useDanaStore from './src/app_state/store';
import * as Sentry from '@sentry/react-native';
import {initWebSocket} from './src/utilities/sockets';
import {showMessage} from 'react-native-flash-message';
import appsFlyer from 'react-native-appsflyer';
import {requestTrackingPermission} from 'react-native-tracking-transparency';

Sentry.init({
  dsn: 'https://<EMAIL>/4505686627975168',
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1.0,
  integrations: [
    Sentry.mobileReplayIntegration({
      maskAllText: false,
      maskAllImages: false,
      maskAllVectors: false,
    }),
  ],
});

const App = () => {
  const toggleSoftLogout = useDanaStore(state => state.toggleSoftLogout);
  const acceptDataCollection = useDanaStore(
    state => state.acceptDataCollection,
  );
  const fetchTransfer = useDanaStore(state => state.fetchTransfer);
  const getUserCurrentState = useDanaStore(state => state.getUserCurrentState);
  const store_fetch_all_transfers = useDanaStore(
    state => state.store_fetch_all_transfers,
  );
  const user = useDanaStore(state => state.user);
  const token = useDanaStore(state => state.token);

  useEffect(() => {
    RNLocalize.addEventListener('change', handleLocalizationChange);
  }, []);

  useEffect(() => {
    if (user) {
      Sentry.setUser({
        id: user?.phoene_number,
        user_id: user?.id,
        email: user?.email,
        full_name: user?.first_name + ' ' + user?.last_name,
      });
    }
  }, [user]);

  const handleLocalizationChange = () => {
    setI18nConfig();
  };

  const handleAppStateChange = newState => {
    if (newState === 'inactive' || newState === 'background') {
      toggleSoftLogout(true);
    } else {
      toggleSoftLogout(false);
    }
  };

  const checkFirstLaunch = async () => {
    try {
      const hasLaunched = await AsyncStorage.getItem('hasLaunched');
      if (hasLaunched === null) {
        await AsyncStorage.setItem('hasLaunched', 'true');
        await analytics().logEvent('install_complete', {
          timestamp: new Date().toISOString(),
          platform: Platform.OS,
        });
      }
    } catch (error) {
      Sentry.captureException(error);
      console.error('Error checking first launch:', error);
    }
  };

  const initializeFirebase = async () => {
    try {
      // Enable analytics data collection
      await analytics().setAnalyticsCollectionEnabled(true);

      // Enable debug mode in development only
      if (__DEV__) {
        await analytics().setAnalyticsCollectionEnabled(true);
      }

      // Set user properties
      await analytics().setUserProperty('user_type', 'customer');
    } catch (error) {
      console.error('Firebase initialization error:', error);
      Sentry.captureException(error);
    }
  };

  useEffect(() => {
    initializeFirebase();
    checkFirstLaunch();
    setI18nConfig();
    const myListener = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      myListener.remove();
    };
  }, []);

  const updateUser = useCallback(event => {
    getUserCurrentState();
    if (event.title.toLowerCase() === 'account activated') {
      showMessage({
        message: translate('account_activated_title'),
        type: 'success',
      });
    } else {
      showMessage({
        message: translate('account_deactivated'),
        type: 'danger',
      });
    }
  }, []);

  const updateTransfers = useCallback(event => {
    showMessage({
      message: translate('payment_status_change'),
      type: 'success',
    });
    fetchTransfer();
    getUserCurrentState();
  }, []);

  useEffect(() => {
    if (token) {
      const echo = initWebSocket(token);

      const channel = echo.private(`User.${user?.id}`);

      channel.listen('TransferStatusChange', data => {
        showMessage({
          message: translate('notification'),
          description: translate('transfer_updated'),
          type: 'default',
          icon: props => (
            <Icon
              name="bell"
              color="#fff"
              size={30}
              style={{marginRight: 10}}
            />
          ),
        });

        store_fetch_all_transfers(0);
      });

      channel.listen('kYCOnBoardingRejected', data => {
        showMessage({
          message: translate('notification'),
          description: translate('user_updated'),
          type: 'none',
          icon: props => (
            <Icon
              name="bell"
              color="#fff"
              size={30}
              style={{marginRight: 10}}
            />
          ),
        });
        updateUser(data);
      });

      channel.listen('UserActivityStatusChange', data => {
        showMessage({
          message: translate('notification'),
          description: translate('user_updated'),
          type: 'none',
          icon: props => (
            <Icon
              name="bell"
              color="#fff"
              size={30}
              style={{marginRight: 10}}
            />
          ),
        });
        updateUser(data);
      });

      channel.listen('PaymentStatusChange', data => {
        showMessage({
          message: translate('notification'),
          description: translate('payment_updated'),
          type: 'none',
          icon: props => (
            <Icon
              name="bell"
              color="#fff"
              size={30}
              style={{marginRight: 10}}
            />
          ),
        });
        store_fetch_all_transfers(0);
      });

      channel.listen('CashoutStatusChange', data => {
        showMessage({
          message: translate('notification'),
          description: translate('withdrawal_updated'),
          type: 'none',
          icon: props => (
            <Icon
              name="bell"
              color="#fff"
              size={30}
              style={{marginRight: 10}}
            />
          ),
        });
        store_fetch_all_transfers(0);
      });

      channel.listen('PayinStatusChange', data => {
        showMessage({
          message: translate('notification'),
          description: translate('withdrawal_updated'),
          type: 'none',
          icon: props => (
            <Icon
              name="bell"
              color="#fff"
              size={30}
              style={{marginRight: 10}}
            />
          ),
        });
        store_fetch_all_transfers(0);
      });
    }
  }, []);

  useEffect(() => {
    async function checkAndRequestPermission() {
      const status =
        Platform.OS === 'ios'
          ? await requestTrackingPermission()
          : 'authorized';

      if (status === 'authorized') {
        console.log('Tracking authorized');
        acceptDataCollection(true);
        appsFlyer.initSdk(
          {
            devKey: Config.APP_FLYERS_DEV_KEY,
            isDebug: true,
            appId:
              Platform.OS === 'ios'
                ? Config.APP_FLYERS_APP_ID_IOS
                : Config.APP_FLYERS_APP_ID_ANDROID,
            onInstallConversionDataListener: true, //Optional
            onDeepLinkListener: true, //Optional
            timeToWaitForATTUserAuthorization: 10, //for iOS 14.5
          },
          result => {
            console.log('...', result);
          },
          error => {
            console.error('-----', error);
          },
        );
      } else {
        console.log('Tracking not authorized');
      }
    }
    checkAndRequestPermission();
  }, []);

  return (
    <MenuProvider>
      <Navigation />
    </MenuProvider>
  );
};

const withSentry = Sentry.wrap(App);

export default Sentry.wrap(withSentry);
