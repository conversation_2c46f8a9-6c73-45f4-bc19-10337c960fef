# Danapay-transfer-app

A modern Mobile application built with React Native ( For Android and Ios platforms ) offering Withdraws, deposits, transfer  and account management features to serve as another avenue for user to consume Danapay services. Its Currently meant for individual account owner.

## Description

This project's objective is to enhance the Danapay service by providing a mobile application platform. It's worth noting that the repository utilizes the `**************:kigh143/danapay_ios_certs.git` repository for managing the iOS provisioning profile. There's no need to clone it separately, as it's referenced within the `MatchFile`, which can be found at `/ios/fastlane/MatchFile`.

## Prerequites

- Node.js >= 14.0.0
- npm or yarn
- Android studio setup
- Xcode setup 
- Android or Ios device
- Cocoapods
- Fastlane
  

## Installation

1. **Clone the repository:**
2. 
   ```bash
   git clone https://github.com/Cartezi/Danapay-transfer-app.git

   ```

3. **Navigate to the project directory:**
4. 
   ``` bash
   cd Danapay-transfer-app
   ```

5. **Install dependencies:**
6. 
   ```bash 
    npm install
    ```
    or if you use yarn :
    ```bash
    yarn install
   ```

    ```bash 
    cd ios && pod install
    ```


4. **Start the development server:**
 
 Run app for android device : the variant can either be devDebug, stagingDebug, prodRelease and the appIdSuffix dev, staging prod
```bash
 npx react-native run-android --variant=devDebug --appIdSuffix=dev
```

 Run app for IOS device : its better to use Xcode, the app has three schemes dev, stage and prod
  
```bash
 npx react-native run-ios --variant=devDebug --appIdSuffix=dev
```

## Utilization

Make sure you have setup Xcode and Android studio and connect your device to the pC via USB. You may also need to activate developer options for android to run the app on your device.

## Deployment to production env 

I've configured Fastlane for both iOS and Android. The Fastlane lane for Android, intended for production builds, is named "productions," while the lane for iOS, used to upload builds to TestFlight, is named "beta.".
The version number and version code are set to auto-increment in the respective lanes. However, if you intend to publish a new version of the app on the App Store for iOS, you will need to manually update the build number.

### To run the lanes

Android : 

```bash
cd android && bundle exec fastlane production
```

IOS :

You can change beta to dev, production 

```bash
cd ios && bundle exec fastlane beta
```


### code push live  deployment

``` bash 
appcenter codepush release-react -a hkatende-danapay.com/Danapay -d Production  (IOS)
appcenter codepush release-react -a hkatende-danapay.com/Danapay-1 -d Production  (Android)

```

``` bash 
appcenter codepush release-react -a hkatende-danapay.com/Danapay -d staging 

```
 
### Deep links simulations

Android 

```bash
npx uri-scheme open https://danapayapp.page.link/downloadapp --android
```

iOS
 
```bash
npx uri-scheme open https://danapayapp.page.link/downloadapp --ios
```

## Tests

To run the tests:

 ```bash
npm test
 ```
or if you use yarn :

 ```bash
yarn test
 ```

## License

This project is under proprietary license. See the [LICENSE](./LICENSE.md) file for more details.

## Contact

For any questions or feedback, please contact:
- G Katende Hakim - <EMAIL> / [Profil GitHub](https://www.github.com/kigh143)



❌  error: sentry-cli - To disable source maps auto upload, set SENTRY_DISABLE_AUTO_UPLOAD=true in your environment variables. Or to allow failing upload, set SENTRY_ALLOW_FAILURE=true



❌  error: sentry-cli -   INFO    2025-04-15 17:45:07.075858 +03:00 Loaded file referenced by SENTRY_PROPERTIES (sentry.properties)


    the transform cache was reset.

❌  error: No such file or directory (os error 2)


Test the firebase analytics events in debugview 
adb shell setprop debug.firebase.analytics.app com.danapay.transfer


Response: Error in the HTTP2 framing layer
pod repo remove trunk && pod cache clean --all && pod update