<manifest xmlns:android="http://schemas.android.com/apk/res/android" package="com.danapay.transfer">
  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.READ_PROFILE" />
  <uses-permission android:name="android.permission.WRITE_CONTACTS" />
  <uses-permission android:name="android.permission.READ_CONTACTS" />
  <uses-permission android:name="android.permission.VIBRATE" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
  
  <application 
  android:name=".MainApplication" 
  android:label="@string/app_name" 
  android:icon="@mipmap/ic_launcher" 
  android:roundIcon="@mipmap/ic_launcher_round" 
  android:screenOrientation="portrait"
  android:allowBackup="false" 
  android:theme="@style/AppTheme">
    <activity 
    android:name=".MainActivity" 
    android:label="@string/app_name" 
    android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
    android:launchMode="singleTask" 
    android:windowSoftInputMode="adjustResize" 
    android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
    </activity>
    <!-- <activity android:name="com.facebook.react.devsupport.DevSettingsActivity" /> -->
    <meta-data android:name="com.google.android.geo.API_KEY" android:value="AIzaSyAzOVkPKOybqZJx3wXcRTg_AFeTTIIi6xY"/>
  </application>

</manifest>
