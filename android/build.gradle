// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 21
        compileSdkVersion = 34
        targetSdkVersion = 34
        playServicesVersion = "17.0.0" 
        androidMapsUtilsVersion = "2.2.0"
        kotlinVersion = "1.8.0"

        if (System.properties['os.arch'] == "aarch64") {
            // For M1 Users we need to use the NDK 24 which added support for aarch64
            ndkVersion = "24.0.8215888"
        } else {
            // Otherwise we default to the side-by-side NDK version from AGP.
            ndkVersion = "21.4.7075529"
        }
    }
    repositories {
        google()
        mavenCentral()
    }
    
    dependencies {
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.8.0'
        classpath("com.android.tools.build:gradle:7.2.1")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("de.undercouch:gradle-download-task:5.0.1")
        classpath 'com.google.gms:google-services:4.3.14'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.4.1'
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.0")
    }
}

configurations.all {
    resolutionStrategy {
     force "com.facebook.soloader:soloader:0.10.4+"x
    }
    compile.exclude group: 'com.twillio', module: 'video-android'
}

allprojects {
    repositories {
   
        maven { url "https://maven.sumsub.com/repository/maven-public/" }
        maven {
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            url("$rootDir/../node_modules/jsc-android/dist")
        }

        mavenCentral {
            // We don't want to fetch react-native from Maven Central as there are
            // older versions over there.
            content {
                excludeGroup "com.facebook.react"
            }
        }

        google()
        // jcenter()
        maven { url 'https://www.jitpack.io' }

    }
}
