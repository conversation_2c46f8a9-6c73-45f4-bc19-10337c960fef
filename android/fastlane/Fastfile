

default_platform(:android)

platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Submit an application to firebase ."
  lane :staging do
    gradle(task: "clean bundleStagingRelease")
    firebase_app_distribution(
      app: "1:780459320355:android:8ad6ded945230250cdfd96",
      release_notes: "Lots of amazing new features to test out!",
      android_artifact_path:"#{lane_context[SharedValues::GRADLE_AAB_OUTPUT_PATH]}",
      android_artifact_type:'AAB',
      debug:true
    )
  end

  desc "Deploy a new version to the Google Play"
  lane :production do
    increment_version_code(
      gradle_file_path:'./app/build.gradle'
    )
    gradle(task: "clean bundleProdRelease")
    supply(
      track:'production',
      aab:"#{lane_context[SharedValues::GRADLE_AAB_OUTPUT_PATH]}",
    )
    slack(
      message: "Danapay Transfer App successfully uploaded to google play store production.",
      success: true,
      slack_url: "*******************************************************************************",
    )
  end
end


# appIdSuffix="dev"