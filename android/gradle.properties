# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx10248m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=true
# android.jetifier.blacklist=bcprov,/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.navigation/navigation-compose/2.6.0/3f8ce7b0bad21cced0c47b42b6347c65a6e422c3/navigation-compose-2.6.0.aar

# Version of flipper SDK to use with React Native
org.gradle.daemon=true
org.gradle.jvmargs=-Xmx4096m -Xms2048m -XX:MaxMetaspaceSize=1024m  -XX:ThreadStackSize=4096 -XX:CompilerThreadStackSize=4096
file.encoding=utf-8

MYAPP_RELEASE_STORE_FILE=danapaysecret.keystore
MYAPP_RELEASE_KEY_ALIAS=danapaysecret
MYAPP_RELEASE_STORE_PASSWORD=danapay
MYAPP_RELEASE_KEY_PASSWORD=danapay

# android.suppressUnsupportedCompileSdk=34
FLIPPER_VERSION=0.137.0
 
# Use this property to specify which architecture you want to build.
# You can also override it from the CLI using
# ./gradlew <task> -PreactNativeArchitectures=x86_64
reactNativeArchitectures=armeabi-v7a,arm64-v8a,x86,x86_64
 
# Use this property to enable support to the new architecture.
# This will allow you to use TurboModules and the Fabric render in
# your application. You should enable this flag either if you want
# to write custom TurboModules/Fabric components OR use libraries that
# are providing them.
newArchEnabled=false
android.enableR8.fullMode=false