{"loading": "Loading", "number": "Number", "receipts": "Receipts", "validate": "validate", "enter_phone": "Enter phone number", "Help": "Help", "choose_lng_to_watch": "Choose the language to view the help", "code_was_sent": "A secrete code has just been sent to you by SMS please enter it", "confirm_pin": "Confirm your PIN code", "no_records": "You have not yet completed a transaction", "cashoutpoint": "Cash out points", "transfer": "Transfer", "delivery": "Delivery", "amount": "Amount", "fees": "Fees", "delivery_fees": "Delivery fees", "return": "Back", "next": "Next", "previous": "Previous", "payment_success_title": "Payment by credit card", "payment_success_message": "Once your payment is validated we will send you a confirmation notification", "pick_up_location": "Our pick-up locations", "pick_up_location_description": "Select the country", "pay_info": "Payment information", "choose_pay_method": "Choose a desired payment method", "instant_payment": "Instant payment", "bank_card": "Bank Card", "bank_transfer": "Bank transfer", "enter_amount_info": "Enter the amount information", "transfer_accepted": "Your transfer has been taken into account", "select_delivery_district": "Select the district where you want to be delivered", "city": "City", "district": "District", "beneficiary_location": "Beneficiary location", "delivery_to_beneficiary_info": "You can have the Funds delivered to your beneficiary's home or office", "valid_identity": "Make sure the recipient has a valid identity.", "no_deliveries": "No delivery for me this time", "name_on_id": "Name (Presented on the identity card)", "first_name_on_id": "First name (Presented on the identity card)", "yes": "Yes", "no": "No", "secret_code": "Secret code", "Beneficiary": "Beneficiary", "transaction_status": "Transaction status", "repeat": "Repeat", "country": "Country", "beneficiary_information": "Beneficiary information", "delivery_fund": "I choose the delivery of funds for 1.5 €.", "Favorite": "Favorite", "pin_code": "Pin Code", "subtitleChoose": "To keep your information secure", "subtitleError": "Please try again", "titleConfirmFailed": "Your entries did not match", "no_contacts": "Add a contact to your favorites", "select_country": "Select Your current country of residence", "contacts": "Contacts", "beneficiary_information_more": "Enter your beneficiary's information or choose it from your contacts ", "Status_one": "Pending payment", "Status_two": "Payment in progress", "Status_three": "Payment validated", "Status_four": "Transfer in progress", "Status_five": "Transfer completed", "Status_six": "Funds withdrawn", "Status_seven": "Payment failed", "Status8": "Transfer failed", "tutorial1": "Do you work hard abroad?", "tutorial2": "Despite everything, is it important for you to be there for your loved ones?", "tutorial3": "We are committed to your side ...", "calcTitle": "Pay only € 1 transfer fee", "calcSubTitle": "Danapay reduces your money transfer fees to Africa", "calcPayMethod": "Payment method", "calcMoneyInEUR": "Amount in  EUR", "calcMoneyInCFA": "Amount in CFA", "calcAmountRange": "Amount range", "calcExchangeRate": "Exchange rate", "searchCountry": "Country search", "PhoneNumber": "Phone number", "Identity": "Identity", "MiniProfileSubTitle": "Enter your information as it appears on your ID", "lastname": "Last Name", "firstname": "First Name", "summary": "Summary", "summarySubTitle": "Please check your transaction details", "totalToPay": "Total to pay", "transfer_not_accepted": "Your payment has been declined", "payment_fail_message": "Please start over with another payment method.", "home": "Home", "restart": "<PERSON><PERSON>", "Recommande": "RECOMMEND", "Prices": "PRICES", "AllTransfers": "Transfers", "Show_all": "Show All", "ContactTitle": "Contacts", "contactSubTitle": "Add one of your contacts to your favorites to automatically pre-fill your transfer form", "Confidentiality": "Confidentiality", "verify_your_identity": "Verify your identity", "reset_pin": "Reset your PIN code", "security": "Security", "personal_information": "Personal information", "Profile": "Profile", "disconnection": "Disconnection", "email": "E-mail", "at": "at", "amountGrater": "This amount is greater than the maximum you can transfer", "amountLower": "The amount is lower than the minimum you can transfer", "funds": "Funds", "search": "Search by name", "all_error": "Please fill in all required fields", "contact_describe": "This app would like to view your contacts.", "logoutText": "Do you confirm your session closing ?", "accountError": "Sorry, this app can only be used by users with customer accounts.", "cancel": "Cancel", "ok": "Okay", "VCRequired": "Verification Code required.", "PNRequired": "Phone number is required.", "pinError": "Invalid PIN code", "selectPay": "Please select the payment method", "transactions": "Transactions", "attention": "Attention", "attention_one": "It is important to respect your transaction wording so that we can execute your transaction quickly.", "attention_two": "It is important to send the exact amount specified above. Otherwise we could send you the funds back by deducting a management fee of 1€.", "attention_three": "We only accept SEPA credit transfers.", "attention_four": "Any incorrect code or amount will result in additional processing time...", "thankYou": "Thank you, ", "operation_accepted": "Your operation has been taken into account.", "please_send": "Please send us the amount ", "wording": " by SEPA bank transfer specifying the following transaction wording ", "bankInfo": "Danapay Banking Information", "accountHolder": "Account Holder", "address": "Address", "bankCode": "Bank Code", "agencyCode": "Agency Code", "accountNumber": "Account Number", "CheckOutYourPayment": "Check Out Your Payment", "invalidPin": "Invalid PIN code", "verifyTitle": "Identity verification", "verifySubTitle": "Please verify your identity, get your identity document ready", "start_verify": "Get Started", "limitTitle": "Transfer limit almost reached", "limitTitle1": "Transfer Limit", "start": "Start", "noTranslation": "No Transactions made yet", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "accepted": "Account Verified", "oldPin": "Old PIN code ", "newPin": "New PIN code ", "valid_email": "Email address is not valid.", "beneficiary_sub_title": "Please seize the beneficiary information", "paymentHeader": "Bank Card payment", "copy": "Copy", "BC_title": "Your transfer has been taken into account", "BC_sub_title": "Please make a transfer now following the instructions below.", "BC_item1": "Reference", "downloadText": "Check your gallery for the PDF file contenting back information", "copyText": "Text has been copied", "payMo_needed": "Please complete all required fields", "failed": "Failed", "completed": "Completed", "processing": "processing", "started": "Pending", "cashout": "Cashed Out", "RIB": "Transfer details", "means_of_pay": "Payment Method", "confirm_add": "Confirm adding to favorites", "add": "Add", "close": "Close", "limitText1": "By sending", "limitText2_one": "You will exceed your sending limit of ", "limitText2_two": " Start your verification", "limitSubTitle_one": "You are about to reach the limit of  ", "limitSubTitle_two": " sending. Start your verification.", "limitSubTitle1_one": "You have reached the limit of  ", "limitSubTitle1_two": "for sending. Start your verification.", "create_account": "Create an account first", "recommend_title": "Recommend <PERSON><PERSON><PERSON>", "recommend_title1": "<PERSON><PERSON>", "recommend_title2": "Recommended people", "recommend_title3": "show all", "recommend_btn": "Recommend", "list_page": "Recommend", "invite_btn": "Invite", "invitations": "Invitations", "sms": "Hello. I recommend you to use the Danapay application for you next transfers. You can sent up to 7000€ at 1€ fees. Clic on this link to get it www.danapay.io"}