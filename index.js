import 'react-native-gesture-handler';
import {AppRegistry, LogBox} from 'react-native';
import App from './App';
import {name as appName} from './app.json';
import messaging from '@react-native-firebase/messaging';
import { enableScreens } from 'react-native-screens';
enableScreens(false);

LogBox.ignoreAllLogs();
LogBox.ignoreLogs(['new NativeEventEmitter']);

messaging().setBackgroundMessageHandler(async remoteMessage => {
  alert('Message handled in the background!' + remoteMessage);
});
// if (__DEV__) globalThis.RNFBDebug = true;

AppRegistry.registerComponent(appName, () => App);
