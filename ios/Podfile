# Set Hermes flag
ENV['USE_HERMES'] = '0'

# Fix the require paths by ensuring they exist
require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'

# Pod sources
source 'https://cdn.cocoapods.org/'
source 'https://github.com/SumSubstance/Specs.git'

def node_require(script)
   # Resolve script with node to allow for hoisting
   require Pod::Executable.execute_command('node', ['-p',
     "require.resolve(
       '#{script}',
       {paths: [process.argv[1]]},
     )", __dir__]).strip
end

node_require('react-native/scripts/react_native_pods.rb')
node_require('react-native-permissions/scripts/setup.rb')

# Firebase configuration
$RNFirebaseAsStaticFramework = true

# iOS platform configuration
platform :ios, '14.0'
install! 'cocoapods', :deterministic_uuids => false
# prepare_react_native_project!

# ⬇️ uncomment the permissions you need
setup_permissions([
  # 'AppTrackingTransparency',
  # 'Bluetooth',
  # 'Calendars',
  # 'CalendarsWriteOnly',
  # 'Camera',
  # 'Contacts',
  # 'FaceID',
  # 'LocationAccuracy',
  'LocationAlways',
  'LocationWhenInUse',
  # 'MediaLibrary',
  # 'Microphone',
  # 'Motion',
  # 'Notifications',
  # 'PhotoLibrary',
  # 'PhotoLibraryAddOnly',
  # 'Reminders',
  # 'Siri',
  # 'SpeechRecognition',
  # 'StoreKit',
])

target 'transferApp' do
  use_frameworks! :linkage => :static

  # Core pods
  pod 'FirebaseCore'
  pod 'Firebase/Analytics'
  pod 'Firebase/Messaging'
  pod 'RNSentry', :path => '../node_modules/@sentry/react-native/RNSentry.podspec'
  pod "react-native-heap", path: "../node_modules/@heap/react-native-heap"
  pod 'react-native-geolocation', :path => '../node_modules/@react-native-community/geolocation'
  
  # Add permissions explicitly
 pod 'RNPermissions', :path => '../node_modules/react-native-permissions'
  
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    :hermes_enabled => false,
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'transferAppTests' do
    inherit! :complete
  end
  
  target 'transferApp-Dev' do
    inherit! :complete
  end

  target 'transferApp-Stage' do
    inherit! :complete
  end

  post_install do |installer|
    react_native_post_install(installer)
    react_native_post_install(
      installer,
      :mac_catalyst_enabled => false
    )
    __apply_Xcode_12_5_M1_post_install_workaround(installer)
  end
end

target 'transferApp-tvOS' do
  target 'transferApp-tvOSTests' do
    inherit! :search_paths
  end
end
