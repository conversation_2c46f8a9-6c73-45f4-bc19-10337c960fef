PODS:
  - AppsFlyerFramework (6.16.2):
    - AppsFlyerFramework/Main (= 6.16.2)
  - AppsFlyerFramework/Main (6.16.2)
  - boost (1.76.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - customerio-reactnative (3.5.0):
    - customerio-reactnative/nopush (= 3.5.0)
    - CustomerIO/MessagingInApp (= 2.12.1)
    - CustomerIO/Tracking (= 2.12.1)
    - React-Core
  - customerio-reactnative/nopush (3.5.0):
    - CustomerIO/MessagingInApp (= 2.12.1)
    - CustomerIO/MessagingPush (= 2.12.1)
    - CustomerIO/Tracking (= 2.12.1)
    - React-Core
  - CustomerIO/MessagingInApp (2.12.1):
    - CustomerIOMessagingInApp (= 2.12.1)
  - CustomerIO/MessagingPush (2.12.1):
    - CustomerIOMessagingPush (= 2.12.1)
  - CustomerIO/Tracking (2.12.1):
    - CustomerIOTracking (= 2.12.1)
  - CustomerIOCommon (2.12.1)
  - CustomerIOMessagingInApp (2.12.1):
    - CustomerIOTracking (= 2.12.1)
  - CustomerIOMessagingPush (2.12.1):
    - CustomerIOTracking (= 2.12.1)
  - CustomerIOTracking (2.12.1):
    - CustomerIOCommon (= 2.12.1)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.70.15)
  - FBReactNativeSpec (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.70.15)
    - RCTTypeSafety (= 0.70.15)
    - React-Core (= 0.70.15)
    - React-jsi (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - Firebase/Analytics (11.11.0):
    - Firebase/Core
  - Firebase/Core (11.11.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.11.0)
  - Firebase/CoreOnly (11.11.0):
    - FirebaseCore (~> 11.11.0)
  - Firebase/Crashlytics (11.11.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.11.0)
  - Firebase/DynamicLinks (11.11.0):
    - Firebase/CoreOnly
    - FirebaseDynamicLinks (~> 11.11.0)
  - Firebase/Messaging (11.11.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.11.0)
  - FirebaseAnalytics (11.11.0):
    - FirebaseAnalytics/AdIdSupport (= 11.11.0)
    - FirebaseCore (~> 11.11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.11.0):
    - FirebaseCore (~> 11.11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.11.0):
    - FirebaseCoreInternal (~> 11.11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.11.0):
    - FirebaseCore (~> 11.11.0)
  - FirebaseCoreInternal (11.11.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.11.0):
    - FirebaseCore (~> 11.11.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseDynamicLinks (11.11.0):
    - FirebaseCore (~> 11.11.0)
  - FirebaseInstallations (11.11.0):
    - FirebaseCore (~> 11.11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.11.0):
    - FirebaseCore (~> 11.11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfigInterop (11.15.0)
  - FirebaseSessions (11.11.0):
    - FirebaseCore (~> 11.11.0)
    - FirebaseCoreExtension (~> 11.11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - fmt (6.2.1)
  - glog (0.3.5)
  - GoogleAppMeasurement (11.11.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.11.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.11.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - Heap (9.1.0)
  - IdensicMobileSDK (1.35.1):
    - IdensicMobileSDK/Default (= 1.35.1)
  - IdensicMobileSDK/Core (1.35.1)
  - IdensicMobileSDK/Default (1.35.1):
    - IdensicMobileSDK/Core
  - Intercom (15.2.3)
  - intercom-react-native (5.3.1):
    - Intercom (~> 15.2.0)
    - React-Core
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - lottie-ios (4.5.0)
  - lottie-react-native (7.2.2):
    - lottie-ios (= 4.5.0)
    - React-Core
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.70.15)
  - RCTTypeSafety (0.70.15):
    - FBLazyVector (= 0.70.15)
    - RCTRequired (= 0.70.15)
    - React-Core (= 0.70.15)
  - React (0.70.15):
    - React-Core (= 0.70.15)
    - React-Core/DevSupport (= 0.70.15)
    - React-Core/RCTWebSocket (= 0.70.15)
    - React-RCTActionSheet (= 0.70.15)
    - React-RCTAnimation (= 0.70.15)
    - React-RCTBlob (= 0.70.15)
    - React-RCTImage (= 0.70.15)
    - React-RCTLinking (= 0.70.15)
    - React-RCTNetwork (= 0.70.15)
    - React-RCTSettings (= 0.70.15)
    - React-RCTText (= 0.70.15)
    - React-RCTVibration (= 0.70.15)
  - React-bridging (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - React-jsi (= 0.70.15)
  - React-callinvoker (0.70.15)
  - React-Codegen (0.70.15):
    - FBReactNativeSpec (= 0.70.15)
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.70.15)
    - RCTTypeSafety (= 0.70.15)
    - React-Core (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-Core (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.70.15)
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/CoreModulesHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/Default (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/DevSupport (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.70.15)
    - React-Core/RCTWebSocket (= 0.70.15)
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-jsinspector (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTBlobHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTImageHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTTextHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTWebSocket (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.70.15)
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-CoreModules (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.15)
    - React-Codegen (= 0.70.15)
    - React-Core/CoreModulesHeaders (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-RCTImage (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-cxxreact (0.70.15):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsinspector (= 0.70.15)
    - React-logger (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - React-runtimeexecutor (= 0.70.15)
  - React-jsi (0.70.15):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-jsi/Default (= 0.70.15)
  - React-jsi/Default (0.70.15):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.70.15):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-perflogger (= 0.70.15)
  - React-jsinspector (0.70.15)
  - React-logger (0.70.15):
    - glog
  - react-native-appsflyer (6.16.2):
    - AppsFlyerFramework (= 6.16.2)
    - React
  - react-native-biometrics (3.0.1):
    - React-Core
  - react-native-config (1.5.5):
    - react-native-config/App (= 1.5.5)
  - react-native-config/App (1.5.5):
    - React-Core
  - react-native-contacts (7.0.8):
    - React-Core
  - react-native-document-picker (9.3.1):
    - React-Core
  - react-native-encrypted-storage (4.0.3):
    - React-Core
  - react-native-geolocation (3.4.0):
    - React-Core
  - react-native-get-random-values (1.11.0):
    - React-Core
  - react-native-heap (0.22.8):
    - Heap (~> 9.0)
    - React-Core
    - React-CoreModules
  - react-native-mobilesdk-module (1.35.1):
    - IdensicMobileSDK (= 1.35.1)
    - React-Core
  - react-native-netinfo (9.5.0):
    - React-Core
  - react-native-print (0.11.0):
    - React-Core
  - react-native-safe-area-context (4.14.1):
    - React-Core
  - react-native-splash-screen (3.3.0):
    - React-Core
  - react-native-tracking-transparency (0.1.2):
    - React
  - react-native-view-shot (3.8.0):
    - React-Core
  - react-native-webview (11.26.1):
    - React-Core
  - React-perflogger (0.70.15)
  - React-RCTActionSheet (0.70.15):
    - React-Core/RCTActionSheetHeaders (= 0.70.15)
  - React-RCTAnimation (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.15)
    - React-Codegen (= 0.70.15)
    - React-Core/RCTAnimationHeaders (= 0.70.15)
    - React-jsi (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-RCTBlob (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.70.15)
    - React-Core/RCTBlobHeaders (= 0.70.15)
    - React-Core/RCTWebSocket (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-RCTNetwork (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-RCTImage (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.15)
    - React-Codegen (= 0.70.15)
    - React-Core/RCTImageHeaders (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-RCTNetwork (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-RCTLinking (0.70.15):
    - React-Codegen (= 0.70.15)
    - React-Core/RCTLinkingHeaders (= 0.70.15)
    - React-jsi (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-RCTNetwork (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.15)
    - React-Codegen (= 0.70.15)
    - React-Core/RCTNetworkHeaders (= 0.70.15)
    - React-jsi (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-RCTSettings (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.15)
    - React-Codegen (= 0.70.15)
    - React-Core/RCTSettingsHeaders (= 0.70.15)
    - React-jsi (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-RCTText (0.70.15):
    - React-Core/RCTTextHeaders (= 0.70.15)
  - React-RCTVibration (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.70.15)
    - React-Core/RCTVibrationHeaders (= 0.70.15)
    - React-jsi (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-runtimeexecutor (0.70.15):
    - React-jsi (= 0.70.15)
  - ReactCommon/turbomodule/core (0.70.15):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-bridging (= 0.70.15)
    - React-callinvoker (= 0.70.15)
    - React-Core (= 0.70.15)
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-logger (= 0.70.15)
    - React-perflogger (= 0.70.15)
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNCAsyncStorage (1.24.0):
    - React-Core
  - RNCClipboard (1.16.2):
    - React-Core
  - RNCMaskedView (0.1.11):
    - React
  - RNCPicker (2.11.0):
    - React-Core
  - RNFastImage (8.6.3):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFBAnalytics (21.14.0):
    - Firebase/Analytics (= 11.11.0)
    - React-Core
    - RNFBApp
  - RNFBApp (21.14.0):
    - Firebase/CoreOnly (= 11.11.0)
    - React-Core
  - RNFBCrashlytics (21.14.0):
    - Firebase/Crashlytics (= 11.11.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNFBDynamicLinks (21.14.0):
    - Firebase/DynamicLinks (= 11.11.0)
    - GoogleUtilities/AppDelegateSwizzler
    - React-Core
    - RNFBApp
  - RNFBMessaging (21.14.0):
    - Firebase/Messaging (= 11.11.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.16.0):
    - React-Core
  - RNLocalize (1.4.1):
    - React
  - RNPermissions (5.3.0):
    - React-Core
  - RNRate (1.2.12):
    - React-Core
  - RNReanimated (2.17.0):
    - DoubleConversion
    - FBLazyVector
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.25.0):
    - React-Core
    - React-RCTImage
  - RNSentry (6.5.0):
    - React-Core
    - Sentry/HybridSDK (= 8.43.0)
  - RNShare (12.0.11):
    - React-Core
  - RNSVG (12.5.1):
    - React-Core
  - RNVectorIcons (10.2.0):
    - React-Core
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - Sentry/HybridSDK (8.43.0)
  - toolbar-android (0.2.1):
    - React
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - customerio-reactnative (from `../node_modules/customerio-reactnative`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Firebase/Analytics
  - Firebase/Messaging
  - FirebaseCore
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - "intercom-react-native (from `../node_modules/@intercom/intercom-react-native`)"
  - lottie-react-native (from `../node_modules/lottie-react-native`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-bridging (from `../node_modules/react-native/ReactCommon`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-appsflyer (from `../node_modules/react-native-appsflyer`)
  - react-native-biometrics (from `../node_modules/react-native-biometrics`)
  - react-native-config (from `../node_modules/react-native-config`)
  - react-native-contacts (from `../node_modules/react-native-contacts`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - react-native-encrypted-storage (from `../node_modules/react-native-encrypted-storage`)
  - "react-native-geolocation (from `../node_modules/@react-native-community/geolocation`)"
  - react-native-get-random-values (from `../node_modules/react-native-get-random-values`)
  - "react-native-heap (from `../node_modules/@heap/react-native-heap`)"
  - "react-native-mobilesdk-module (from `../node_modules/@sumsub/react-native-mobilesdk-module`)"
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-print (from `../node_modules/react-native-print`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - react-native-tracking-transparency (from `../node_modules/react-native-tracking-transparency`)
  - react-native-view-shot (from `../node_modules/react-native-view-shot`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCMaskedView (from `../node_modules/@react-native-community/masked-view`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBAnalytics (from `../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBCrashlytics (from `../node_modules/@react-native-firebase/crashlytics`)"
  - "RNFBDynamicLinks (from `../node_modules/@react-native-firebase/dynamic-links`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNLocalize (from `../node_modules/react-native-localize`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNRate (from `../node_modules/react-native-rate`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - "RNSentry (from `../node_modules/@sentry/react-native/RNSentry.podspec`)"
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - "toolbar-android (from `../node_modules/@react-native-community/toolbar-android`)"
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  https://github.com/SumSubstance/Specs.git:
    - IdensicMobileSDK
  trunk:
    - AppsFlyerFramework
    - CustomerIO
    - CustomerIOCommon
    - CustomerIOMessagingInApp
    - CustomerIOMessagingPush
    - CustomerIOTracking
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseDynamicLinks
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - fmt
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - Heap
    - Intercom
    - libwebp
    - lottie-ios
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - SDWebImage
    - SDWebImageWebPCoder
    - Sentry

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  customerio-reactnative:
    :path: "../node_modules/customerio-reactnative"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  intercom-react-native:
    :path: "../node_modules/@intercom/intercom-react-native"
  lottie-react-native:
    :path: "../node_modules/lottie-react-native"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-bridging:
    :path: "../node_modules/react-native/ReactCommon"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-appsflyer:
    :path: "../node_modules/react-native-appsflyer"
  react-native-biometrics:
    :path: "../node_modules/react-native-biometrics"
  react-native-config:
    :path: "../node_modules/react-native-config"
  react-native-contacts:
    :path: "../node_modules/react-native-contacts"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-encrypted-storage:
    :path: "../node_modules/react-native-encrypted-storage"
  react-native-geolocation:
    :path: "../node_modules/@react-native-community/geolocation"
  react-native-get-random-values:
    :path: "../node_modules/react-native-get-random-values"
  react-native-heap:
    :path: "../node_modules/@heap/react-native-heap"
  react-native-mobilesdk-module:
    :path: "../node_modules/@sumsub/react-native-mobilesdk-module"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-print:
    :path: "../node_modules/react-native-print"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  react-native-tracking-transparency:
    :path: "../node_modules/react-native-tracking-transparency"
  react-native-view-shot:
    :path: "../node_modules/react-native-view-shot"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNCMaskedView:
    :path: "../node_modules/@react-native-community/masked-view"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBAnalytics:
    :path: "../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBCrashlytics:
    :path: "../node_modules/@react-native-firebase/crashlytics"
  RNFBDynamicLinks:
    :path: "../node_modules/@react-native-firebase/dynamic-links"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNLocalize:
    :path: "../node_modules/react-native-localize"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNRate:
    :path: "../node_modules/react-native-rate"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSentry:
    :path: "../node_modules/@sentry/react-native/RNSentry.podspec"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  toolbar-android:
    :path: "../node_modules/@react-native-community/toolbar-android"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  AppsFlyerFramework: fe5303bffcdfd941d5f570c2d21eaaea982e7bdc
  boost: 9fa78656d705f55b1220151d997e57e2a3f2cde0
  BVLinearGradient: cb006ba232a1f3e4f341bb62c42d1098c284da70
  CustomerIO: 89249909bce5f1ecba43721bda800dea79341085
  customerio-reactnative: 703a2bf1f927a2517c9a13c849cc6ad0a9c6c83d
  CustomerIOCommon: 8e3a003b3daf5096d56f193140a0eadf1fdce2b4
  CustomerIOMessagingInApp: b160de4b0bed0a5b511ee20830acc695512d09af
  CustomerIOMessagingPush: c49222a62f9885c18a6779fe50458ad54d8d7cd5
  CustomerIOTracking: d062619ac968b5b2c2d413d1c8586e25747e161f
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: 9cf707e46f9bd90816b7c91b2c1c8b8a2f549527
  FBReactNativeSpec: 5ce1ea97a4309ded19af6c21f13f63ee3cabfed2
  Firebase: 6a8f201c61eda24e98f1ce2b44b1b9c2caf525cc
  FirebaseAnalytics: acfa848bf81e1a4dbf60ef1f0eddd7328fe6673e
  FirebaseCore: 2321536f9c423b1f857e047a82b8a42abc6d9e2c
  FirebaseCoreExtension: 3a64994969dd05f4bcb7e6896c654eded238e75b
  FirebaseCoreInternal: 31ee350d87b30a9349e907f84bf49ef8e6791e5a
  FirebaseCrashlytics: 5058c465e10782f54337b394c37254e0595174e9
  FirebaseDynamicLinks: 583591e36d7659ba1cd4c1ac6ea0825faf45f161
  FirebaseInstallations: 781e0e37aa0e1c92b44d00e739aba79ad31b2dba
  FirebaseMessaging: c7be9357fd8ba33bc45b9a6c3cdff0b466e1e2a4
  FirebaseRemoteConfigInterop: 1c6135e8a094cc6368949f5faeeca7ee8948b8aa
  FirebaseSessions: f5c6bfeb66a7202deaf33352017bb6365e395820
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  GoogleAppMeasurement: 8a82b93a6400c8e6551c0bcd66a9177f2e067aed
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  Heap: 5cf4e3f4b2fdfdae57a619fad4cafd5a654ccc97
  IdensicMobileSDK: 51fc58be474b021907077e1bea350c03454d6cee
  Intercom: 9bffad96cc66d42cfd971b37807d6dc49d5bb914
  intercom-react-native: fc9e5c03b07f7020bf9fe79550a9239821f27067
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  lottie-ios: a881093fab623c467d3bce374367755c272bdd59
  lottie-react-native: c5da8db276ae5ad7d816255e383575faa4c634ad
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RCT-Folly: fd2673f5bfadb2a8173799b6965b892a67ae31d9
  RCTRequired: 2a96ea90ffddd10cc43115bd93803692e09b5d9a
  RCTTypeSafety: 02c99baddcf0b3393bf58e6d9b792e83a37716b4
  React: 45e3210df90d25ec6da7fc286943b377b63a92ec
  React-bridging: e3a18265bbd59003562e29429985e0923a5b6286
  React-callinvoker: 344ff205a470c3c99b4daf0a2dff9bc29045d6c5
  React-Codegen: 2b1765b0e1a38b8b3601178ca27c1e9216e81632
  React-Core: b6db729ece149a8eba28593b6b08e24558f9a303
  React-CoreModules: 4eb535b1650b718cb3680767c1b9a1cacf649cbc
  React-cxxreact: cc904c9a621922244d12fe38a85fc3cdab619fa7
  React-jsi: ac97489adfc24154e3eb48286d39a0cdc67dfdb2
  React-jsiexecutor: a19d406388d34ec2f77e5f9b0976179bb71f38b5
  React-jsinspector: bfedded1f4f562d29c2d4a8bb795c9a150a739e4
  React-logger: 56142736969724ed81d3ea042a5ad2ef59bb2725
  react-native-appsflyer: 4ac00a02833e9e81cc08da668728817fdc2a94e0
  react-native-biometrics: 43ed5b828646a7862dbc7945556446be00798e7d
  react-native-config: 644074ab88db883fcfaa584f03520ec29589d7df
  react-native-contacts: f551920b74ebfc5f7f6df307495f085e504a4369
  react-native-document-picker: e824b468df16d1f4d560f6c53dbabe644043fcd4
  react-native-encrypted-storage: 569d114e329b1c2c2d9f8c84bcdbe4478dda2258
  react-native-geolocation: 17b040465ff06d914f733d7f61356cbdae409a0d
  react-native-get-random-values: d16467cf726c618e9c7a8c3c39c31faa2244bbba
  react-native-heap: 08ed2bee0e032aaf633fa33da924d260cfd6bd1e
  react-native-mobilesdk-module: a8e1ee3970dbd9593dc1eee1df2b9f2acb99dcec
  react-native-netinfo: 26560022f28c06d8ef00a9ff1e03beefbbb60c2d
  react-native-print: 692e8782bc89872ea01738c6759631388e77358c
  react-native-safe-area-context: 758e894ca5a9bd1868d2a9cfbca7326a2b6bf9dc
  react-native-splash-screen: 95994222cc95c236bd3cdc59fe45ed5f27969594
  react-native-tracking-transparency: 15eb319f2b982070eb9831582af27d87badfa624
  react-native-view-shot: d1a701eb0719c6dccbd20b4bb43b1069f304cb70
  react-native-webview: ec195db71ebf55705d7b46a7da5f1b1746bb7efd
  React-perflogger: 010e98d3335e5185a8f7496babca50d82a042e84
  React-RCTActionSheet: 0f585d684b540a5bbfc62b0a1fbc5292cff2aefc
  React-RCTAnimation: eb0e5b020333f9cc652d85f27a47086fbf56fffd
  React-RCTBlob: 4af18ad2a64515c3ede9b829e8532f1508e00894
  React-RCTImage: 08787efa5378ad0e7344943eed1b898619cf956a
  React-RCTLinking: ea7ec6fbfdb04df7895c39f15f0e7479acc43bca
  React-RCTNetwork: 926b436b6afada9905d969a8e3713cf204905a00
  React-RCTSettings: cc083c9b6e126b7e6ea1128e64837d8b78ceb219
  React-RCTText: c36ddf2bda5131b325e1c2763700f0a63a963e1d
  React-RCTVibration: 12a2a859fa22368d2fc3ca7594504fd130b91a18
  React-runtimeexecutor: 04332dda2f2335ea4ddaf9255de069d3269f4e8b
  ReactCommon: 71bb0be95c51fea8a5598980b6abb9517c2cfb03
  rn-fetch-blob: 25612b6d6f6e980c6f17ed98ba2f58f5696a51ca
  RNCAsyncStorage: b6410dead2732b5c72a7fdb1ecb5651bbcf4674b
  RNCClipboard: e1d17c9d093d8129ef50b39b63a17a0e8ccd0ade
  RNCMaskedView: 4c5ee1c8667d56077246cc6d1977f77393923560
  RNCPicker: 124b4fb5859ba1a3fd53a91e16d1e7a0fc016e59
  RNFastImage: 462a183c4b0b6b26fdfd639e1ed6ba37536c3b87
  RNFBAnalytics: 03c83ba4617a3754c99e66267983efcc908932a9
  RNFBApp: a448037d2df74af9d374a0b765be12ff1e844dc0
  RNFBCrashlytics: c3bb5533f9957eddc88f3ea383583309a4ce9f89
  RNFBDynamicLinks: 741b5407a43c906801fe1fc4095d86456eba9892
  RNFBMessaging: 0f0498a95c605e3afcf13ac5f349d0b201ea65f6
  RNFS: 89de7d7f4c0f6bafa05343c578f61118c8282ed8
  RNGestureHandler: ebcfc6f109e042d53c0aaede2dc7ad836fb9534c
  RNLocalize: 81be1926be3e6b01aa33568579d6a77027383200
  RNPermissions: 86494d86a4335fb78777ec779dd661b7d447c35f
  RNRate: 7641919330e0d6688ad885a985b4bd697ed7d14c
  RNReanimated: 32ed5a11b7feae1cd35c005cda69822a3a2efdd4
  RNScreens: 4f2dbf88d1ba333a4455a29833e30510b21f14c5
  RNSentry: 6f1cd8912843519c849e4cdb6344e3f27fb959c2
  RNShare: 0d5d569d2d0bd94e8aa5bd3f19fa9169007e7f6c
  RNSVG: d7cb8bd34550cf4c4fc7edd7ac627905e2b71f3f
  RNVectorIcons: e944f84b90781998c847ee42ad411a1bf8825ac8
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  Sentry: 532b281a53b1b45a523fd592f608956fb36e577c
  toolbar-android: c426ed5bd3dcccfed20fd79533efc0d1ae0ef018
  Yoga: d6134eb3d6e3675afc1d6d65ccb3169b60e21980

PODFILE CHECKSUM: b26644102f9e173ea3e2bfa8dce09551aa780976

COCOAPODS: 1.16.2
