PODS:
  - AppsFlyerFramework (6.16.2):
    - AppsFlyerFramework/Main (= 6.16.2)
  - AppsFlyerFramework/Main (6.16.2)
  - boost (1.76.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - customerio-reactnative (3.5.0):
    - customerio-reactnative/nopush (= 3.5.0)
    - CustomerIO/MessagingInApp (= 2.12.1)
    - CustomerIO/Tracking (= 2.12.1)
    - React-Core
  - customerio-reactnative/nopush (3.5.0):
    - CustomerIO/MessagingInApp (= 2.12.1)
    - CustomerIO/MessagingPush (= 2.12.1)
    - CustomerIO/Tracking (= 2.12.1)
    - React-Core
  - CustomerIO/MessagingInApp (2.12.1):
    - CustomerIOMessagingInApp (= 2.12.1)
  - CustomerIO/MessagingPush (2.12.1):
    - CustomerIOMessagingPush (= 2.12.1)
  - CustomerIO/Tracking (2.12.1):
    - CustomerIOTracking (= 2.12.1)
  - CustomerIOCommon (2.12.1)
  - CustomerIOMessagingInApp (2.12.1):
    - CustomerIOTracking (= 2.12.1)
  - CustomerIOMessagingPush (2.12.1):
    - CustomerIOTracking (= 2.12.1)
  - CustomerIOTracking (2.12.1):
    - CustomerIOCommon (= 2.12.1)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.70.15)
  - FBReactNativeSpec (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.70.15)
    - RCTTypeSafety (= 0.70.15)
    - React-Core (= 0.70.15)
    - React-jsi (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - Firebase/Analytics (11.11.0):
    - Firebase/Core
  - Firebase/Core (11.11.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.11.0)
  - Firebase/CoreOnly (11.11.0):
    - FirebaseCore (~> 11.11.0)
  - Firebase/Crashlytics (11.11.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.11.0)
  - Firebase/DynamicLinks (11.11.0):
    - Firebase/CoreOnly
    - FirebaseDynamicLinks (~> 11.11.0)
  - Firebase/Messaging (11.11.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.11.0)
  - FirebaseAnalytics (11.11.0):
    - FirebaseAnalytics/AdIdSupport (= 11.11.0)
    - FirebaseCore (~> 11.11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.11.0):
    - FirebaseCore (~> 11.11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.11.0):
    - FirebaseCoreInternal (~> 11.11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.11.0):
    - FirebaseCore (~> 11.11.0)
  - FirebaseCoreInternal (11.11.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.11.0):
    - FirebaseCore (~> 11.11.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseDynamicLinks (11.11.0):
    - FirebaseCore (~> 11.11.0)
  - FirebaseInstallations (11.11.0):
    - FirebaseCore (~> 11.11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.11.0):
    - FirebaseCore (~> 11.11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfigInterop (11.14.0)
  - FirebaseSessions (11.11.0):
    - FirebaseCore (~> 11.11.0)
    - FirebaseCoreExtension (~> 11.11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - fmt (6.2.1)
  - glog (0.3.5)
  - GoogleAppMeasurement (11.11.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.11.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.11.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - Heap (9.1.0)
  - IdensicMobileSDK (1.35.1):
    - IdensicMobileSDK/Default (= 1.35.1)
  - IdensicMobileSDK/Core (1.35.1)
  - IdensicMobileSDK/Default (1.35.1):
    - IdensicMobileSDK/Core
  - Intercom (15.2.3)
  - intercom-react-native (5.3.1):
    - Intercom (~> 15.2.0)
    - React-Core
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - lottie-ios (4.5.0)
  - lottie-react-native (7.2.2):
    - lottie-ios (= 4.5.0)
    - React-Core
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.70.15)
  - RCTTypeSafety (0.70.15):
    - FBLazyVector (= 0.70.15)
    - RCTRequired (= 0.70.15)
    - React-Core (= 0.70.15)
  - React (0.70.15):
    - React-Core (= 0.70.15)
    - React-Core/DevSupport (= 0.70.15)
    - React-Core/RCTWebSocket (= 0.70.15)
    - React-RCTActionSheet (= 0.70.15)
    - React-RCTAnimation (= 0.70.15)
    - React-RCTBlob (= 0.70.15)
    - React-RCTImage (= 0.70.15)
    - React-RCTLinking (= 0.70.15)
    - React-RCTNetwork (= 0.70.15)
    - React-RCTSettings (= 0.70.15)
    - React-RCTText (= 0.70.15)
    - React-RCTVibration (= 0.70.15)
  - React-bridging (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - React-jsi (= 0.70.15)
  - React-callinvoker (0.70.15)
  - React-Codegen (0.70.15):
    - FBReactNativeSpec (= 0.70.15)
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.70.15)
    - RCTTypeSafety (= 0.70.15)
    - React-Core (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-Core (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.70.15)
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/CoreModulesHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/Default (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/DevSupport (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.70.15)
    - React-Core/RCTWebSocket (= 0.70.15)
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-jsinspector (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTBlobHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTImageHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTTextHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-Core/RCTWebSocket (0.70.15):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.70.15)
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsiexecutor (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - Yoga
  - React-CoreModules (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.15)
    - React-Codegen (= 0.70.15)
    - React-Core/CoreModulesHeaders (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-RCTImage (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-cxxreact (0.70.15):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-jsinspector (= 0.70.15)
    - React-logger (= 0.70.15)
    - React-perflogger (= 0.70.15)
    - React-runtimeexecutor (= 0.70.15)
  - React-jsi (0.70.15):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-jsi/Default (= 0.70.15)
  - React-jsi/Default (0.70.15):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.70.15):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-perflogger (= 0.70.15)
  - React-jsinspector (0.70.15)
  - React-logger (0.70.15):
    - glog
  - react-native-appsflyer (6.16.2):
    - AppsFlyerFramework (= 6.16.2)
    - React
  - react-native-biometrics (3.0.1):
    - React-Core
  - react-native-config (1.5.5):
    - react-native-config/App (= 1.5.5)
  - react-native-config/App (1.5.5):
    - React-Core
  - react-native-contacts (7.0.8):
    - React-Core
  - react-native-document-picker (9.3.1):
    - React-Core
  - react-native-encrypted-storage (4.0.3):
    - React-Core
  - react-native-geolocation (3.4.0):
    - React-Core
  - react-native-get-random-values (1.11.0):
    - React-Core
  - react-native-heap (0.22.8):
    - Heap (~> 9.0)
    - React-Core
    - React-CoreModules
  - react-native-mobilesdk-module (1.35.1):
    - IdensicMobileSDK (= 1.35.1)
    - React-Core
  - react-native-netinfo (9.5.0):
    - React-Core
  - react-native-print (0.11.0):
    - React-Core
  - react-native-safe-area-context (4.14.1):
    - React-Core
  - react-native-splash-screen (3.3.0):
    - React-Core
  - react-native-tracking-transparency (0.1.2):
    - React
  - react-native-view-shot (3.8.0):
    - React-Core
  - react-native-webview (11.26.1):
    - React-Core
  - React-perflogger (0.70.15)
  - React-RCTActionSheet (0.70.15):
    - React-Core/RCTActionSheetHeaders (= 0.70.15)
  - React-RCTAnimation (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.15)
    - React-Codegen (= 0.70.15)
    - React-Core/RCTAnimationHeaders (= 0.70.15)
    - React-jsi (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-RCTBlob (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.70.15)
    - React-Core/RCTBlobHeaders (= 0.70.15)
    - React-Core/RCTWebSocket (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-RCTNetwork (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-RCTImage (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.15)
    - React-Codegen (= 0.70.15)
    - React-Core/RCTImageHeaders (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-RCTNetwork (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-RCTLinking (0.70.15):
    - React-Codegen (= 0.70.15)
    - React-Core/RCTLinkingHeaders (= 0.70.15)
    - React-jsi (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-RCTNetwork (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.15)
    - React-Codegen (= 0.70.15)
    - React-Core/RCTNetworkHeaders (= 0.70.15)
    - React-jsi (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-RCTSettings (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.15)
    - React-Codegen (= 0.70.15)
    - React-Core/RCTSettingsHeaders (= 0.70.15)
    - React-jsi (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-RCTText (0.70.15):
    - React-Core/RCTTextHeaders (= 0.70.15)
  - React-RCTVibration (0.70.15):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.70.15)
    - React-Core/RCTVibrationHeaders (= 0.70.15)
    - React-jsi (= 0.70.15)
    - ReactCommon/turbomodule/core (= 0.70.15)
  - React-runtimeexecutor (0.70.15):
    - React-jsi (= 0.70.15)
  - ReactCommon/turbomodule/core (0.70.15):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-bridging (= 0.70.15)
    - React-callinvoker (= 0.70.15)
    - React-Core (= 0.70.15)
    - React-cxxreact (= 0.70.15)
    - React-jsi (= 0.70.15)
    - React-logger (= 0.70.15)
    - React-perflogger (= 0.70.15)
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNCAsyncStorage (1.24.0):
    - React-Core
  - RNCClipboard (1.16.2):
    - React-Core
  - RNCMaskedView (0.1.11):
    - React
  - RNCPicker (2.11.0):
    - React-Core
  - RNFastImage (8.6.3):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFBAnalytics (21.14.0):
    - Firebase/Analytics (= 11.11.0)
    - React-Core
    - RNFBApp
  - RNFBApp (21.14.0):
    - Firebase/CoreOnly (= 11.11.0)
    - React-Core
  - RNFBCrashlytics (21.14.0):
    - Firebase/Crashlytics (= 11.11.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNFBDynamicLinks (21.14.0):
    - Firebase/DynamicLinks (= 11.11.0)
    - GoogleUtilities/AppDelegateSwizzler
    - React-Core
    - RNFBApp
  - RNFBMessaging (21.14.0):
    - Firebase/Messaging (= 11.11.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.16.0):
    - React-Core
  - RNLocalize (1.4.1):
    - React
  - RNPermissions (5.3.0):
    - React-Core
  - RNRate (1.2.12):
    - React-Core
  - RNReanimated (2.17.0):
    - DoubleConversion
    - FBLazyVector
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.25.0):
    - React-Core
    - React-RCTImage
  - RNSentry (6.5.0):
    - React-Core
    - Sentry/HybridSDK (= 8.43.0)
  - RNShare (12.0.11):
    - React-Core
  - RNSVG (12.5.1):
    - React-Core
  - RNVectorIcons (10.2.0):
    - React-Core
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - Sentry/HybridSDK (8.43.0)
  - toolbar-android (0.2.1):
    - React
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - customerio-reactnative (from `../node_modules/customerio-reactnative`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Firebase/Analytics
  - Firebase/Messaging
  - FirebaseCore
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - "intercom-react-native (from `../node_modules/@intercom/intercom-react-native`)"
  - lottie-react-native (from `../node_modules/lottie-react-native`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-bridging (from `../node_modules/react-native/ReactCommon`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-appsflyer (from `../node_modules/react-native-appsflyer`)
  - react-native-biometrics (from `../node_modules/react-native-biometrics`)
  - react-native-config (from `../node_modules/react-native-config`)
  - react-native-contacts (from `../node_modules/react-native-contacts`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - react-native-encrypted-storage (from `../node_modules/react-native-encrypted-storage`)
  - "react-native-geolocation (from `../node_modules/@react-native-community/geolocation`)"
  - react-native-get-random-values (from `../node_modules/react-native-get-random-values`)
  - "react-native-heap (from `../node_modules/@heap/react-native-heap`)"
  - "react-native-mobilesdk-module (from `../node_modules/@sumsub/react-native-mobilesdk-module`)"
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-print (from `../node_modules/react-native-print`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - react-native-tracking-transparency (from `../node_modules/react-native-tracking-transparency`)
  - react-native-view-shot (from `../node_modules/react-native-view-shot`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCMaskedView (from `../node_modules/@react-native-community/masked-view`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBAnalytics (from `../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBCrashlytics (from `../node_modules/@react-native-firebase/crashlytics`)"
  - "RNFBDynamicLinks (from `../node_modules/@react-native-firebase/dynamic-links`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNLocalize (from `../node_modules/react-native-localize`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNRate (from `../node_modules/react-native-rate`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - "RNSentry (from `../node_modules/@sentry/react-native/RNSentry.podspec`)"
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - "toolbar-android (from `../node_modules/@react-native-community/toolbar-android`)"
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  https://github.com/SumSubstance/Specs.git:
    - IdensicMobileSDK
  trunk:
    - AppsFlyerFramework
    - CustomerIO
    - CustomerIOCommon
    - CustomerIOMessagingInApp
    - CustomerIOMessagingPush
    - CustomerIOTracking
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseDynamicLinks
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - fmt
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - Heap
    - Intercom
    - libwebp
    - lottie-ios
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - SDWebImage
    - SDWebImageWebPCoder
    - Sentry

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  customerio-reactnative:
    :path: "../node_modules/customerio-reactnative"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  intercom-react-native:
    :path: "../node_modules/@intercom/intercom-react-native"
  lottie-react-native:
    :path: "../node_modules/lottie-react-native"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-bridging:
    :path: "../node_modules/react-native/ReactCommon"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-appsflyer:
    :path: "../node_modules/react-native-appsflyer"
  react-native-biometrics:
    :path: "../node_modules/react-native-biometrics"
  react-native-config:
    :path: "../node_modules/react-native-config"
  react-native-contacts:
    :path: "../node_modules/react-native-contacts"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-encrypted-storage:
    :path: "../node_modules/react-native-encrypted-storage"
  react-native-geolocation:
    :path: "../node_modules/@react-native-community/geolocation"
  react-native-get-random-values:
    :path: "../node_modules/react-native-get-random-values"
  react-native-heap:
    :path: "../node_modules/@heap/react-native-heap"
  react-native-mobilesdk-module:
    :path: "../node_modules/@sumsub/react-native-mobilesdk-module"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-print:
    :path: "../node_modules/react-native-print"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  react-native-tracking-transparency:
    :path: "../node_modules/react-native-tracking-transparency"
  react-native-view-shot:
    :path: "../node_modules/react-native-view-shot"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNCMaskedView:
    :path: "../node_modules/@react-native-community/masked-view"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBAnalytics:
    :path: "../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBCrashlytics:
    :path: "../node_modules/@react-native-firebase/crashlytics"
  RNFBDynamicLinks:
    :path: "../node_modules/@react-native-firebase/dynamic-links"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNLocalize:
    :path: "../node_modules/react-native-localize"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNRate:
    :path: "../node_modules/react-native-rate"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSentry:
    :path: "../node_modules/@sentry/react-native/RNSentry.podspec"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  toolbar-android:
    :path: "../node_modules/@react-native-community/toolbar-android"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  AppsFlyerFramework: fe5303bffcdfd941d5f570c2d21eaaea982e7bdc
  boost: 9fa78656d705f55b1220151d997e57e2a3f2cde0
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  CustomerIO: 89249909bce5f1ecba43721bda800dea79341085
  customerio-reactnative: fbfde615aa7fb88ef48e1e73ded1c95c92bd7fd5
  CustomerIOCommon: 8e3a003b3daf5096d56f193140a0eadf1fdce2b4
  CustomerIOMessagingInApp: b160de4b0bed0a5b511ee20830acc695512d09af
  CustomerIOMessagingPush: c49222a62f9885c18a6779fe50458ad54d8d7cd5
  CustomerIOTracking: d062619ac968b5b2c2d413d1c8586e25747e161f
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: 9cf707e46f9bd90816b7c91b2c1c8b8a2f549527
  FBReactNativeSpec: 5ce1ea97a4309ded19af6c21f13f63ee3cabfed2
  Firebase: 6a8f201c61eda24e98f1ce2b44b1b9c2caf525cc
  FirebaseAnalytics: acfa848bf81e1a4dbf60ef1f0eddd7328fe6673e
  FirebaseCore: 2321536f9c423b1f857e047a82b8a42abc6d9e2c
  FirebaseCoreExtension: 3a64994969dd05f4bcb7e6896c654eded238e75b
  FirebaseCoreInternal: 31ee350d87b30a9349e907f84bf49ef8e6791e5a
  FirebaseCrashlytics: 5058c465e10782f54337b394c37254e0595174e9
  FirebaseDynamicLinks: 583591e36d7659ba1cd4c1ac6ea0825faf45f161
  FirebaseInstallations: 781e0e37aa0e1c92b44d00e739aba79ad31b2dba
  FirebaseMessaging: c7be9357fd8ba33bc45b9a6c3cdff0b466e1e2a4
  FirebaseRemoteConfigInterop: 7b74ceaa54e28863ed17fa39da8951692725eced
  FirebaseSessions: f5c6bfeb66a7202deaf33352017bb6365e395820
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  GoogleAppMeasurement: 8a82b93a6400c8e6551c0bcd66a9177f2e067aed
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  Heap: 5cf4e3f4b2fdfdae57a619fad4cafd5a654ccc97
  IdensicMobileSDK: 51fc58be474b021907077e1bea350c03454d6cee
  Intercom: 9bffad96cc66d42cfd971b37807d6dc49d5bb914
  intercom-react-native: d74faad59c5f73834a6ea32bed1a45e459d5ade6
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  lottie-ios: a881093fab623c467d3bce374367755c272bdd59
  lottie-react-native: ba3b9bbac6170e729cc4f049bdab4200f4c9b6dc
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RCT-Folly: fd2673f5bfadb2a8173799b6965b892a67ae31d9
  RCTRequired: 2a96ea90ffddd10cc43115bd93803692e09b5d9a
  RCTTypeSafety: 02c99baddcf0b3393bf58e6d9b792e83a37716b4
  React: 45e3210df90d25ec6da7fc286943b377b63a92ec
  React-bridging: e3a18265bbd59003562e29429985e0923a5b6286
  React-callinvoker: 344ff205a470c3c99b4daf0a2dff9bc29045d6c5
  React-Codegen: 2b1765b0e1a38b8b3601178ca27c1e9216e81632
  React-Core: 93efb81ef85fafee7f83f7ef6ecf546b2e1ee2c0
  React-CoreModules: 4eb535b1650b718cb3680767c1b9a1cacf649cbc
  React-cxxreact: 283248db3101de28d6cf0fe438a2dc95537ee472
  React-jsi: 560bdf0bc36d5c137ac962c0eb4b60b50c304d77
  React-jsiexecutor: d2eebcd5a432f90be3baa5d1309f47d05478ea61
  React-jsinspector: bfedded1f4f562d29c2d4a8bb795c9a150a739e4
  React-logger: 31f198387a04172be49fe38e41a082560a81aeeb
  react-native-appsflyer: d52a08a5e687e4c2aca78519b0e258e1a82fad0a
  react-native-biometrics: 352e5a794bfffc46a0c86725ea7dc62deb085bdc
  react-native-config: 3367df9c1f25bb96197007ec531c7087ed4554c3
  react-native-contacts: cb4f3b823550adbce0802901b9e63bf1526ec0af
  react-native-document-picker: 940269e45632f48eb5e81543381ffd15a970d849
  react-native-encrypted-storage: db300a3f2f0aba1e818417c1c0a6be549038deb7
  react-native-geolocation: e535ecd762e6d789ecb00fb6167a1febd667b0e9
  react-native-get-random-values: 21325b2244dfa6b58878f51f9aa42821e7ba3d06
  react-native-heap: e662b86f523da34339fcc6102d96263bb1577cbc
  react-native-mobilesdk-module: bcf0225a82fa748eb72305eb523e36a105fe16c4
  react-native-netinfo: 48c5f79a84fbc3ba1d28a8b0d04adeda72885fa8
  react-native-print: f704aef52d931bfce6d1d84351dbb5232d7ecb89
  react-native-safe-area-context: 141eca0fd4e4191288dfc8b96a7c7e1c2983447a
  react-native-splash-screen: 4312f786b13a81b5169ef346d76d33bc0c6dc457
  react-native-tracking-transparency: 25ff1ff866e338c137c818bdec20526bb05ffcc1
  react-native-view-shot: 6b7ed61d77d88580fed10954d45fad0eb2d47688
  react-native-webview: 9f111dfbcfc826084d6c507f569e5e03342ee1c1
  React-perflogger: 010e98d3335e5185a8f7496babca50d82a042e84
  React-RCTActionSheet: 0f585d684b540a5bbfc62b0a1fbc5292cff2aefc
  React-RCTAnimation: eb0e5b020333f9cc652d85f27a47086fbf56fffd
  React-RCTBlob: 4af18ad2a64515c3ede9b829e8532f1508e00894
  React-RCTImage: 08787efa5378ad0e7344943eed1b898619cf956a
  React-RCTLinking: ea7ec6fbfdb04df7895c39f15f0e7479acc43bca
  React-RCTNetwork: 926b436b6afada9905d969a8e3713cf204905a00
  React-RCTSettings: cc083c9b6e126b7e6ea1128e64837d8b78ceb219
  React-RCTText: c36ddf2bda5131b325e1c2763700f0a63a963e1d
  React-RCTVibration: 12a2a859fa22368d2fc3ca7594504fd130b91a18
  React-runtimeexecutor: 04332dda2f2335ea4ddaf9255de069d3269f4e8b
  ReactCommon: 200471e0841cf2f7cde1fa2ef3d3c199ed970c07
  rn-fetch-blob: f065bb7ab7fb48dd002629f8bdcb0336602d3cba
  RNCAsyncStorage: ec53e44dc3e75b44aa2a9f37618a49c3bc080a7a
  RNCClipboard: 4411a880506f11fbcbdc03b5093eb8ffb246d900
  RNCMaskedView: 0e1bc4bfa8365eba5fbbb71e07fbdc0555249489
  RNCPicker: 4f6d5e8d107064be32dfc8f1bcb5a3741135db0b
  RNFastImage: 5c9c9fed9c076e521b3f509fe79e790418a544e8
  RNFBAnalytics: caec446c723d33cfdcf75aab53fa1287e499b2d0
  RNFBApp: fda4a8b08fe31bea8492808106aa638d1bb595b7
  RNFBCrashlytics: 02234987b4cffb39119af52253bf3d70acec0818
  RNFBDynamicLinks: 3e07d1af77e17bd19bc44aa4f75879a4be98554b
  RNFBMessaging: 099972ab397d61815f32610514b0573d1db2b1e1
  RNFS: 4ac0f0ea233904cb798630b3c077808c06931688
  RNGestureHandler: 998887f1b2c6098ffa2506402087c0e8ef5d69a1
  RNLocalize: 49634a6a605dcdeb319e9c42a5c2f1aec508694b
  RNPermissions: 87e469bdb622485ad8e4c1ff35361ee67e63ef40
  RNRate: ef3bcff84f39bb1d1e41c5593d3eea4aab2bd73a
  RNReanimated: bec7736122a268883bdede07f1bf9cf4b40158db
  RNScreens: 85d3880b52d34db7b8eeebe2f1a0e807c05e69fa
  RNSentry: ab4c20e52c66e7bcd461b4a1006ea93b4a5290d6
  RNShare: 19de4c74e0a9203f5f11e88c5f53ebb34d59cee0
  RNSVG: d7d7bc8229af3842c9cfc3a723c815a52cdd1105
  RNVectorIcons: 441de54a3b30f7d1be9354b5dc96ad6e2219948a
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  Sentry: 532b281a53b1b45a523fd592f608956fb36e577c
  toolbar-android: 2a73856e98b750d7e71ce4644d3f41cc98211719
  Yoga: d6134eb3d6e3675afc1d6d65ccb3169b60e21980

PODFILE CHECKSUM: b26644102f9e173ea3e2bfa8dce09551aa780976

COCOAPODS: 1.16.2
