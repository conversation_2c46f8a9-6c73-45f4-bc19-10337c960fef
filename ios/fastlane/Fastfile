
default_platform(:ios)

platform :ios do

  ENV["ITMSTRANSPORTER_FORCE_ITMS_PACKAGE_UPLOAD"] = "false"
  ENV["FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD"]="wtlj-btjt-wtgw-xlqx"
  ENV["FASTLANE_XCODEBUILD_SETTINGS_TIMEOUT"]="40"

  desc "Push a new Dev build to testflight"
  lane :dev do
    match(type:'development', app_identifier: "com.danapay.transfer.dev", )
    cocoapods
    increment_build_number(xcodeproj: "transferApp.xcodeproj")
    build_app(
      workspace: "transferApp.xcworkspace", 
      scheme: "transferApp-Dev",
      xcargs: "CODE_SIGN_STYLE='Manual' DEVELOPMENT_TEAM='42GZCW6UB4'",
      export_method: "development"
    )
    upload_to_testflight
    clean_build_artifacts
  end

  desc "Push a new staging build to testflight"
  lane :stage do
    match(type:'appstore')
    cocoapods
    increment_build_number(xcodeproj: "transferApp.xcodeproj")
    build_app(
      workspace: "transferApp.xcworkspace", 
      scheme: "transferApp-Stage",
      xcargs: "CODE_SIGN_STYLE='Manual' DEVELOPMENT_TEAM='42GZCW6UB4'",
    )
    upload_to_testflight(
      skip_waiting_for_build_processing: true,
      itc_provider: "42GZCW6UB4"
    )
    clean_build_artifacts
  end

  desc "Push a new production build to testflight"
  lane :release do
    match(type:'appstore', app_identifier: "com.danapay.transfer", readonly: true)
    cocoapods
    increment_build_number(xcodeproj: "transferApp.xcodeproj")
    gym(
      workspace: "transferApp.xcworkspace", 
      scheme: "transferApp",  
      xcargs: "CODE_SIGN_STYLE='Manual' DEVELOPMENT_TEAM='42GZCW6UB4'",
    )
    upload_to_testflight(
      skip_waiting_for_build_processing:true,
      itc_provider: "42GZCW6UB4"
    )
    clean_build_artifacts
  end
end
