#import "AppDelegate.h"

#import <React/RCTBridge.h>
#import <React/RCTBundleURLProvider.h>
#import <React/RCTRootView.h>
#import <Firebase.h>
#import <IntercomModule.h>
// #import <CodePush/CodePush.h>
#import <RNFBDynamicLinksAppDelegateInterceptor.h>
#import <AppsFlyerLib/AppsFlyerLib.h>

// Import RNSplashScreen
#import "RNSplashScreen.h"
// #import <AppCenterReactNative.h>
// #import <AppCenterReactNativeAnalytics.h>
// #import <AppCenterReactNativeCrashes.h>

#ifdef FB_SONARKIT_ENABLED
#import <FlipperKit/FlipperClient.h>
#import <FlipperKitLayoutPlugin/FlipperKitLayoutPlugin.h>
#import <FlipperKitUserDefaultsPlugin/FKUserDefaultsPlugin.h>
#import <FlipperKitNetworkPlugin/FlipperKitNetworkPlugin.h>
#import <SKIOSNetworkPlugin/SKIOSNetworkAdapter.h>
#import <FlipperKitReactPlugin/FlipperKitReactPlugin.h>



static void InitializeFlipper(UIApplication *application) {
  FlipperClient *client = [FlipperClient sharedClient];
  SKDescriptorMapper *layoutDescriptorMapper = [[SKDescriptorMapper alloc] initWithDefaults];
  [client addPlugin:[[FlipperKitLayoutPlugin alloc] initWithRootNode:application withDescriptorMapper:layoutDescriptorMapper]];
  [client addPlugin:[[FKUserDefaultsPlugin alloc] initWithSuiteName:nil]];
  [client addPlugin:[FlipperKitReactPlugin new]];
  [client addPlugin:[[FlipperKitNetworkPlugin alloc] initWithNetworkAdapter:[SKIOSNetworkAdapter new]]];
  [client start];
}
#endif

@interface AppDelegate () <AppsFlyerLibDelegate>
@end

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
  [RNFBDynamicLinksAppDelegateInterceptor sharedInstance];
  // [GMSServices provideAPIKey:@"AIzaSyBvotCS4MyZ3GOeE860z1-WSLMyQJhIOJI"];
  // [AppCenterReactNative register];
  // [AppCenterReactNativeAnalytics registerWithInitiallyEnabled:true];
  // [AppCenterReactNativeCrashes registerWithAutomaticProcessing];
   if ([FIRApp defaultApp] == nil) {
    [FIRApp configure];
  }

  // Set AppsFlyer delegate
  [AppsFlyerLib shared].delegate = self;
  #ifdef FB_SONARKIT_ENABLED
    InitializeFlipper(application);
  #endif

  RCTBridge *bridge = [[RCTBridge alloc] initWithDelegate:self launchOptions:launchOptions];
  RCTRootView *rootView = [[RCTRootView alloc] initWithBridge:bridge
                                                   moduleName:@"transferApp"
                                            initialProperties:nil];

  if (@available(iOS 13.0, *)) {
      rootView.backgroundColor = [UIColor systemBackgroundColor];
  } else {
      rootView.backgroundColor = [UIColor whiteColor];
  }

  self.window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
  UIViewController *rootViewController = [UIViewController new];
  rootViewController.view = rootView;
  self.window.rootViewController = rootViewController;
  // [IntercomModule initialize:@"ios_sdk-667d4df93c7d11e9f415fa5564b045e08f07d819" withAppId:@"xuz76by7"];
  [IntercomModule initialize:@"ios_sdk-fe77b98ab6555fd42aadc1224c62cee0fc97f82b" withAppId:@"xuz76by7"];
  [self.window makeKeyAndVisible];
  // Set the splash screen to show by default.
  // [RNSplashScreen show]; 
  return YES;
}

- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {
    // Handle the file URL here
    NSLog(@"Opened file URL: %@", url);
    return YES;
}



- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge
{
  #if DEBUG
  //  return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index" fallbackResource:nil];
    return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index"];
  #else
    return [[NSBundle mainBundle] URLForResource:@"main" withExtension:@"jsbundle"];
    // return [CodePush bundleURL];
  #endif
}

// MARK: - AppsFlyer Delegate Methods
- (void)onConversionDataSuccess:(NSDictionary *)conversionInfo {
    NSLog(@"AppsFlyer conversion data success: %@", conversionInfo);
}

- (void)onConversionDataFail:(NSError *)error {
    NSLog(@"AppsFlyer conversion data failed: %@", error);
}

- (void)onAppOpenAttribution:(NSDictionary *)attributionData {
    NSLog(@"AppsFlyer app open attribution: %@", attributionData);
}

- (void)onAppOpenAttributionFailure:(NSError *)error {
    NSLog(@"AppsFlyer app open attribution failed: %@", error);
}

// MARK: - URL Handling for Deep Links
- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {
    [[AppsFlyerLib shared] handleOpenUrl:url options:options];
    return YES;
}

- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler {
    [[AppsFlyerLib shared] continueUserActivity:userActivity restorationHandler:restorationHandler];
    return YES;
}

- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler {
    [[AppsFlyerLib shared] handlePushNotification:userInfo];
    completionHandler(UIBackgroundFetchResultNewData);
}

@end
