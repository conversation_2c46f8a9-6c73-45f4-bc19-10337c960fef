<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.danapay.transfer</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.danapay.transfer</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>351</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>We require camera access to capture and upload photos for KYC verification.</string>
	<key>NSContactsUsageDescription</key>
	<string>Authorise the access to your contacts in order to add them as favorites</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Danapay needs access to location to detect your country</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Danapay needs access to location to detect your country</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Danapay needs access to location to detect your country</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>We require camera access to capture and upload photos for KYC verification.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>We use Face ID to provide secure and convenient authentication, ensuring that only you can access your personal data and protect your account.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We require camera access to capture and upload photos for KYC verification.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>This app would like to track you across apps and websites to provide personalized ads and improve our services.</string>
	<key>UIAppFonts</key>
	<array>
		<string>Inter-Black.ttf</string>
		<string>Inter-Bold.ttf</string>
		<string>Inter-ExtraBold.ttf</string>
		<string>Inter-ExtraLight.ttf</string>
		<string>Inter-Light.ttf</string>
		<string>Inter-Medium.ttf</string>
		<string>Inter-Regular.ttf</string>
		<string>Inter-SemiBold.ttf</string>
		<string>Inter-Thin.ttf</string>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Fontisto.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Lato-Black.ttf</string>
		<string>Lato-BlackItalic.ttf</string>
		<string>Lato-Bold.ttf</string>
		<string>Lato-BoldItalic.ttf</string>
		<string>Lato-Italic.ttf</string>
		<string>Lato-Light.ttf</string>
		<string>Lato-LightItalic.ttf</string>
		<string>Lato-Regular.ttf</string>
		<string>Lato-Thin.ttf</string>
		<string>Lato-ThinItalic.ttf</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
