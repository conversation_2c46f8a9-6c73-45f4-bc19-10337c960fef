{"name": "transferApp", "version": "1.6.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "devDebug": "npx react-native run-android --variant=devDebug --appIdSuffix=dev", "stagingDebug": "npx react-native run-android --variant=stagingDebug  --appIdSuffix=staging", "prodDebug": "npx react-native run-android --variant=prodDebug  --appIdSuffix=prod", "prodRelease": "cd android && ./gradlew assembleProdRelease", "devBuild": "cd android && ./gradlew assembleDevRelease", "stagingBuild": "cd android && ./gradlew assembleStagingRelease", "prodBuild": "cd android && ./gradlew assembleProdRelease", "start": "react-native start --reset-cache", "test": "jest", "postinstall": "patch-package", "lint": "eslint .", "clean_app": "watchman watch-del-all && killall -9 node && rm -rf yarn.lock package-lock.json node_modules ios/Pods ios/Podfile.lock android/app/build && yarn install && cd ios && pod update && cd ..", "android_clean": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res", "ios_clean": "react-native bundle --entry-file index.js --platform ios --dev false --bundle-output ios/main.jsbundle --assets-dest ios"}, "dependencies": {"@heap/react-native-heap": "^0.22.6", "@intercom/intercom-react-native": "^5.2.0", "@react-native-async-storage/async-storage": "^1.17.10", "@react-native-clipboard/clipboard": "^1.14.2", "@react-native-community/cli-platform-android": "^9.0.0", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/netinfo": "^9.0.0", "@react-native-community/toolbar-android": "^0.2.1", "@react-native-firebase/analytics": "^21.14.0", "@react-native-firebase/app": "^21.14.0", "@react-native-firebase/crashlytics": "^21.14.0", "@react-native-firebase/dynamic-links": "^21.14.0", "@react-native-firebase/messaging": "^21.14.0", "@react-native-picker/picker": "^2.7.2", "@react-navigation/native": "^6.1.17", "@react-navigation/stack": "^6.3.29", "@sentry/react-native": "6.5.0", "@sumsub/react-native-mobilesdk-module": "^1.34.1", "axios": "^1.6.8", "buffer": "^6.0.3", "crypto-js": "^4.2.0", "customerio-reactnative": "3.5.0", "i18n-js": "3.7.1", "laravel-echo": "^1.15.3", "libphonenumber-js": "^1.10.51", "lodash": "^4.17.21", "lodash.memoize": "^4.1.2", "lottie-react-native": "^7.1.0", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "patch-package": "^7.0.0", "postinstall-postinstall": "^2.1.0", "pusher-js": "^8.3.0", "react": "18.1.0", "react-native": "^0.70.15", "react-native-biometrics": "^3.0.1", "react-native-code-input": "^1.0.6", "react-native-config": "^1.5.1", "react-native-contacts": "7.0.8", "react-native-document-picker": "^9.3.1", "react-native-encrypted-storage": "^4.0.3", "react-native-extended-stylesheet": "^0.12.0", "react-native-fast-image": "^8.6.3", "react-native-flash-message": "^0.4.1", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "2.16.0", "react-native-get-random-values": "^1.10.0", "react-native-google-places-autocomplete": "^2.5.6", "react-native-input-select": "^1.3.5", "react-native-linear-gradient": "^2.8.3", "react-native-loading-spinner-overlay": "^3.0.1", "react-native-localize": "1.4.1", "react-native-modal": "^13.0.0", "react-native-modalize": "^2.1.1", "react-native-onboarding-swiper": "^1.2.0", "react-native-otp-inputs": "^7.4.0", "react-native-permissions": "^5.3.0", "react-native-picker-select": "9.1.2", "react-native-popup-menu": "^0.16.1", "react-native-print": "^0.11.0", "react-native-progress": "^5.0.1", "react-native-rate": "^1.2.12", "react-native-reanimated": "^2.10.0", "react-native-safe-area-context": "^4.4.1", "react-native-screens": "3.25.0", "react-native-share": "^12.0.11", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^12.3.0", "react-native-timeline-flatlist": "^0.8.0", "react-native-user-avatar": "^1.0.8", "react-native-vector-icons": "^10.0.0", "react-native-view-shot": "^3.7.0", "react-native-webview": "^11.23.0", "rn-fetch-blob": "^0.12.0", "socket.io-client": "^4.7.2", "uuid": "^9.0.1", "yup": "^1.4.0", "zustand": "^4.3.8"}, "devDependencies": {"@babel/core": "^7.11.1", "@babel/runtime": "^7.11.1", "babel-eslint": "^10.1.0", "babel-jest": "^26.2.2", "eslint": "^9.21.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "jest": "^26.6.3", "metro-react-native-babel-preset": "^0.72.4", "react-native-codegen": "^0.0.7", "react-test-renderer": "18.1.0"}, "jest": {"preset": "react-native"}}