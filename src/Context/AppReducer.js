export default (state, action) => {
  switch (action.type) {
    case 'ADD_CASH_INFO':
      return {
        ...state,
        cash: action.payload,
      };
    case 'INITIALIZE':
      return {
        ...state,
        ...action.payload,
      };
    case 'ADD_BENE_BIO':
      return {
        ...state,
        beneficiary: action.payload,
      };
    case 'ADD_BENE_LOCATION':
      return {
        ...state,
        beneficiaryLocation: action.payload,
      };
    case 'ADD_PAYMENT_INFO':
      return {
        ...state,
        payment: action.payload,
      };
    case 'CAPTURE_USER':
      return {
        ...state,
        user: action.payload,
      };
    case 'ADD_FAVORITES':
      return {
        ...state,
        favorites: [...action.payload],
      };
    case 'SET_PIN_OPTION':
      return {
        ...state,
        pinOptions: action.payload,
      };
    case 'SET_ACCESS_TOKEN':
      return {
        ...state,
        token: action.payload,
      };
    case 'SET_SOFT_LOGOUT':
      return {
        ...state,
        softLogOut: action.payload,
      };
    case 'REMOVE_ALL_DATA':
      return {};
    case 'SET_LIMITS':
      return {
        ...state,
        limits: action.payload,
      };
    case 'SET_TRANSACTION_STATUS':
      return {
        ...state,
        transactionStatus: action.payload,
      };
    case 'SET_TRANSACTIONS':
      return {
        ...state,
        transactions: action.payload,
      };
    case 'SET_ACTIVE_TRANSACTION':
      return {
        ...state,
        active_transaction: action.payload,
      };
    case 'SET_VERIFICATION_LIMIT':
      return {
        ...state,
        limits_verification: action.payload,
      };
    case 'LOGIN':
      return {
        ...state,
        isLoggedIn: true,
      };
    case 'LOGOUT':
      return {
        ...state,
        isLoggedIn: false,
      };
    case 'SET_EURO_LIMITS':
      return {
        ...state,
        euroLimits: action.payload,
      };
    case 'SET_SENDING_COUNTRY':
      return {
        ...state,
        sending_country: action.payload,
      };
    case 'SET_RECEIVING_COUNTRY':
      return {
        ...state,
        receiving_country: action.payload,
      };
    case 'SET_CASH_FEES':
      return {
        ...state,
        cash_fees_on_euro: action.payload,
      };
    default:
      return state;
  }
};
