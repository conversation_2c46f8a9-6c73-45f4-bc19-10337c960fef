import React, {createContext, useReducer, useState, useEffect} from 'react';
import AppReducer from './AppReducer';
import {getData, storeData} from '../utilities/DataStore';

export const GlobalContext = createContext({
  state: async () => await getData(),
});

const GlobalProvider = ({children}) => {
  const [loc, setLoc] = useState(null);
  const [state, dispatch] = useReducer(AppReducer, loc);

  const addCashObj = async (cashObj) => {
    const newStorage = {...loc, cash: cashObj};
    dispatch({type: 'ADD_CASH_INFO', payload: cashObj});
    setLoc(newStorage);
    await storeData(newStorage);
  };

  const addBeneficiary = async (beneficiary) => {
    dispatch({type: 'ADD_BENE_BIO', payload: beneficiary});
    const newStorage = {...loc, beneficiary};
    setLoc(newStorage);
    await storeData(newStorage);
  };

  const addBeneficiaryLocation = async (location) => {
    dispatch({type: 'ADD_BENE_LOCATION', payload: location});
    const newStorage = {...loc, beneficiaryLocation: location};
    setLoc(newStorage);
    await storeData(newStorage);
  };

  const addPaymentInfo = async (payInfo) => {
    const newStorage = {...loc, payment: payInfo};
    setLoc(newStorage);
    await storeData(newStorage);
    dispatch({type: 'ADD_PAYMENT_INFO', payload: payInfo});
  };

  const captureUserData = async (user) => {
    const newStorage = {...loc, user};
    setLoc(newStorage);
    await storeData(newStorage);
    dispatch({type: 'CAPTURE_USER', payload: user});
  };

  const addFavorites = async (favorites) => {
    const newStorage = {...loc, favorites};
    setLoc(newStorage);
    await storeData(newStorage);
    dispatch({type: 'ADD_FAVORITES', payload: favorites});
  };

  const removeAllData = async () => {
    const newStorage = null;
    setLoc(newStorage);
    await storeData(newStorage);
    dispatch({type: 'REMOVE_ALL_DATA', payload: null});
  };

  const setPinOption = async (option) => {
    const newStorage = {...loc, pinOptions: option};
    await storeData(newStorage);
    dispatch({type: 'SET_PIN_OPTION', payload: option});
  };

  const setUserAccessToken = async (token) => {
    const newStorage = {...loc, token};
    dispatch({type: 'SET_ACCESS_TOKEN', payload: token});
    setLoc(newStorage);
    await storeData(newStorage);
  };

  const softLogout = async (option) => {
    const newStorage = {...loc, softLogout: option};
    setLoc(newStorage);
    await storeData(newStorage);
    dispatch({type: 'SET_SOFT_LOGOUT', payload: option});
  };

  const setInitialState = (data) => {
    setLoc(data);
    dispatch({type: 'INITIALIZE', payload: data});
  };

  const setLimits = async (limits) => {
    const newStorage = {...loc, limits};
    setLoc(newStorage);
    await storeData(newStorage);
    dispatch({type: 'SET_LIMITS', payload: limits});
  };

  const setTransactionStatus = async (status) => {
    const newStorage = {...loc, transactionStatus: status};
    setLoc(newStorage);
    await storeData(newStorage);
    dispatch({type: 'SET_TRANSACTION_STATUS', payload: status});
  };

  const setTransactionsData = async (transactions) => {
    const newStorage = {...loc, transactions};
    setLoc(newStorage);
    await storeData(newStorage);
    dispatch({type: 'SET_TRANSACTIONS', payload: transactions});
  };

  const setLimitVerifications = async (data) => {
    const newStorage = {...loc, limits_verification: data};
    setLoc(newStorage);
    await storeData(newStorage);
    dispatch({type: 'SET_VERIFICATION_LIMIT', payload: data});
  };

  const logIn = async () => {
    const newStorage = {...loc, isLoggedIn: true};
    setLoc(newStorage);
    await storeData(newStorage);
    dispatch({type: 'LOGIN', payload: true});
  };

  const logOut = async () => {
    const newStorage = {...loc, isLoggedIn: false};
    setLoc(newStorage);
    await storeData(newStorage);
    dispatch({type: 'LOGOUT', payload: false});
  };

  const setEuroLimits = async (amount) => {
    const newStorage = {...loc, euroLimits: amount};
    setLoc(newStorage);
    await storeData(newStorage);
    dispatch({type: 'SET_EURO_LIMITS', payload: amount});
  };

  const setSendingCountry = async (country) => {
    const newStorage = {...loc, sending_country: country};
    setLoc(newStorage);
    await storeData(newStorage);
    dispatch({type: 'SET_SENDING_COUNTRY', payload: country});
  };

  const setReceivingCountry = async (country) => {
    const newStorage = {...loc, receiving_country: country};
    setLoc(newStorage);
    await storeData(newStorage);
    dispatch({type: 'SET_RECEIVING_COUNTRY', payload: country});
  };

  const setFeesOnCash = async (fees) => {
    const newStorage = {...loc, cash_fees_on_euro: fees};
    setLoc(newStorage);
    await storeData(newStorage);
    dispatch({type: 'SET_CASH_FEES ', payload: fees});
  };

  // useEffect(() => {
  //   const s = async () => await getData();
  //   setLoc(s);
  // }, [loc]);

  return (
    <GlobalContext.Provider
      value={{
        ...state,
        setInitialState,
        addCashObj,
        addBeneficiary,
        addBeneficiaryLocation,
        addPaymentInfo,
        captureUserData,
        addFavorites,
        removeAllData,
        setUserAccessToken,
        softLogout,
        setLimits,
        setTransactionStatus,
        setTransactionsData,
        setLimitVerifications,
        logIn,
        logOut,
        setSendingCountry,
        setEuroLimits,
        setReceivingCountry,
        setFeesOnCash,
      }}>
      {children}
    </GlobalContext.Provider>
  );
};
export default GlobalProvider;
