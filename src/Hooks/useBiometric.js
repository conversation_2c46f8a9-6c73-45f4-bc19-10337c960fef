import {useEffect, useState} from 'react';
import ReactNativeBiometrics, {BiometryTypes} from 'react-native-biometrics';
const rnBiometrics = new ReactNativeBiometrics();

const useBiometric = () => {
  const [available, setAvailable] = useState({});

  useEffect(() => {
    async function checkAvailability() {
      rnBiometrics.isSensorAvailable().then(resultObject => {
        const {available, biometryType} = resultObject;
        if (available && biometryType === BiometryTypes.TouchID) {
          setAvailable(prev => ({...prev, hasTouchId: true}));
        } else if (available && biometryType === BiometryTypes.FaceID) {
          setAvailable(prev => ({...prev, hasFaceId: true}));
        } else if (available && biometryType === BiometryTypes.Biometrics) {
          setAvailable(prev => ({...prev, hasTouchId: true}));
        } else {
        }
      });
    }
    checkAvailability();
  }, []);

  return available;
};

export default useBiometric;
