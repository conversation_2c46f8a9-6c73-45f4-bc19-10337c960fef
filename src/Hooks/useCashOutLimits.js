import {useCallback, useEffect, useState} from 'react';
import useDanaStore from '../app_state/store';

const useCashOutLimits = () => {
  const beneficiary = useDanaStore(state => state.beneficiary);
  const countries = useDanaStore(state => state.countries);

  const [lowerLimit, setLowerLimit] = useState(0);
  const [upperLimit, setUpperLimit] = useState(0);

  const getAllLimits = useCallback(() => {
    const {biggestMaxLimit, smallestMinLimit} =
      beneficiary.country.cashout_methods.reduce(
        (acc, limit) => ({
          biggestMaxLimit: Math.max(
            +acc.biggestMaxLimit,
            +limit?.cashout_method?.max_amount,
          ),
          smallestMinLimit: Math.min(
            +acc.smallestMinLimit,
            +limit?.cashout_method?.min_amount,
          ),
        }),
        {biggestMaxLimit: -Infinity, smallestMinLimit: Infinity},
      );
    setLowerLimit(smallestMinLimit);
    setUpperLimit(biggestMaxLimit);
  }, [beneficiary]);

  useEffect(() => {
    getAllLimits();
  }, []);

  return [lowerLimit, upperLimit];
};

export default useCashOutLimits;
