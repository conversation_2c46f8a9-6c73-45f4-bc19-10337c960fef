import {useEffect, useState} from 'react';
import {Platform, PermissionsAndroid} from 'react-native';
import Contacts from 'react-native-contacts';
import {translate} from '../utilities/Translate';

const useContactPermission = () => {
  const [hasPermission, setHasPermission] = useState(false);
  const [loading, setLoading] = useState(false);

  const checkPermission = async () => {
    if (Platform.OS === 'ios') {
      const permission = await Contacts.checkPermission();
      setHasPermission(permission === 'authorized' ? true : false);
    } else {
      const permission = await PermissionsAndroid.check(
        'android.permission.READ_CONTACTS',
      );
      setHasPermission(permission || permission === 'granted' ? true : false);
    }
  };

  const requestPermission = async () => {
    setLoading(true);
    if (Platform.OS === 'ios') {
      const result = await Contacts.requestPermission();
      setHasPermission(result === 'authorized' ? true : false);
      setLoading(false);
    } else {
      const result = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.READ_CONTACTS,
        {
          title: translate('contacts'),
          message: translate('contact_describe'),
        },
      );
      setHasPermission(result || result === 'granted' ? true : false);
      setLoading(false);
    }
  };

  useEffect(() => {
    checkPermission();
  }, []);

  return {hasPermission, requestPermission,loading};
};

export default useContactPermission;
