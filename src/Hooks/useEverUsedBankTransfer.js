import {useCallback, useEffect, useState} from 'react';
import useDanaStore from '../app_state/store';

const useEverUsedBankTransfer = () => {

  const [neverUsed, setNeverUsed] = useState(false);
  const transfers = useDanaStore(state => state.transfers);
  const fetchTransfer = useDanaStore(state => state.fetchTransfer);

  const checkTransfers = useCallback(() => {
    const used =
      transfers.length === 0
        ? []
        : transfers?.filter(val => val.type === 'bank_transfer');

    if (used.length === 0) {
      setNeverUsed(true);
    } else {
      setNeverUsed(false);
    }
  }, [transfers]);

  useEffect(() => {
    fetchTransfer();
    checkTransfers();
  }, []);

  return neverUsed;
};

export default useEverUsedBankTransfer;
