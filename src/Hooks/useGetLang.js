import {useEffect, useState} from 'react';
import {NativeModules, Platform} from 'react-native';

const useGetLang = () => {
  const [lang, setLang] = useState('en');

  const dectectUserDefaultLang = () => {
    let locale = 'en';
    if (Platform.OS === 'android') {
      locale = NativeModules.I18nManager.localeIdentifier;
    } else {
      locale =
        NativeModules.SettingsManager.settings.AppleLocale ||
        NativeModules.SettingsManager.settings.AppleLanguages[0];
    }
    setLang(locale.split('_')[0]);
  };

  useEffect(() => {
    dectectUserDefaultLang();
  }, []);

  return lang;
};

export default useGetLang;
