import axios from 'axios';
import {useEffect} from 'react';
import {useState} from 'react';
import useDanaStore from '../app_state/store';

export default function useGetLocation() {
  const [locationData, setLocationData] = useState(null);
  const setUserCurrentCountry = useDanaStore(
    state => state.setUserCurrentCountry,
  );

  useEffect(() => {
    getLocation();
  }, []);

  getLocation = async () => {
    const res = await axios.get('http://ip-api.com/json');
    if (res.status === 200) setLocationData(res.data);
    setUserCurrentCountry(res.data);
  };

  return {
    city: locationData?.city,
    country: locationData?.country,
    countryCode: locationData?.countryCode,
    lat: locationData?.lat,
    lon: locationData?.lon,
    region: locationData?.regionName,
    regionCode: locationData?.region,
    timezone: locationData?.timezone,
    zip: locationData?.zip,
  };
}
