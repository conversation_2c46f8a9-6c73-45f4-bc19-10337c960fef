import {useCallback, useEffect, useMemo, useState} from 'react';
import {NativeModules, Platform} from 'react-native';
import useDanaStore from '../app_state/store';

const useGetStep = () => {
  const progress = useDanaStore(state => state.missingData);
  const user = useDanaStore(state => state.user);
  const setVerifyEmail = useDanaStore(state => state.setVerifyEmail);
  const [step, setStep] = useState(0);

  const computeActiveStep = useCallback(() => {
    const bio = [
      'first_name',
      'last_name',
      'email',
      'address_line',
      'city',
      'post_code',
    ].filter(value => progress?.missing_fields.includes(value));

    const origin = ['user_origin'].filter(value =>
      progress?.missing_fields.includes(value),
    );

    const revenue = ['monthly_revenue', 'job', 'activity_id'].filter(value =>
      progress?.missing_fields.includes(value),
    );

    const preferences = ['receiving_countries_ids'].filter(value =>
      progress?.missing_fields.includes(value),
    );

    const residency = ['residency'].filter(value =>
      progress?.missing_fields.includes(value),
    );

    if (origin.length > 0) {
      setStep(0);
      return;
    }

    if (bio.length > 0 || (user?.email && !user?.email_verified_at)) {
      if (!user?.email_verified_at && bio.length === 0) {
        setVerifyEmail(true);
      }

      setStep(1);
      return;
    }
    if (revenue.length > 0) {
      setStep(2);
      return;
    }

    if (preferences.length > 0) {
      setStep(3);
      return;
    }

    if (residency.length > 0) {
      setStep(4);
      return;
    }
  }, [progress]);

  useEffect(() => {
    computeActiveStep();
  }, []);

  return step;
};

export default useGetStep;
