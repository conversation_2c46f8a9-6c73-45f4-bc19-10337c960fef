import {useEffect, useState} from 'react';
import useDanaStore from '../app_state/store';

const useIsCheckActive = () => {
  const user = useDanaStore(state => state.user);
  const [isVerified, setIsVerified] = useState(false);

  useEffect(() => {
    function checkVerificationStatus() {
      if (
        !user?.is_verified ||
        !user?.is_active ||
        user?.client?.type === 'temporary-customer'
      ) {
        setIsVerified(false);
      } else {
        setIsVerified(true);
      }
    }
    checkVerificationStatus();
  }, []);

  return isVerified;
};

export default useIsCheckActive;
