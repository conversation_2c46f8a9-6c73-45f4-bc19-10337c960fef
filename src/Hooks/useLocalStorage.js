import React, {useEffect, useState} from 'react';
import {
  storeData,
  getData,
  clearData,
  removeKeys,
} from '../utilities/DataStore';

export function useLocalStorage(itemName) {
  const [loc, setState] = useState(null);

  const setLoc = newValue => {
    storeData(itemName, newValue);
    setState(newValue);
  };

  useEffect(() => {
    const getLoc = async () => {
      const localData = await getData(itemName);
      setState(localData);
    };
    getLoc();
  }, []);

  return [loc, setLoc];
}
