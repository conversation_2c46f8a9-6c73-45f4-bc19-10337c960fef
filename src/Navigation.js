import React from 'react';
import Heap from '@heap/react-native-heap';
import {NavigationContainer} from '@react-navigation/native';
import {createStackNavigator} from '@react-navigation/stack';
import FlashMessage from 'react-native-flash-message';
import Loading from './screens/Loading';
import Pin from './screens/Pin';
import Home from './screens/Home';
import TransferSuccess from './screens/TransferSuccess';
import Transfers from './screens/Transfers';
import Contact from './screens/Contact';
import Tutorial from './screens/Tutorial';
import Calculator from './screens/Calculator';
import Beneficiary from './screens/Beneficiary';
import PaymentInfo from './screens/PaymentInfo';
import MiniProfile from './screens/MiniProfile';
import Summary from './screens/Summary';
import Profile from './screens/Profile';
import Lock from './screens/Lock';
import BankerTransfer from './screens/BankerTransfer';
import PayLine from './screens/PayLine';
import Sumsub from './screens/Sumsub';
import ChangePin from './screens/ChangePin';
import NotAllowed from './screens/NotAllowed';
import SuccessPage from './screens/SuccessPage';
import WebViewPage from './screens/WebViewPage';
import Deposit from './screens/Deposit';
import Withdraw from './screens/Withdraw';
import Accounts from './screens/Accounts';
import AccountVerification from './screens/AccountVerification';
import Setpin from './screens/Setpin';
import AllContacts from './screens/AllContacts';
import MobileMoney from './screens/MobileMoney';
import ChangePhone from './screens/ChangePhone';
import ProfileEdit from './screens/ProfileEdit';
import Init from './screens/Init';
import CashInMethods from './screens/CashInMethods';
import WithdrawalOptions from './screens/WithdrawalOptions';
import BeneficiarySelection from './screens/transfer/BeneficiarySelection';
import PayInfo from './screens/transfer/PayInfo';
import WithdrawalMethods from './screens/transfer/WithdrawalMethods';
import CashInMethod from './screens/transfer/CashInMethod';
import TransferSummary from './screens/transfer/TransferSummary';
import CreatePin from './screens/auth/CreatePin';
import Register from './screens/auth/Register';
import PhoneNumberCheck from './screens/auth/PhoneNumberCheck';
import Rejected from './screens/auth/Rejected';
import Login from './screens/auth/Login';
import WithdrawalDetails from './screens/withdrawal/WithdrawalDetails';
import WithdrawalVerification from './screens/withdrawal/WithdrawalVerification';
import WithdrawalSuccess from './screens/withdrawal/WithdrawalSuccess';
import Rewards from './screens/Rewards';

const StackNavigator = createStackNavigator();

const config = {
  animation: 'spring',
  config: {
    stiffness: 1000,
    damping: 500,
    mass: 3,
    overshootClamping: true,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 0.01,
  },
};

const HeapNavigationContainer =
  Heap.withReactNavigationAutotrack(NavigationContainer);

const Navigation = () => {
  return (
    <HeapNavigationContainer>
      <StackNavigator.Navigator
        screenOptions={{
          gestureEnabled: true,
          headerShown: false,
          gestureDirection: 'horizontal',
          transitionSpec: {
            open: config,
            close: config,
          },
        }}>
        <StackNavigator.Screen name="Loading" component={Loading} />
        <StackNavigator.Screen name="Tutorial" component={Tutorial} />
        <StackNavigator.Screen name="Login" component={Login} />
        <StackNavigator.Screen name="Lock" component={Lock} />
        <StackNavigator.Screen name="Profile" component={Profile} />
        <StackNavigator.Screen name="Pin" component={Pin} />
        <StackNavigator.Screen name="Home" component={Home} />
        <StackNavigator.Screen name="Deposit" component={Deposit} />
        <StackNavigator.Screen name="Withdraw" component={Withdraw} />
        <StackNavigator.Screen name="Calculator" component={Calculator} />
        <StackNavigator.Screen name="Beneficiary" component={Beneficiary} />
        <StackNavigator.Screen name="CashInMethods" component={CashInMethods} />
        <StackNavigator.Screen name="PaymentInfo" component={PaymentInfo} />
        <StackNavigator.Screen name="Summary" component={Summary} />
        <StackNavigator.Screen name="BankTransfer" component={BankerTransfer} />
        <StackNavigator.Screen
          name="BankerTransfer"
          component={BankerTransfer}
        />
        <StackNavigator.Screen name="AllContacts" component={AllContacts} />
        <StackNavigator.Screen
          name="TransferSuccess"
          component={TransferSuccess}
        />
        <StackNavigator.Screen name="Contact" component={Contact} />
        <StackNavigator.Screen name="Transfers" component={Transfers} />
        <StackNavigator.Screen name="ChangePhone" component={ChangePhone} />
        <StackNavigator.Screen name="Setpin" component={Setpin} />
        <StackNavigator.Screen name="SuccessPage" component={SuccessPage} />
        <StackNavigator.Screen name="Sumsub" component={Sumsub} />
        <StackNavigator.Screen name="ChangePin" component={ChangePin} />
        <StackNavigator.Screen name="WebViewPage" component={WebViewPage} />
        <StackNavigator.Screen name="Accounts" component={Accounts} />
        <StackNavigator.Screen name="MiniProfile" component={MiniProfile} />
        <StackNavigator.Screen name="MobileMoney" component={MobileMoney} />
        <StackNavigator.Screen name="PayLine" component={PayLine} />
        <StackNavigator.Screen name="NotAllowed" component={NotAllowed} />
        <StackNavigator.Screen
          name="AccountVerification"
          component={AccountVerification}
        />
        <StackNavigator.Screen name="ProfileEdit" component={ProfileEdit} />
        <StackNavigator.Screen
          name="WithdrawalOptions"
          component={WithdrawalOptions}
        />

        <StackNavigator.Screen
          name="BeneficiarySelection"
          component={BeneficiarySelection}
        />

        <StackNavigator.Screen name="PayInfo" component={PayInfo} />
        <StackNavigator.Screen
          name="WithdrawalMethods"
          component={WithdrawalMethods}
        />

        <StackNavigator.Screen name="CashInMethod" component={CashInMethod} />
        <StackNavigator.Screen
          name="TransferSummary"
          component={TransferSummary}
        />

        <StackNavigator.Screen name="CreatePin" component={CreatePin} />
        <StackNavigator.Screen name="Rejected" component={Rejected} />
        <StackNavigator.Screen name="Register" component={Register} />
        <StackNavigator.Screen name="Rewards" component={Rewards} />
        <StackNavigator.Screen
          name="PhoneNumberCheck"
          component={PhoneNumberCheck}
        />

        <StackNavigator.Screen
          name="WithdrawalDetails"
          component={WithdrawalDetails}
        />

        <StackNavigator.Screen
          name="WithdrawalVerification"
          component={WithdrawalVerification}
        />

        <StackNavigator.Screen
          name="WithdrawalSuccess"
          component={WithdrawalSuccess}
        />
      </StackNavigator.Navigator>
      <FlashMessage position="top" />
    </HeapNavigationContainer>
  );
};

export default Navigation;