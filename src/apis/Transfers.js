import {cleanObject} from '../utilities';
import instance from './instance';

export const getAllUserTransactions = async (
  page = 1,
  per_page = 8,
  data = null,
) => {
  const payload = cleanObject({page, per_page, ...data});
  const params = new URLSearchParams(payload);
  let response = await instance.get(`/operations?${params}`);
  return response.data;
};


export const _getAllUserTransactions = async payload => {
  // const payload = cleanObject({page, per_page, ...data});
  const params = new URLSearchParams(payload);
  let response = await instance.get(`/operations?${params}`);
  return response.data;
};

export const getATransaction = async (payment_id, token) => {
  let response = await instance.get(`/operations/${payment_id}`);
  return response.data;
};

export const depositOnAccount = async ({data, token}) => {
  let response = await instance.post(`/payin`, JSON.stringify(data));
  return response.data;
};

export const getAppliedFeesForDeposits = async ({data, user_id, token}) => {
  let response = await instance.post(
    `/payments/getEuroAppliedFees/deposit/${user_id}`,
    JSON.stringify(data),
  );
  return response.data;
};

export const makeTransfer = async transaction => {
  let url = '/payments';
  let response = await instance.post(url, transaction);

  return response.data;
};

export const makeDirectTransfer = async transaction => {
  let url = '/payments/direct';
  let response = await instance.post(url, transaction);
  return response.data;
};

export const getTransferLimits = async () => {
  let response = await instance.get('/payments/getAmountLimits');
  return response.data;
};

export const getAllFavorite = async token => {
  let response = await instance.get('/transactions/beneficiaries');
  return response.data;
};

export const downloadPDF = async (transfer_id, token) => {
  let response = await instance.get(`/transactions/transfers/${transfer_id}`);
  return response.data;
};

export const downloadInvoice = async transfer_id => {
  try {
    const response = await instance.get(`/payments/invoices/${transfer_id}`, {
      responseType: 'blob',
    });
    return response.data;
  } catch (error) {
    // Ensure error object has proper structure
    if (error.response) {
      throw error;
    } else {
      // If error doesn't have response property, create a structured error
      throw {
        response: {
          status: 0,
          data: {
            message: error.message || 'Network error occurred'
          }
        }
      };
    }
  }
};

export const getFavorites = async token => {
  let response = await instance.get(`/transactions/favorites`);
  return response.data;
};

export const getEuroLimits = async data => {
  let response = await instance.post(`/payments/getEuroAppliedFees`, data);
  return response.data;
};

export const getCashOutPoints = async () => {
  let response = await instance.get(`/distributorLocations`);
  return response.data;
};

export const getTransferCountries = async () => {
  let response = await instance.get(`/transactions/transferCountries`);
  return response.data;
};

export const calculateFees = async (body, user_id = null) => {
  const url = user_id
    ? `/payments/getEuroAppliedFees/transfer/${user_id}`
    : `/payments/getEuroAppliedFees/transfer`;
  let response = await instance.post(url, body);
  return response.data;
};

export const cancelTransfer = async id => {
  let response = await instance.delete(`/payments/${id}`);
  return response.data;
};

export const getOperationsDetails = async id => {
  let response = await instance.get('/operations/' + id);
  return response.data;
};

//calculate
export const getAppliedFeesForWithdraws = async (data, user_id, token) => {
  let response = await instance.post(
    `/payments/getEuroAppliedFees/withdraw/${user_id}`,
    JSON.stringify(data),
  );
  return response.data;
};

//start payout
export const initiateCashOut = async (data, token) => {
  let response = await instance.post(`/initiatePayout`, JSON.stringify(data));
  return response.data;
};

//send code
export const sendResendCode = async (data, cashout_id, token) => {
  let response = await instance.post(
    `/cashout/${cashout_id}/resendCode`,
    JSON.stringify(data),
  );
  return response.data;
};

//verif otp
export const confirmCashOut = async (payload, token) => {
  let response = await instance.post(`/verifyPayout`, JSON.stringify(payload));
  return response.data;
};

export const getNewStatus = async (payment_id, token) => {
  let response = await instance.get(`/payments/hub2/${payment_id}`);
  return response.data;
};

export const getMMProvider = async (payload, token) => {
  let response = await instance.post(
    '/payments/hub2/execute',
    JSON.stringify(payload),
  );
  return response.data;
};

export const getMMAuthenticate = async (payload, token) => {
  let response = await instance.post(
    '/payments/hub2/confirm',
    JSON.stringify(payload),
  );
  return response.data;
};

// export const makeDirectTransfer = async ({token, payload}) => {
//   let response = await instance.post(
//     '/payments/direct',
//     JSON.stringify(payload),
//   );
//   return response.data;
// };

// get cashout methods for a country
export const getCountryCashOutMethods = async country => {
  let response = await instance.get(`/cashout/${country}`);
  return response.data;
};

// Get the cashout providers
export const getInstitutions = async (providerName, country_code) => {
  let response = await instance.get(
    `/${providerName}/institutions?country_code=${country_code}`,
  );
  return response.data;
};

export const uploadDocs = async (user_id, data) => {
  let response = await instance.post(`/users/${user_id}/documents`, data);
  return response.data;
};


// nero apis
export const addNeeroAccount = async (data, user_id) => {
  let response = await instance.post(`/userWalletAccount/${user_id}`, data);
  return response.data;
};

export const getAllUserNeeroWalletsById = async (id) => {
  return instance.get(`/userWalletAccount/all/${id}`);
};

export const getAllUserNeeroWallets = async () => {
  let response = await instance.get(`/userWalletAccount/all`);
  return response.data;
};

export const deleteNeeroAccount = async (id) => {
  let response = await instance.delete(`/userWalletAccount/${id}`);
  return response.data;
};
