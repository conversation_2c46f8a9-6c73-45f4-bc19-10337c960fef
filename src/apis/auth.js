import Config from 'react-native-config';
import instance from './instance';

export const checkUser = async data => {
  let response = await instance.get('/mobile/customers/exists', {
    params: {...data},
  });
  return response.data;
};

export const getLastRegisterationStep = async () => {
  let response = await instance.get('/users/register');
  return response.data;
};

export const getUserProfile = async user_id => {
  let response = await instance.get('/users/' + user_id);
  return response.data;
};

export const updateUserData = async data => {
  let response = await instance.post('/users/register', JSON.stringify(data));
  return response.data;
};

export const verifyCode = async obj => {
  const url = '/mobile/phoneNumber/verifyCode';
  let response = await instance.post(url, JSON.stringify(obj));
  return response.data;
};

export const sendCode = async obj => {
  const url = '/mobile/phoneNumber/sendCode';
  let response = await instance.post(url, JSON.stringify(obj));
  return response.data;
};

export const updatePhone = async (data, user_id) => {
  const url = `/users/${user_id}`;
  let response = await instance.put(url, data);
  return response.data;
};

export const updateUser = async (data, user_id, token) => {
  const url = `/users/${user_id}`;
  let response = await instance.put(url, data);

  return response.data;
};

export const editProfile = async user => {
  const url = '/mobile/users/register';
  let response = await instance.post(url, user);
  return response.data;
};

export const addRevenue = revenue_data => {
  return instance.put(`/users/updateProfile`, JSON.stringify(revenue_data));
};

export const addHearAbout = async obj => {
  const url = `/meta`;
  let response = await instance.post(url, obj);
  return response.data;
};

export const sendCodeOnPhoneChange = async obj => {
  const url = '/phoneNumber/sendCode';
  let response = await instance.post(url, obj);
  return response.data;
};

export const verifyCodeForPhoneNumberChange = async obj => {
  const url = '/phoneNumber/verifyCode';
  let response = await instance.post(url, obj);
  return response.data;
};

export const register = async user => {
  let response = await instance.post('/customers', JSON.stringify(user));
  return response.data;
};

export const loginUser = async user => {
  let response = await instance.post('/auth/byMobileApp', JSON.stringify(user));
  return response.data;
};

export const loginWithEmailAndPassword = async user => {
  let response = await instance.post('/auth/byEmail', JSON.stringify(user));
  return response.data;
};

export const setPin = async pinData => {
  let response = await instance.put(
    '/mobile/users/resetPin',
    JSON.stringify(pinData),
  );
  return response.data;
};

export const changePin = async pinData => {
  let response = await instance.put(
    '/mobile/users/changePin',
    JSON.stringify(pinData),
  );
  return response.data;
};

export const getCitiesAndDistricts = async country => {
  let response = await instance.get('/countries', {params: {...country}});
  return response.data;
};

export const getTransferCountries = async () => {
  let response = await instance.get('/transactions/transferCountries');
  return response.data;
};

export const getKYCSumSubToken = async payload => {
  let response = await instance.post(
    '/userKYC/accessToken',
    JSON.stringify(payload),
  );
  return response.data;
};

export const getVerificationInfo = async token => {
  let response = await instance.get('/users/verificationDetails');
  return response.data;
};

export const changePinApi = async data => {
  let response = await instance.put('/users/changePassword', data);
  return response;
};

export const updateFCMToken = async data => {
  let response = await instance.put('/auth/fcmToken', {
    fcm_token: data.fcm_token,
    platform: data.platform,
    application: 'Danapay Transfer',
  });
  return response.data;
};

export const getUserCurrentState = async () => {
  let response = await instance.get('/auth/current');
  return response;
};

export const api_getUserCurrentState = async () => {
  let response = await instance.get('/auth/current');
  return response.data;
};

export const addMMAccount = async account => {
  let response = await instance.post(
    '/userMMAccounts',
    JSON.stringify(account),
  );
  return response.data;
};

export const addBankAccount = async account => {
  let response = await instance.post(
    '/userBankAccounts',
    JSON.stringify(account),
  );
  return response.data;
};

export const getMMAccounts = async () => {
  let response = await instance.get('/userMMAccounts/all');
  return response.data;
};

export const getBankAccounts = async () => {
  let response = await instance.get('/userBankAccounts/all');
  return response.data;
};

export const getMMAccountsByUserId = async user_id => {
  let response = await instance.get(`/userMMAccounts/all/${user_id}`);
  return response.data;
};

export const getBankAccountsByUserId = async user_id => {
  let response = await instance.get(`/userBankAccounts/all/${user_id}`);
  return response.data;
};

export const editAccount = async (account, account_id, type) => {
  if (type === 'mobile_accounts') {
    let response = await instance.put(
      `/userMMAccounts/${account_id}`,
      JSON.stringify(account),
    );
    return response.data;
  } else {
    let response = await instance.put(
      `/userBankAccounts/${account_id}`,
      JSON.stringify(account),
    );
    return response.data;
  }
};

export const deleteAccount = async (account_id, type) => {
  if (type === 'mobile_accounts') {
    let response = await instance.delete(`/userMMAccounts/${account_id}`);
    return response.data;
  } else {
    let response = await instance.delete(`/userBankAccounts/${account_id}`);
    return response.data;
  }
};

export const getProviders = async ({country_code}) => {
  let response = await instance.get(`/cashout/${country_code}`, {});
  return response.data;
};

export const app_logout = async () => {
  let response = await instance.post(`/auth/logout`);
  return response.data;
};

export const broadcastingAuth = async (socketId, channel, token = null) => {
  const url = `${Config.URL}/broadcasting/auth`;
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'X-API-SECRET': Config.X_API_SECRET,
      Authorization: `bearer ${token}`,
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      socket_id: socketId,
      channel_name: channel.name,
    }),
  }).then(res => res.json());
  return response;
};

export const sendCodeToEmail = async obj => {
  let url = `/mobile/email/sendCode`;
  let response = await instance.post(url, JSON.stringify(obj));
  return response.data;
};

export const verifyEmailCode = async obj => {
  let url = `/mobile/email/verifyCode`;
  let response = await instance.post(url, JSON.stringify(obj));
  return response.data;
};

export const _getAllActivities = async () => {
  let url = `/individual-activities`;
  let response = await instance.get(url);
  return response.data;
};

export const emailExists = async payload => {
  let response = await instance.post(`/email/exists`, JSON.stringify(payload));
  return response.data;
};

export const checkUserNameIfReal = async payload => {
  let response = await instance.post(
    `/auth/contacts/check-username`,
    JSON.stringify(payload),
  );
  return response.data;
};

export const verifyEmailForExistingUser = async payload => {
  let response = await instance.put(
    `/mobile/users/email/verifyCode`,
    JSON.stringify(payload),
  );
  return response.data;
};

export const removeFavorite = async user_id => {
  const url = `auth/contacts/${user_id}`;
  const response = await instance.delete(url, {});
  return response.data;
};

// reward logic

export const getReferralListing = async id => {
  const response = await instance.get(`campaign-referrals-data/${id}`);
  return response.data;
};

export const getUserReferralData = async id => {
  const response = await instance.get(
    `getUserDefaultCampaignReferralCodeDetails/${id}`,
  );
  return response.data;
};
