import Config from 'react-native-config';

export const base_test_api = Config.BASE_URL;
export const sum_sub_url = Config.SUM_SUB_URL;

const X_API_SECRET = Config.X_API_SECRET;

export const cashRegExp = /^\s+$/;

export const API_HEADERS = {
  Accept: 'application/json',
  'Content-Type': 'application/json',
  X_API_SECRET: X_API_SECRET,
};

export const XAS = {
  X_API_SECRET_PROD: X_API_SECRET,
  X_API_SECRET_DEV: X_API_SECRET,
};
