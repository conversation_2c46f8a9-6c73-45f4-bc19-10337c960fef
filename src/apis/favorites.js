import instance from './instance';

export const addContact = async (data) => {
  let response = await instance.post('/auth/contacts', {...data});
  return response.data;
};

export const checkContactExists = async details => {
  let response = await instance.get(
    `/customers/exists?phone_number=${details.phone_number}&country_code=${details?.country_code}`,
  );
  return response.data;
};

export const fetchingFavorites = async token => {
  let response = await instance.get(`/transactions/favorites`);
  return response.data;
};
