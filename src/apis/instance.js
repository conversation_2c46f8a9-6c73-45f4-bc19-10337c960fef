import axios from 'axios';
import {base_test_api, API_HEADERS, XAS} from './constants';
import useDanaStore from '../app_state/store'; // Add this line to import the Zustand store

const headers = API_HEADERS;

const instance = axios.create({
  baseURL: base_test_api,
  headers: {...headers, 'X-API-SECRET': XAS.X_API_SECRET_DEV},
});

instance.interceptors.request.use(
  async config => {
    const token = useDanaStore.getState().token;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  function (error) {

    return Promise.reject(error);
  },
);

export default instance;
