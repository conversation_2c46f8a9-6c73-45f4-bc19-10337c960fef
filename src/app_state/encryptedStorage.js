import EncryptedStorage from 'react-native-encrypted-storage';
const KEY = 'local_data';

export const save = async (value) => {
  const result = await EncryptedStorage.setItem(KEY, value);
  return result;
};
export const get = async () => {
  const session = await EncryptedStorage.getItem(KEY);
  if (session !== undefined) {
    return session_token;
  }
};
export const remove = async () => {
  return await EncryptedStorage.removeItem(KEY);
};
