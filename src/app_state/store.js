import {create} from 'zustand';
import {
  getTransferCountries,
  getAllUserTransactions,
  getOperationsDetails,
} from '../apis/Transfers';
import {persist, createJSONStorage} from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Heap from '@heap/react-native-heap';
import {
  _getAllActivities,
  getBankAccounts,
  getMMAccounts,
  getProviders,
  getUserCurrentState,
  updateUser,
} from '../apis/auth';
import {fetchingFavorites} from '../apis/favorites';
import {getDanapayBanks} from '../apis/banks';

let store = (set, get) => ({
  beneficiary: {
    firstName: '',
    lastName: '',
    country: {},
    country_name: '',
    phone: '',
  },
  location: {
    city: '',
    district: '',
    isPaid: false,
  },
  transfer: {
    cash_in_method: '',
    amount_in_euro: '',
    amount_in_cfa: '',
    cash_in_method_name: '',
    currency: '',
    reason: '',
    is_direct: false,
    w_method: '',
    w_country: '',
    w_provider: '',
    w_phone: '',
  },
  transfer_response: {
    body: null,
    nextPage: '',
  },
  sendingCountry: null,
  receivingCountry: null,
  user: null,
  user_clone: null,
  temp_user: null,
  token: null,
  countries: [],
  fetching_countries: false,
  transfers: [],
  selected_transfer: null,
  fetching_transfers: false,
  softLogout: false,
  toSummary: false,
  fees: {
    fee: 0,
    exchange_rate: 665.967,
    net_exchange_rate: 665.967,
    spread: 0,
    received_amount: 0,
  },
  recent_transfers: [],
  contacts: [],
  mmAccounts: [],
  bankAccounts: [],
  fetching_contacts: false,
  fetching_next: false,
  localPin: null,
  selected_country_providers: [],
  selected_country_cash_out_Methods: [],
  currency: 'EUR',
  hasNavigatedToVerifyPage: false,
  new_transfers: [],
  linkParams: null,
  trackingId: '',
  cashinFess: {
    fee: 1,
    fee_calculation: [
      {amount_max_range: 1000},
      {amount_min_range: 1},
      {
        exchange_rate: 655.957,
        net_exchange_rate: '655.957',
        received_amount: 0,
        spread: null,
        receiving_currency: 'CFA',
      },
      {'cashin method fee': 0},
    ],
    message: 'Transfer Fee was successfully calculated',
  },
  heapIOUserId: '',
  onboardingTimer: 0,
  transferTimer: 0,
  depositTimer: 0,
  transferTries: 0,
  depositTries: 0,
  onboardingTries: 0,
  activeOperation: '',
  activeOperationPage: '',
  configuredActivities: [],
  userCurrentCountry: null,
  homePageVisitAfterVerification: 0,
  deposit_payload: null,
  withdrawal_payload: null,
  widthdrawal_options: null,
  selectedTransfer: null,
  bankTransferType: null,
  banks: [],
  rejectedReason: '',
  missingData: null,
  verifyEmail: false,
  amountHidden: false,

  fetching_all_transfers: false,
  store_all_transfers: [],

  withdrawal_details: null,
  neeroWallets: [],
  userAccetedDataCollection: false,

  acceptDataCollection: value => {
    set(state => ({
      ...state,
      userAccetedDataCollection: value,
    }));
  },

  setNeeroWallets: wallets => {
    set(state => ({
      ...state,
      neeroWallets: wallets,
    }));
  },

  updateWithdrawalDetails: details => {
    set(state => ({
      ...state,
      withdrawal_details: {
        ...state.withdrawal_details,
        ...details,
      },
    }));
  },
  saveCashinFees: fees => {
    set(state => ({...state, cashinFess: fees}));
  },

  store_fetch_all_transfers: async pageNumber => {
    try {
      set(state => ({
        ...state,
        fetching_all_transfers: true,
      }));

      const response = await getAllUserTransactions(pageNumber, 9, {});
      const details = await Promise.all(
        response?.data?.map(async transfer => {
          const _details = await getOperationsDetails(transfer.payment_id);
          return {...transfer, _details};
        }),
      );

      const _transfers = details?.filter(val => {
        if (
          val?._details?.operation_direction?.toLowerCase() === 'receiving' &&
          ['in progress', 'failed', 'aborted', 'action required'].includes(
            val?._details?.operation_status_en?.toLowerCase(),
          )
        ) {
          return false;
        } else {
          return true;
        }
      });

      if (pageNumber === 0) {
        set(state => ({
          ...state,
          store_all_transfers: _transfers,
          fetching_all_transfers: false,
        }));
      } else {
        set(state => ({
          ...state,
          store_all_transfers: [..._transfers],
          fetching_all_transfers: false,
        }));
      }
    } catch (error) {
      console.log('error', error);
    }
  },

  updateAmountHidden: status => {
    set(state => ({
      ...state,
      amountHidden: status,
    }));
  },

  setVerifyEmail: status => {
    set(state => ({
      ...state,
      verifyEmail: status,
    }));
  },

  setMissingData: data => {
    set(state => ({
      ...state,
      missingData: data,
    }));
  },

  setRejectReason: reason => {
    set(state => ({
      ...state,
      rejectedReason: reason,
    }));
  },

  getBanks: () => {
    const _state = get();
    const {country_code} = _state.user;
    getDanapayBanks(country_code)
      .then(response => {
        set(state => ({
          ...state,
          banks: response.bank_accounts,
        }));
      })
      .catch(error => {});
  },

  setBankTransferType: text => {
    set(state => ({
      ...state,
      bankTransferType: text,
    }));
  },

  setSelelctedTransfer: transfer => {
    set(state => ({
      ...state,
      selectedTransfer: transfer,
    }));
  },

  setDespositPayload: obj => {
    set(state => ({
      ...state,
      deposit_payload: {...state.deposit_payload, ...obj},
    }));
  },

  setWithdrawalOptionsPayload: obj => {
    set(state => ({
      ...state,
      widthdrawal_options: {...state.widthdrawal_options, ...obj},
    }));
  },

  setWithdrawalPayload: obj => {
    set(state => ({
      ...state,
      withdrawal_payload: {...state.withdrawal_payload, ...obj},
    }));
  },

  updateHomePageVisitAfterVerification: () => {
    set(state => ({
      ...state,
      homePageVisitAfterVerification: state.homePageVisitAfterVerification + 1,
    }));
  },

  setUserCurrentCountry: country => {
    set(state => ({...state, userCurrentCountry: country}));
  },

  getAllActivities: async () => {
    try {
      const response = await _getAllActivities();
      set(state => ({...state, configuredActivities: response.data}));
    } catch (error) {}
  },

  setActiveOperationType: operation => {
    set(state => ({...state, activeOperation: operation}));
  },

  setActiveOperationPage: activeOperationPage => {
    set(state => ({...state, activeOperationPage}));
  },

  increaseTries: data => {
    const _state = get();
    if (data.type === 'increment') {
      if (data.operations === 'transfer') {
        const transferCounter = _state.transferTries + 1;
        set(state => ({...state, transferTries: transferCounter}));
      } else if (data.operations === 'deposit') {
        const depositCounter = _state.depositTries + 1;
        set(state => ({...state, depositTries: depositCounter}));
      } else if (data.operations === 'onboarding') {
        const onboardingCounter = _state.onboardingTries + 1;
        set(state => ({...state, onboardingTries: onboardingCounter}));
      }
    } else if (data.type === 'reset') {
      if (data.operations === 'transfer') {
        set(state => ({...state, transferTries: 0}));
      } else if (data.operations === 'deposit') {
        set(state => ({...state, depositTries: 0}));
      } else if (data.operations === 'onboarding') {
        set(state => ({...state, onboardingTries: 0}));
      }
    }
  },

  updateOnboardingTimer: value => {
    const _state = get();
    if (value === 'increment') {
      const newCounter = _state.onboardingTimer + 1;
      set(state => ({...state, onboardingTimer: newCounter}));
    } else {
      set(state => ({...state, onboardingTimer: 0}));
    }
  },

  updateTransferTimer: value => {
    const _state = get();
    if (value === 'increment') {
      const newCounter = _state.transferTimer + 1;
      set(state => ({...state, transferTimer: newCounter}));
    } else {
      set(state => ({...state, transferTimer: 0}));
    }
  },

  updateDepositTimer: value => {
    const _state = get();
    if (value === 'increment') {
      const newCounter = _state.depositTimer + 1;
      set(state => ({...state, depositTimer: newCounter}));
    } else {
      set(state => ({...state, depositTimer: 0}));
    }
  },

  updateHNTVP: value => {
    set(state => ({...state, hasNavigatedToVerifyPage: value}));
  },

  storeLinkParams: values => {
    set(state => ({...state, linkParams: values}));
  },

  setLocalPin: pin => {
    set(state => ({...state, localPin: pin}));
  },

  setTemp: temp => {
    set(state => ({...state, temp_user: {...state.temp_user, ...temp}}));
  },

  updateUser: async data => {
    set(state => ({
      ...state,
      user: {...state.user, ...data},
      user_clone: {...state.user, ...data},
    }));
  },

  fetchCountries: async () => {
    try {
      set(state => ({...state, fetching_countries: true}));
      const response = await getTransferCountries();
      set(state => ({
        ...state,
        countries: response.data.length === 0 ? state.countries : response.data,
        fetching_countries: false,
      }));
    } catch (error) {}
  },

  updateConfiguredCounntries: async data => {
    set(state => ({
      ...state,
      countries: data,
    }));
  },

  fetchSelectedCountryProviders: async country_letter_code => {
    const _state = get();
    set(state => ({...state, fetching_providers: true}));
    const response = await getProviders({
      country_letter_code,
    });
    set(state => ({
      ...state,
      selected_country_providers: response[0].providers,
      selected_country_cash_out_Methods: response[0].cash_out_method,
      fetching_providers: false,
    }));
  },

  fetchTransfer: async (data = null, page = 0) => {
    const _state = get();
    set(state => ({...state, fetching_transfers: true}));
    try {
      const response = await getAllUserTransactions(page, 9, data);
      // Wait for all promises to resolve
      const details = await Promise.all(
        response.data.map(async transfer => {
          const _details = await getOperationsDetails(transfer.payment_id);
          return {...transfer, _details};
        }),
      );

      if (response.data) {
        set(state => ({
          ...state,
          new_transfers: details,
          transfers: response.data,
          fetching_transfers: false,
          recent_transfers: response.data.slice(0, 3),
        }));
      }
    } catch (error) {
      set(state => ({...state, fetching_transfers: false}));
    }
  },

  fetchNextTransfer: async (data = null, page = 0) => {
    const _state = get();
    set(state => ({...state, fetching_next: true}));
    try {
      const response = await getAllUserTransactions(page, 9, data);
      set(state => ({
        ...state,
        transfers: [...state.transfers, ...response.data],
        fetching_next: false,
      }));
    } catch (error) {
      set(state => ({...state, fetching_next: false}));
    }
  },

  fetchContacts: async () => {
    const _state = get();
    set(state => ({...state, fetching_contacts: true}));
    try {
      const response = await fetchingFavorites(_state.token);
      set(state => ({
        ...state,
        contacts: response,
        fetching_contacts: false,
      }));
    } catch (error) {
      set(state => ({...state, fetching_contacts: false}));
    }
  },

  getUserCurrentState: async () => {
    const _state = get();
    const user_info = await getUserCurrentState();
    const sendingCountry = _state.countries.find(
      val => val.country_code === user_info.data.country_code,
    );
    set(state => ({
      ...state,
      user: {..._state.user, ...user_info.data},
      user_clone: {..._state.user, ...user_info.data},
      sendingCountry,
    }));
  },

  storeOperationTrackingId: trackingId => {
    set(state => ({...state, trackingId}));
  },

  storeHeapIOUserId: async () => {
    const heapIOUserId = await Heap.getUserId();
    set(state => ({...state, heapIOUserId}));
  },

  getMMAccounts: async () => {
    const _state = get();
    const mmAccounts = await getMMAccounts();

    set(state => ({...state, mmAccounts: mmAccounts.data}));
  },

  getBankAccounts: async () => {
    const _state = get();
    const bankAccounts = await getBankAccounts();
    set(state => ({...state, bankAccounts: bankAccounts.data}));
  },

  toggleToSummary: () => {
    set(status => state => ({...state, toSummary: status}));
  },

  saveTransferResponse: data => {
    set(state => ({...state, transfer_response: data}));
  },

  toggleSoftLogout: status => set(state => ({...state, softLogout: status})),

  saveTransfer: transfer =>
    set(state => ({...state, transfer: {...state.transfer, ...transfer}})),

  selectTransfer: transfer =>
    set(state => ({...state, selected_transfer: transfer})),

  saveBeneficiary: beneficiary =>
    set(state => ({
      ...state,
      beneficiary: {...state.beneficiary, ...beneficiary},
    })),

  removeBeneficiary: () =>
    set(state => ({
      ...state,
      beneficiary: null,
    })),
  selectOperation: operation =>
    set(state => ({...state, selected_operation: operation})),
  saveLocation: location => set(state => ({...state, location})),
  saveReceivingCountry: receivingCountry =>
    set(state => ({...state, receivingCountry})),
  saveFees: fees => {
    if (fees.exchange_rate) {
      set(state => ({...state, fees}));
    }
  },
  saveSendingCountry: sendingCountry =>
    set(state => ({...state, sendingCountry})),

  saveUser: user =>
    set(state => ({
      ...state,
      user: {...state.user, ...user},
      user_clone: {...state.user, ...user},
    })),

  saveToken: token => set(state => ({...state, token})),

  saveChangeCurrency: currency => set(state => ({...state, currency})),

  restoreTransfer: () => {
    set(state => ({
      ...state,
      beneficiary: {
        firstName: '',
        lastName: '',
        country: {},
        country_name: '',
        phone: '',
      },
      location: {
        city: '',
        district: '',
        isPaid: false,
      },
      transfer: {
        cash_in_method: '',
        amount_in_euro: '',
        amount_in_cfa: '',
        cash_in_method_name: '',
        reason: '',
        is_direct: false,
        w_method: '',
        w_country: '',
        w_provider: '',
        w_phone: '',
      },
      receivingCountry: null,
      fees: {
        fee: 0,
      },
      deposit_payload: null,
      withdrawal_payload: null,
    }));
  },

  resetStore: () =>
    set({
      beneficiary: {
        firstName: '',
        lastName: '',
        country: {},
        country_name: '',
        phone: '',
      },
      new_transfers: [],
      location: {
        city: '',
        district: '',
        isPaid: false,
      },
      transfer: {
        cash_in_method: '',
        amount_in_euro: '',
        amount_in_cfa: '',
        cash_in_method_name: '',
        currency: '',
        reason: '',
        is_direct: false,
        w_method: '',
        w_country: '',
        w_provider: '',
        w_phone: '',
      },
      transfer_response: {
        body: null,
        nextPage: '',
      },
      sendingCountry: null,
      receivingCountry: null,
      user: null,
      user_clone: null,
      temp_user: null,
      token: null,
      fetching_countries: false,
      transfers: [],
      selected_transfer: null,
      fetching_transfers: false,
      softLogout: false,
      toSummary: false,
      fees: {
        fee: 0,
        exchange_rate: 665.967,
        net_exchange_rate: 665.967,
        spread: 0,
        received_amount: 0,
      },
      recent_transfers: [],
      contacts: [],
      mmAccounts: [],
      bankAccounts: [],
      fetching_contacts: false,
      fetching_next: false,
      localPin: null,
      selected_country_providers: [],
      selected_country_cash_out_Methods: [],
      currency: 'EUR',
      hasNavigatedToVerifyPage: false,
      linkParams: null,
      trackingId: '',
      heapIOUserId: '',
      onboardingTimer: 0,
      transferTimer: 0,
      depositTimer: 0,
      transferTries: 0,
      depositTries: 0,
      onboardingTries: 0,
      activeOperation: '',
      activeOperationPage: '',
      configuredActivities: [],
      userCurrentCountry: null,
      homePageVisitAfterVerification: 0,
      deposit_payload: null,
      withdrawal_payload: null,
      widthdrawal_options: null,
      selectedTransfer: null,
      bankTransferType: null,
      banks: [],
      rejectedReason: '',
      missingData: null,
      verifyEmail: false,
      fetching_all_transfers: false,
      store_all_transfers: [],
    }),
});

store = persist(store, {
  name: 'dana_store',
  storage: createJSONStorage(() => AsyncStorage), // <==  pay attention
});

const useDanaStore = create(store);
export default useDanaStore;
