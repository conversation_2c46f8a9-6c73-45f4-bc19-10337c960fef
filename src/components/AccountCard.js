import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  ActivityIndicator,
  SafeAreaView,
  ScrollView,
  TextInput,
  Alert,
  Dimensions,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import Icon from 'react-native-vector-icons/Feather';
import Modal from 'react-native-modal';
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
} from 'react-native-popup-menu';
import TextCp from './TextCp';
import {flags} from '../images/flags';
import {deleteAccount, editAccount, getProviders} from '../apis/auth';
import useDanaStore from '../app_state/store';
import DanaButton from './DanaButton';
import SelectInput from '../components/SelectInput';
import Input from '../components/Input';
import {showMessage} from 'react-native-flash-message';
import {extractError} from '../utilities/errorReporting';

const {width} = Dimensions.get('window');

const AccountCard = ({
  refreshList,
  details,
  type,
  style = {},
  is_active,
  translate,
}) => {
  const [deleting, setDeleting] = useState(false);
  const state_countries = useDanaStore(state => state.countries);
  const [providers, setProviders] = useState([]);
  const [account, setAccount] = useState({
    title: details?.account?.title,
    owner: details?.account?.owner_name,
    operator: details?.operator,
    bic: details?.bic,
    iban: details?.iban,
    bank_name: details?.bank_name,
    phone_number: details?.phone_number,
    country_code: details?.country_code,
    ...details,
    country: state_countries.find(
      val => val.country_code === details?.country_code,
    ),
  });
  const [show, setShow] = useState(false);
  const [loading, setLoading] = useState(false);
  const [processing, setProcessing] = useState(false);
  const token = useDanaStore(state => state.token);

  const deleteCard = () => {
    Alert.alert('', translate('deleting_account_desc'), [
      {
        text: translate('cancel'),
        onPress: () => null,
        style: 'cancel',
      },
      {
        style: 'delete',
        text: translate('yes_delete'),
        onPress: () => {
          setDeleting(true);
          deleteAccount(details?.id, type)
            .then(response => {
              refreshList();
              setDeleting(false);
            })
            .catch(error => {
              setDeleting(false);
              showMessage({
                type: 'danger',
                message: extractError(error),
              });
            });
        },
      },
    ]);
  };

  const editSelectedAccount = async () => {
    setLoading(true);
    try {
      if (type == 'mobile_accounts') {
        const mm_response = await editAccount(
          {
            country_code: account?.country?.country_code,
            country: account?.country?.name,
            title: account?.title,
            owner_name: account?.owner,
            operator: account?.operator,
            phone_number: account?.phone_number,
          },
          details?.account?.id,
          type,
        );
        setShow(false);
        refreshList();
      } else {
        const bank_response = await editAccount(
          {
            country_code: account?.country?.country_code,
            country: account?.country.name,
            title: account?.title,
            owner_name: account?.owner,
            bic: account?.bic,
            bank_name: account?.bank_name,
            iban: account?.iban,
          },
          details?.account?.id,
          type,
        );
        setShow(false);
        refreshList();
      }
      setLoading(false);
    } catch (error) {
      showMessage({
        type: 'danger',
        message: extractError(error),
      });
      setLoading(false);
      setShow(false);
    }
  };

  const fetchProviders = async code => {
    setProviders([]);
    try {
      setProcessing(true);
      const all = await getProviders({
        country_letter_code: code,
      });
      const mobile_money_option = all.find(
        val => val?.cash_out_method?.name === 'Mobile Money By Hub2',
      );
      if (mobile_money_option) {
        setProviders([
          ...mobile_money_option?.providers?.map(provider => ({
            name: provider?.name,
          })),
          {
            name: 'Other',
          },
        ]);
      }

      setProcessing(false);
    } catch (error) {
      setProcessing(false);
    }
  };

  useEffect(() => {
    const country = state_countries?.find(
      val => val.code === details?.country_code,
    );
    fetchProviders(details?.country_code);
    setAccount(prev => ({...prev, country}));
  }, [state_countries]);

  return (
    <>
      <View
        style={[
          styles.card,
          {...style, borderWidth: is_active ? 2 : 0, borderColor: '#037375'},
        ]}>
        <TouchableOpacity onPress={() => refreshList(details)}>
          <View style={styles.cardHeader}>
            <View>
              <TextCp textType={'bold'} style={styles.cardTitle}>
                {details?.title}
              </TextCp>
              <TextCp>{details?.owner_name}</TextCp>
            </View>
            {details?.country && (
              <Image
                source={
                  flags[details?.country?.toLowerCase().replace(/\s/g, '_')]
                }
                style={styles.flag}
              />
            )}
          </View>
          <View style={styles.cardContent}>
            {type === 'mobile_accounts' ? (
              <View style={styles.cardContentInner}>
                <View style={styles.cardContentInnerLeft}>
                  <TextCp opacity={0.6} style={styles.itemTitle}>
                    {translate('phone_number')}
                  </TextCp>
                  <TextCp
                    opacity={0.8}
                    textType={
                      'bold'
                    }>{`+${details?.account?.country_code}${details?.phone_number}`}</TextCp>
                </View>
                <View style={styles.cardContentInnerLeft}>
                  <TextCp opacity={0.6} style={styles.itemTitle}>
                    {translate('operator')}
                  </TextCp>
                  <TextCp opacity={0.8} textType={'bold'}>
                    {details?.operator}
                  </TextCp>
                </View>
              </View>
            ) : (
              <View style={[{flexDirection: 'row'}]}>
                <View style={[styles.cardContentInnerLeft]}>
                  <View style={{marginBottom: 8}}>
                    <TextCp opacity={0.6} style={styles.itemTitle}>
                      {translate('bank_name')}
                    </TextCp>
                    <TextCp textType={'bold'}>{details?.bank_name}</TextCp>
                  </View>
                  <View style={{flexDirection: 'row'}}>
                    <View style={{flex: 1}}>
                      <TextCp opacity={0.6} style={styles.itemTitle}>
                        {translate('iban')}
                      </TextCp>
                      <TextCp textType={'bold'}>{details?.iban}</TextCp>
                    </View>

                    <View style={{flex: 1}}>
                      <TextCp opacity={0.6} style={styles.itemTitle}>
                        {translate('bic')}
                      </TextCp>
                      <TextCp textType={'bold'}>{details?.bic}</TextCp>
                    </View>
                  </View>
                </View>
              </View>
            )}
          </View>
        </TouchableOpacity>

        <View style={styles.cardFooter}>
          {deleting ? (
            <View style={styles.cardFooterBtn}>
              <ActivityIndicator color="#BC4749" size="small" />
            </View>
          ) : (
            <TouchableOpacity onPress={deleteCard} style={styles.cardFooterBtn}>
              <TextCp style={styles.textDelete} color="#BC4749">
                {translate('delete_account')}
              </TextCp>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            onPress={() => {
              setAccount(details);
              setShow(true);
            }}
            style={styles.cardFooterBtn}>
            <TextCp style={styles.textEdit} color="#4a4a4a">
              {translate('edit_account')}
            </TextCp>
          </TouchableOpacity>
        </View>
      </View>
      <Modal
        isVisible={show}
        animationType="fade"
        statusBarTranslucent={true}
        avoidKeyboard={true}
        style={{backgroundColor: 'rgba(154,155,159, .3)', margin: 0}}>
        <SafeAreaView style={{flex: 1, backgroundColor: '#fff'}}>
          <ScrollView contentContainerStyle={styles.modal}>
            <View style={styles.toolbar}>
              <TextCp textType={'bold'} style={styles.modalHeaderText}>
                {type === 'mobile_accounts'
                  ? translate('edit_mm_account')
                  : translate('edit_bank_account')}
              </TextCp>
              <TouchableOpacity
                onPress={() => {
                  setShow(false);
                }}>
                <Icon name="x-circle" size={30} color={'#4a4a4a'} />
              </TouchableOpacity>
            </View>
            <View>
              {type === 'mobile_accounts' && (
                <>
                  <SelectInput
                    onValueChange={value => {
                      const country = state_countries.find(
                        val => val.id === value,
                      );
                      setAccount(acc => ({...acc, country}));
                      fetchProviders(country?.code);
                    }}
                    items={state_countries?.map(val => {
                      return {
                        label: val?.name,
                        value: val?.id,
                      };
                    })}
                    placeholder={{
                      label: translate('country'),
                      value: translate('country'),
                    }}
                    value={account?.country?.id}
                  />

                  {type === 'mobile_accounts' && (
                    <View style={styles.row}>
                      <View
                        style={[
                          styles.input,
                          {
                            justifyContent: 'center',
                            alignItems: 'center',
                            width: 80,
                            marginRight: 5,
                            height: 48,
                            marginVertical: 0,
                          },
                        ]}>
                        <TextCp>+{account?.country?.country_code}</TextCp>
                      </View>
                      <View style={{flex: 1}}>
                        <Input
                          onChange={text => {
                            dispatch({type: 'SET_PHONE_NUMBER', payload: text});
                          }}
                          label={translate('phone_number')}
                          value={account?.phone_number}
                          backgroundColor={'rgba(0, 0, 0, 0.07)'}
                          marginVertical={0}
                        />
                      </View>
                    </View>
                  )}
                </>
              )}

              <TextInput
                style={[styles.input]}
                onChangeText={text =>
                  setAccount(prev => ({...prev, title: text}))
                }
                placeholder={translate('account_title')}
                value={account?.title}
                returnKeyType="next"
              />

              <TextInput
                style={[styles.input]}
                onChangeText={text =>
                  setAccount(prev => ({...prev, owner_name: text}))
                }
                placeholder={translate('owner')}
                value={account?.owner_name}
                returnKeyType="next"
              />

              {type === 'mobile_accounts' ? (
                <>
                  {processing && <TextCp>fetching operators...</TextCp>}
                  <SelectInput
                    onValueChange={value => {
                      setAccount(acc => ({...acc, operator: value}));
                    }}
                    items={providers.map(item => ({
                      label: item.name,
                      value: item.name,
                    }))}
                    placeholder={{
                      label: translate('operator'),
                      value: translate('operator'),
                    }}
                    value={account?.operator}
                  />
                </>
              ) : (
                <>
                  <TextInput
                    style={[styles.input]}
                    onChangeText={text =>
                      setAccount(prev => ({...prev, bank_name: text}))
                    }
                    placeholder={translate('bank_name')}
                    value={account?.bank_name}
                    returnKeyType="next"
                  />
                  <View style={styles.row}>
                    <TextInput
                      style={[styles.input, {flex: 1, marginRight: 5}]}
                      onChangeText={text =>
                        setAccount(prev => ({...prev, iban: text}))
                      }
                      placeholder={translate('iban')}
                      value={account?.iban}
                      returnKeyType="next"
                    />

                    <TextInput
                      style={[styles.input, {flex: 1, marginLeft: 5}]}
                      onChangeText={text =>
                        setAccount(prev => ({...prev, bic: text}))
                      }
                      placeholder={translate('bic')}
                      value={account?.bic}
                      returnKeyType="next"
                    />
                  </View>
                </>
              )}

              <DanaButton
                title={translate('edit_account')}
                onPress={editSelectedAccount}
                theme="#282828"
                loading={loading}
              />
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  cardFooterBtn: {
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  card: {
    backgroundColor: '#fff',
    elevation: 0.4,
    marginVertical: 8,
    borderRadius: 8,
  },
  cardHeader: {
    paddingVertical: 8,
    paddingHorizontal: 13,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    elevation: 0.4,
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
  },
  flag: {
    height: 25,
    width: 25,
  },
  cardContent: {
    paddingHorizontal: 13,
    paddingVertical: 8,
  },
  cardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    backgroundColor: '#fff',
    borderTopWidth: 0.4,
    borderTopColor: '#4a4a4a',
    borderBottomRightRadius: 10,
    borderBottomLeftRadius: 10,
  },
  cardContentInner: {
    flexDirection: 'row',
  },
  cardContentInnerLeft: {
    flex: 1,
  },
  cardContentInnerRight: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 16,
    textTransform: 'capitalize',
    color: '#4a4a4a',
  },
  cardContentTitle: {
    color: '#aaa',
    fontSize: 12,
    marginBottom: 3,
  },
  divider: {
    height: 16,
  },
  textDelete: {
    color: 'red',
    fontSize: 14,
  },
  textEdit: {
    color: '#4a4a4a',
    fontSize: 14,
  },
  toolbar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    height: 56,
    alignItems: 'center',
    marginVertical: 16,
  },
  modal: {
    backgroundColor: '#fff',
    padding: 14,
    marginTop: 20,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
  },
  input: {
    width: '100%',
    height: 48,
    backgroundColor: 'rgba(0, 0, 0, 0.07)',
    borderRadius: 5,
    paddingHorizontal: 17,
    fontFamily: 'Inter-Regular',
    marginVertical: 8,
  },
  modalHeaderText: {
    fontSize: 18,
    width: width * 0.7,
  },
  itemTitle: {
    textTransform: 'uppercase',
    fontSize: 10,
    marginBottom: 5,
  },
});

export default AccountCard;
