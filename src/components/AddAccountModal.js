import {
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import React, {useReducer, useState} from 'react';
import Icon from 'react-native-vector-icons/Feather';
import Modal from 'react-native-modal';
import TextCp from './TextCp';
import useDanaStore from '../app_state/store';
import DanaButton from './DanaButton';
import {addBankAccount, addMMAccount, getProviders} from '../apis/auth';
import SelectInput from './SelectInput';
import Input from './Input';
import FlashMessage from 'react-native-flash-message';
import {extractError} from '../utilities/errorReporting';
import {useRef} from 'react';

const initial_state = {
  country: null,
  title: '',
  owner: '',
  operator: '',
  bic: '',
  iban: '',
  bank_name: '',
  phone_number: '',
};

const reducer = (state = initial_state, action) => {
  switch (action.type) {
    case 'SET_COUNTRY':
      return {...state, country: action.payload};
    case 'SET_TITLE':
      return {...state, title: action.payload};
    case 'SET_OWNER':
      return {...state, owner: action.payload};
    case 'SET_OPERATOR':
      return {...state, operator: action.payload};
    case 'SET_OPERATOR_OTHER':
      return {...state, operator_other: action.payload};
    case 'SET_BIC':
      return {...state, bic: action.payload};
    case 'SET_IBAN':
      return {...state, iban: action.payload};
    case 'SET_PHONE_NUMBER':
      return {...state, phone_number: action.payload};
    case 'SET_BANK_NAME':
      return {...state, bank_name: action.payload};
    default:
      break;
  }
};

const AddAccountModal = ({
  show,
  setShow,
  type,
  translate,
  onModalHide = null,
}) => {
  const myLocalFlashMessage = useRef(null);

  const state_countries = useDanaStore(state => state.countries);
  const token = useDanaStore(state => state.token);
  const [state, dispatch] = useReducer(reducer, initial_state);
  const [loading, setLoading] = useState(false);
  const [providers, setProviders] = useState([]);
  const [processing, setProcessing] = useState(false);

  const addAccount = async () => {
    setLoading(true);
    if (type == 'mobile_accounts') {
      try {
        const mm_response = await addMMAccount(
          {
            country_code: state?.country?.country_code,
            country: state?.country?.name,
            title: state?.title,
            owner_name: state?.owner,
            operator:
              state.operator === 'Other'
                ? state?.operator_other
                : state?.operator,
            phone_number: state?.phone_number,
          },
        );

        myLocalFlashMessage.current.showMessage({
          type: 'success',
          message: translate('account_added'),
        });
        setShow(false);
      } catch (error) {
        myLocalFlashMessage.current.showMessage({
          type: 'danger',
          message: extractError(error),
        });
        setLoading(false);
      }
    } else {
      try {
        const bank_response = await addBankAccount(
          {
            country_code: state?.country?.country_code,
            country: state?.country?.name,
            title: state?.title,
            owner_name: state?.owner,
            bic: state?.bic,
            bank_name: state?.bank_name,
            iban: state?.iban,
          },
        );

        myLocalFlashMessage.current.showMessage({
          type: 'success',
          message: translate('account_added'),
        });
        setShow(false);
      } catch (error) {
        const msg = extractError(error) || translate('something_went_wrong');
        myLocalFlashMessage.current.showMessage({
          type: 'danger',
          message: msg,
        });
        setLoading(false);
      }
    }
    setLoading(false);
  };

  const fetchProviders = async country => {

    setProviders([]);
    try {
      setProcessing(true);
      const all = await getProviders({
        country_code: country.code,
      });


      const mobile_money_option = all.find(
        val =>
          val.cash_out_method.name === 'Mobile Money By Hub2' ||
          val.cash_out_method.name === 'Mobile Money Internal',
      );

      if (mobile_money_option) {
        setProviders([
          ...mobile_money_option.providers.map(provider => ({
            name: provider.name,
          })),
          {
            name: 'Other',
          },
        ]);
      }
      setProcessing(false);
    } catch (error) {
      setProcessing(false);
    }
  };

  return (
    <Modal
      isVisible={show}
      animationType="fade"
      statusBarTranslucent={true}
      onModalHide={onModalHide}
      avoidKeyboard={true}
      style={{backgroundColor: 'rgba(154,155,159, .3)', margin: 0}}>
      <SafeAreaView style={{flex: 1, backgroundColor: '#fff'}}>
        <ScrollView contentContainerStyle={styles.modal}>
          <View style={styles.toolbar}>
            <TextCp textType={'bold'} style={styles.modalHeaderText}>
              {type === 'mobile_accounts'
                ? translate('add_mm')
                : translate('add_ba')}
            </TextCp>
            <TouchableOpacity onPress={setShow}>
              <Icon name="x-circle" size={30} color={'#4a4a4a'} />
            </TouchableOpacity>
          </View>
          <View>
            <SelectInput
              onValueChange={value => {
                const country = state_countries.find(val => val.id === value);
                fetchProviders(country);
                dispatch({type: 'SET_COUNTRY', payload: country});
              }}
              items={state_countries?.map(val => {
                return {
                  label: val?.name,
                  value: val?.id,
                };
              })}
              placeholder={{
                label: translate('country'),
                value: translate('country'),
              }}
              value={state.country?.id}
            />

            {type === 'mobile_accounts' && (
              <View style={styles.row}>
                <View
                  style={[
                    styles.input,
                    {
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: 80,
                      marginRight: 5,
                      height: 48,
                      backgroundColor: '#fff',
                    },
                  ]}>
                  <TextCp>+{state?.country?.country_code}</TextCp>
                </View>
                <View style={{flex: 1}}>
                  <Input
                    onChange={text => {
                      dispatch({type: 'SET_PHONE_NUMBER', payload: text});
                    }}
                    label={translate('phone_number')}
                    value={state.phone_number}
                    backgroundColor={'#fff'}
                  />
                </View>
              </View>
            )}

            <Input
              onChange={text => {
                dispatch({type: 'SET_TITLE', payload: text});
              }}
              label={translate('account_title')}
              value={state.title}
              backgroundColor={'#fff'}
            />

            <Input
              onChange={text => {
                dispatch({type: 'SET_OWNER', payload: text});
              }}
              label={translate('owner')}
              value={state.owner}
              backgroundColor={'#fff'}
            />

            {type === 'mobile_accounts' ? (
              <>
                <SelectInput
                  onValueChange={value => {
                    dispatch({type: 'SET_OPERATOR', payload: value});
                  }}
                  items={providers.map(item => ({
                    label: item.name,
                    value: item.name,
                  }))}
                  placeholder={{
                    label: translate('operator'),
                    value: translate('operator'),
                  }}
                  value={state.providers}
                />
                {processing && <TextCp color=""> Fetching operators</TextCp>}

                {state.operator === 'Other' && (
                  <Input
                    onChange={text => {
                      dispatch({type: 'SET_OPERATOR_OTHER', payload: text});
                    }}
                    label={translate('operator')}
                    value={state.operator_other}
                    backgroundColor={'#fff'}
                  />
                )}
              </>
            ) : (
              <>
                <Input
                  onChange={text => {
                    dispatch({type: 'SET_BANK_NAME', payload: text});
                  }}
                  label={translate('bank_name')}
                  value={state.bank_name}
                  backgroundColor={'#fff'}
                />

                <View style={styles.row}>
                  <View style={{flex: 1, marginRight: 4}}>
                    <Input
                      onChange={text => {
                        dispatch({type: 'SET_IBAN', payload: text});
                      }}
                      label={translate('iban')}
                      value={state.iban}
                      backgroundColor={'#fff'}
                    />
                  </View>
                  <View style={{flex: 1, marginLeft: 4}}>
                    <Input
                      onChange={text => {
                        dispatch({type: 'SET_BIC', payload: text});
                      }}
                      label={translate('bic')}
                      value={state.bic}
                      backgroundColor={'#fff'}
                    />
                  </View>
                </View>
              </>
            )}

            <DanaButton
              title={translate('add_account')}
              onPress={addAccount}
              theme="#282828"
              loading={loading}
            />
          </View>
        </ScrollView>
        <FlashMessage position="top" ref={myLocalFlashMessage} />
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  toolbar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    height: 56,
    alignItems: 'center',
  },
  modal: {
    backgroundColor: '#fff',
    padding: 18,
    marginTop: 20,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    width: '100%',
    height: 50,
    backgroundColor: 'rgba(0, 0, 0, 0.07)',
    borderRadius: 5,
    paddingHorizontal: 17,
    fontFamily: 'Inter-Regular',
  },
  modalHeaderText: {
    fontSize: 18,
  },
});

export default AddAccountModal;
