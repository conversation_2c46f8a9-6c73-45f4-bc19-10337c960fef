import { View, StyleSheet } from 'react-native'
import React from 'react'
import FastImage from 'react-native-fast-image';

const BackgroundImageComponent = ({ imagePath, children }) => {
  
    return (
      <FastImage
        source={imagePath}
        style={styles.backgroundImage}
        resizeMode={FastImage.resizeMode.cover}
      >
        {children}
      </FastImage>
    );
  };

  const styles = StyleSheet.create({
    backgroundImage: {
      flex: 1,
      flexDirection: 'column',
      justifyContent: 'flex-end',
    },
  });
  

export default BackgroundImageComponent;