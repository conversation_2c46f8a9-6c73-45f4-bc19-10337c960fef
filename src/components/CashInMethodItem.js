import {View, TouchableOpacity, Image, StyleSheet} from 'react-native';
import React from 'react';
import {pay_method_images} from '../images/pay_method';
import TextCp from './TextCp';
import {translate} from '../utilities/Translate';
import Icon from 'react-native-vector-icons/Entypo';
import {colors} from '../theme';

const method_text = {
  'bank card': {
    title: 'bank_card_instruction_title',
    description: 'bank_card_instruction_description',
  },
  'bank transfer': {
    title: 'bank_transfer_instruction_title',
    description: 'bank_transfer_instruction_description',
  },
};

const CashInMethodItem = ({option, saveTransfer, transfer}) => {
  const imageSrc = option.cash_in_method?.name
    .toLowerCase()
    .split(' ')
    .join('_');
  const isActiveMethod =
    transfer?.cash_in_method === option?.cash_in_method?.id;

  return (
    <TouchableOpacity
      disabled={
        option?.cash_in_method?.name == 'Instant payment' ? true : false
      }
      style={{
        ...styles.container,
        borderColor: isActiveMethod ? colors.primary : '#fff',
      }}
      testID="selectCashInMethodBtn"
      onPress={() =>
        saveTransfer({
          cash_in_method: option.cash_in_method.id,
          cash_in_method_name: option.cash_in_method.name,
        })
      }>
      <View style={styles.methodInfo}>
        <View style={styles.methodInfoInner}>
          <Image
            source={pay_method_images[imageSrc]}
            resizeMode="contain"
            style={[styles.pay_img]}
          />
          <TextCp textType="bold" style={{fontSize: 17}}>
            {translate(
              option.cash_in_method?.name.toLowerCase().split(' ').join('_'),
            )}
          </TextCp>
        </View>
        <View>
          {isActiveMethod && (
            <Icon name="controller-record" size={25} color={colors.primary} />
          )}
        </View>
      </View>
      {isActiveMethod && (
        <View style={styles.details}>
          {transfer.cash_in_method &&
            transfer?.cash_in_method_name.toLowerCase() in method_text && (
              <View style={{marginTop: 10}}>
                <TextCp>
                  {translate('max_limit')} {option.cash_in_method.max_amount}{' '}
                  EUR
                </TextCp>
                <View style={{height: 8}} />
                <TextCp color="#a3a3a3">
                  <TextCp color="#333" textStyle="bold">
                    {translate(
                      method_text[transfer?.cash_in_method_name.toLowerCase()]
                        ?.title,
                    )}{' '}
                    :
                  </TextCp>{' '}
                  {translate(
                    method_text[transfer?.cash_in_method_name.toLowerCase()]
                      ?.description,
                  )}
                </TextCp>
              </View>
            )}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
    marginVertical: 8,
    borderColor: '#fff',
    borderWidth: StyleSheet.hairlineWidth * 4,
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    borderRadius: 10,
  },
  pay_img: {
    height: 40,
    width: 40,
    marginRight: 18,
  },
  methodInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  methodInfoInner: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  details: {
    flex: 1,
  },
});

export default CashInMethodItem;
