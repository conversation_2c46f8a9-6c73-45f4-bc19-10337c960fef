import {View, TextInput, StyleSheet} from 'react-native';
import React from 'react';
import TextCp from './TextCp';

const CashInput = ({value, onChange, placeholder, style, currency}) => {
  return (
    <View style={styles.cashRow}>
      <View style={styles.cashRowLeft}>
        <TextCp style={styles.currency}>{currency}</TextCp>
      </View>
      <TextInput
        style={[styles.input, style]}
        onChangeText={text => {
          onChange(text.replace(/\s/g, ''));
        }}
        placeholder={placeholder}
        value={value}
        keyboardType="numeric"
        placeholderTextColor="#aaa"
        returnKeyType="done"
        testID={`${currency?.toLowerCase()}_input`}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  input: {
    width: '100%',
    height: 48,
    paddingHorizontal: 8,
    color: '#444',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 10,
  },
  cashRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.07)',
    borderRadius: 8,
  },
  cashRowLeft: {
    marginHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
    height: 48,
  },
  currency: {
    color: '#555',
    fontSize: 11,
  },
});

export default CashInput;
