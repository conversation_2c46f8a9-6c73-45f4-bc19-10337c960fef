import {View, Text} from 'react-native';
import React from 'react';
import useDanaStore from '../app_state/store';
import TextCp from './TextCp';
import {parseAmountCurrency} from '../utilities';

const CashLayout = ({
  value,
  fontSize = 15,
  color = '#000',
  rate = 655.957,
  localCurrancy = 'CFA',
  amountHidden = false,
}) => {
  const currency = useDanaStore(state => state.currency);
  const amountObject = parseAmountCurrency(value);

  return (
    <View>
      {currency === 'EUR' ? (
        <TextCp style={{fontSize}} color={color} textType={'bold'}>
          {amountHidden ? '*****' : amountObject?.amount} EUR
        </TextCp>
      ) : (
        <TextCp style={{fontSize}} color={color} textType={'bold'}>
          {amountHidden ? '*****' : (amountObject?.amount * rate).toFixed(2)}{' '}
          {localCurrancy}
        </TextCp>
      )}
    </View>
  );
};

export default CashLayout;
