import React from 'react';
import {View, StyleSheet, TouchableOpacity, Platform} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import TextCp from '../components/TextCp';
import {pagePadding} from '../theme';

const ContactItem = ({contactClicked, item}) => {
  const name =
    [item?.givenName, item?.familyName, item?.middleName]
      .filter(Boolean)
      .join(' ') || '';

  return (
    <TouchableOpacity
      style={[styles.contact]}
      onPress={() => contactClicked(item)}>
      <View style={{flex: 1}}>
        {name && (
          <TextCp
            style={styles.contact_details}
            textType={'bold'}
            opacity={0.8}>
            {name}
          </TextCp>
        )}
        {item?.phoneNumbers?.length > 0 && (
          <TextCp style={styles.phones} opacity={0.6}>
            <TextCp>{item?.phoneNumbers[0]?.number}</TextCp>
          </TextCp>
        )}
      </View>
      <Icon name="chevron-right" size={30} color={'#bbb'} />
    </TouchableOpacity>
  );
};

export default ContactItem;

const styles = StyleSheet.create({
  phones: {
    fontSize: 13,
  },
  contact_details: {
    fontSize: 16,
    marginBottom: 4,
    textTransform: 'capitalize',
  },
  contact: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff66',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingVertical: 16,
  },
  bullet: {
    fontSize: 30,
    marginRight: 13,
  },
  input: {
    width: '100%',
    height: 45,
    backgroundColor: 'rgba(0, 0, 0, 0.07)',
    borderRadius: 5,
    marginVertical: 5.5,
    paddingHorizontal: 17,
  },
  page: {
    paddingHorizontal: pagePadding,
    backgroundColor: '#fff',
    paddingTop: Platform.OS === 'android' ? 1 : 56,
  },
  title: {
    fontSize: 30,
  },
});
