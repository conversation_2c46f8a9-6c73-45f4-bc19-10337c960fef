import React, {useEffect} from 'react';
import {View, StyleSheet, TouchableOpacity} from 'react-native';
import TextCp from './TextCp';
import {translate, setI18nConfig} from '../utilities/Translate';
import Icon from 'react-native-vector-icons/Feather';
import {scaleRatio} from '../theme';
import useDanaStore from '../app_state/store';
import ContactListItem from './ContactListItem';
import {ScrollView} from 'react-native-gesture-handler';

const ContactList = ({contacts, goToContacts, startTransfer}) => {
  const beneficiary = useDanaStore(state => state.beneficiary);
  const removeBeneficiary = useDanaStore(state => state.removeBeneficiary);

  useEffect(() => {
    setI18nConfig();
  }, []);

  const userSelected = user => {
    const {favorite} = user;
    if (beneficiary && beneficiary?.phone_number === favorite?.phone_number) {
      removeBeneficiary();
      return;
    }
    startTransfer(user);
  };

  return (
    <>
      {contacts?.length === 0 ? (
        <View style={styles.emptyList}>
          <TouchableOpacity
            style={styles.circle}
            onPress={() => goToContacts()}>
            <Icon name="plus" style={styles.addIcon} />
          </TouchableOpacity>
          <View style={styles.emptyText}>
            <TextCp style={{color: '#757575'}} textType="light">
              {translate('no_contacts')}
            </TextCp>
          </View>
        </View>
      ) : (
        <ScrollView horizontal={true} showsHorizontalScrollIndicator={false}>
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 10,
            }}>
            <TouchableOpacity
              style={styles.circle}
              onPress={() => goToContacts()}>
              <Icon name="plus" style={styles.addIcon} />
            </TouchableOpacity>
            <TextCp style={styles.addContactText}>{translate('create')}</TextCp>
          </View>

          {contacts?.map((item, index) => {
            return (
              <ContactListItem
                key={index}
                item={item}
                beneficiary={beneficiary}
                userSelected={item => userSelected(item)}
              />
            );
          })}
        </ScrollView>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  userBox: {
    marginHorizontal: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyList: {
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  emptyText: {
    paddingHorizontal: 10,
    flex: 4,
    justifyContent: 'center',
    // alignItems: 'center',
    color: '#555',
  },
  circle: {
    height: 70 * scaleRatio,
    width: 70 * scaleRatio,
    borderRadius: 35 * scaleRatio,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  flag: {
    height: 18,
    width: 18,
    borderRadius: 9,
    position: 'absolute',
    bottom: 1,
    right: 1,
    borderWidth: 2,
    borderColor: '#fff',
  },
  addContactText: {
    fontSize: 11,
    textTransform: 'capitalize',
  },
  addIcon: {fontWeight: 'bold', color: '#eee', fontSize: 35},
});

export default ContactList;
