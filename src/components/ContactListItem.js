import {View, StyleSheet, TouchableOpacity, Image} from 'react-native';
import React, {useMemo} from 'react';
import UserAvatar from 'react-native-user-avatar';
import TextCp from './TextCp';
import {borderRadius, scaleRatio} from '../theme';
// import {flags} from '../images/flags';

const COLORS = ['#4a4e69', '#264653', '#219ebc', '#588157', '#b5838d'];

const ContactListItem = ({item, beneficiary, userSelected}) => {
  const name = useMemo(() => {
    if (item?.favorite?.is_individual) {
      return item?.favorite?.full_name;
    } else {
      return item?.favorite?.company?.name;
    }
  }, [item, beneficiary]);

        
  const isbeneficiary =
    beneficiary?.phone_number === item?.favorite?.phone_number;

  return (
    <TouchableOpacity
      style={{
        ...styles.userBox,
      }}
      onPress={() => userSelected(item)}
      testID={`user_${item?.favorite?.phone_number}`}
      accessibilityLabel={`user_${item?.favorite?.phone_number}`}>
      <View>
        <UserAvatar
          size={65 * scaleRatio}
          name={name}
          bgColor={isbeneficiary ? 'orange' : '#eee'}
          textColor={isbeneficiary ? 'white' : '#333'}
          style={{
            height: 65,
            width: 65,
            borderRadius: 40,
          }}
        />
      </View>

      <TextCp
        textType={isbeneficiary ? 'bold' : 'regular'}
        style={{
          ...styles.contactText,
          textDecorationLine: isbeneficiary ? 'underline' : 'none',
        }}>
        {name}
      </TextCp>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  contactText: {
    fontSize: 11,
    textTransform: 'capitalize',
    // marginTop: 5,
  },
  circle: {
    height: 70 * scaleRatio,
    width: 70 * scaleRatio,
    borderRadius: 35 * scaleRatio,
    backgroundColor: '#ddd',
    justifyContent: 'center',
    alignItems: 'center',
  },
  // flag: {
  //   height: 18,
  //   width: 18,
  //   borderRadius: 9,
  //   position: 'absolute',
  //   bottom: 1,
  //   right: 1,
  //   borderWidth: 2,
  //   borderColor: '#fff',
  // },
  userBox: {
    marginHorizontal: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ContactListItem;
