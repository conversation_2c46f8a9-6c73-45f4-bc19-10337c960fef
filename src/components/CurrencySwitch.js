import React from 'react';
import {StyleSheet, View} from 'react-native';
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
} from 'react-native-popup-menu';
import TextCp from './TextCp';

const CurrencySwitch = ({onPress, value, title}) => {
  return (
    <View style={styles.container}>
      <Menu>
        <MenuTrigger>
          <View style={styles.trigger}>
            <TextCp style={{fontSize: 10, marginBottom: 2}} opacity={0.7}>
              {title}
            </TextCp>
            <TextCp color="#037375" style={{fontSize: 12}} textType={'bold'}>
              {value == 'XOF' ? 'CAF' : value}
            </TextCp>
          </View>
        </MenuTrigger>
        <MenuOptions>
          <MenuOption onSelect={() => onPress(`EUR`)}>
            <TextCp style={{margin: 6}}>EUR</TextCp>
          </MenuOption>
          <MenuOption onSelect={() => onPress(`XOF`)}>
            <TextCp style={{margin: 6}}>CFA</TextCp>
          </MenuOption>
        </MenuOptions>
      </Menu>
    </View>
  );
};

const styles = StyleSheet.create({
  select: {
    flexDirection: 'row',
  },
  icon: {
    marginLeft: 8,
  },
  trigger: {
    height: 45,
    width: 45,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {width: 50, marginLeft: 30},
});

export default CurrencySwitch;
