import React, {memo} from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import {scaleRatio} from '../theme';
import TextCp from './TextCp';

const height = Dimensions.get('screen').height;

const DanaButton = ({
  onPress,
  title,
  theme = '#282828',
  loading,
  textColor = '#eee',
  icon = null,
  loadingColor = '#fff',
  testID = 'button',
  fontSize = 15,
}) => (
  <TouchableOpacity
    testID={testID}
    accessibilityLabel={testID}
    onPress={onPress}
    disabled={loading}
    style={[styles.button, {backgroundColor: theme}]}>
    {loading ? (
      <ActivityIndicator size="small" color={loadingColor} />
    ) : (
      <>
        {icon && (
          <Icon
            name={icon}
            color={textColor}
            size={16}
            style={{marginRight: 8}}
          />
        )}
        <TextCp
          color={textColor}
          style={{
            fontSize: scaleRatio * fontSize,
            textAlign: 'center',
            fontWeight: 'bold',
            textTransform: 'capitalize',
          }}>
          {title}
        </TextCp>
      </>
    )}
  </TouchableOpacity>
);
const styles = StyleSheet.create({
  button: {
    borderRadius: 10,
    elevation: 2,
    height: height * 0.065,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 11,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    flexDirection: 'row',
  },
});
export default memo(DanaButton);
