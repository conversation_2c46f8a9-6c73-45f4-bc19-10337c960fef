import React from 'react';
import {TouchableHighlight} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';

export default function DeleteButton(props) {
  return (
    <TouchableHighlight
      onPress={props.onPress}
      style={{
        width: 70,
        height: 70,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 35,
        borderWidth: 1,
        borderColor: 'rgba(0, 0, 0, 0.25)',
        backgroundColor: 'rgba(255, 255, 255, 0.5)',
        marginRight: 20,
        marginBottom: 20,
      }}>
      <Icon name="arrow-left" style={{fontSize: 30, color: 'rgba(0,0,0,.5)'}} />
    </TouchableHighlight>
  );
}
