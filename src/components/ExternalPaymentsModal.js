import {
  View,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Platform,
} from 'react-native';
import React, {useRef} from 'react';
import Heap from '@heap/react-native-heap';
import Modal from 'react-native-modal';
import {WebView} from 'react-native-webview';
import TextCp from './TextCp';
import useDanaStore from '../app_state/store';

const {height} = Dimensions.get('window');

const ExternalPaymentsModal = ({
  connect_url,
  navigation,
  show,
  setShow,
  translate,
}) => {
  const body = useDanaStore(state => state.transfer_response.body);

  const webView = useRef();

  const sendCancelEvent = () => {
    const isTransfer = body?.payment?.transaction_type || null;
    if (isTransfer) {
      Heap.track('Transfer Cancel');
      Heap.clearEventProperties();
    } else {
      Heap.track('Deposit cancelled');
      Heap.clearEventProperties();
    }
  };

  const sendSuccessEvent = () => {
    const isTransfer = body?.payment?.transaction_type || null;
  };

  const navigatorChanged = event => {
    const url = event?.url;
    // look for success in the url and if url is danapay then redirect
    if (url.includes('success') && url.includes('app.danapay')) {
      sendSuccessEvent();
      setShow(false);
      navigation.navigate('TransferSuccess', {status: 'success'});
    } else if (
      (url.includes('cancelled') ||
        url.includes('abort') ||
        url.includes('error')) &&
      url.includes('app.danapay')
    ) {
      sendCancelEvent();
      setShow(false);
      navigation.navigate('TransferSuccess', {status: 'cancelled'});
    }
  };

  return (
    <Modal
      isVisible={show}
      animationType="fade"
      statusBarTranslucent={true}
      style={{backgroundColor: 'rgba(154,155,159, .3)', margin: 0}}>
      <View style={styles.modal}>
        <View
          style={{
            padding: 10,
            justifyContent: 'flex-end',
            flexDirection: 'row',
          }}>
          <TextCp></TextCp>
          <TouchableOpacity
            style={{
              paddingVertical: 10,
              paddingHorizontal: 25,
              justifyContent: 'center',
              alignItems: 'center',
            }}
            onPress={() => {
              setShow(false);
              sendCancelEvent();
              navigation.navigate('TransferSuccess', {
                status: 'user_cancelled',
              });
            }}>
            <TextCp textType={'bold'}>{translate('Done')}</TextCp>
          </TouchableOpacity>
        </View>
        <WebView
          ref={webView}
          style={styles.webview}
          source={{
            uri: connect_url,
          }}
          onNavigationStateChange={event => navigatorChanged(event)}
          scalesPageToFit={true}
          startInLoadingState={true}
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    borderTopRightRadius: 30,
    borderTopLeftRadius: 30,
    paddingHorizontal: Platform.OS === 'ios' ? 16 : 0,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    paddingHorizontal: 26,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16,
  },
  innerContainerTra: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16,
  },
  modalTitle: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginVertical: 10,
    alignItems: 'center',
  },
  webview: {
    height: height - 100,
  },
});

export default ExternalPaymentsModal;
