import {View, Image, TouchableOpacity, StyleSheet} from 'react-native';
import React, {useMemo} from 'react';
import UserAvatar from 'react-native-user-avatar';
import Icon from 'react-native-vector-icons/Feather';
import TextCp from './TextCp';

const FavouriteListItem = ({item, flags, startTransfer}) => {
  const name = useMemo(() => {
    return item.favorite?.is_individual
      ? item.favorite?.full_name
      : item.favorite?.company?.name;
  }, [item]);

  return (
    <TouchableOpacity
      style={styles.item}
      onPress={() => startTransfer(item.favorite)}>
      <View style={{height: 56, width: 56}}>
        <UserAvatar
          size={56}
          name={name}
          bgColors={['#4a4e69', '#264653', '#219ebc', '#588157', '#b5838d']}
          textColor={'#fff'}
          src=""
          textStyle={{fontSize: 10}}
          style={styles.user}
        />
        <Image
          source={
            flags[item?.favorite['country'].toLowerCase().replace(/\s/g, '_')]
          }
          style={styles.flag}
        />
      </View>

      <View style={styles.itemDetails}>
        <TextCp style={styles.title} textType="bold" opacity={0.8}>
          {name}
        </TextCp>
        <TextCp opacity={0.6} style={{fontSize: 15}}>
          {item?.favorite?.email ||
            item?.favorite?.full_phone_number ||
            item?.favorite?.phone_number}
        </TextCp>
      </View>
      <Icon name="chevron-right" size={24} color={'#bbb'} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    // paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: '#ffffff66',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingVertical: 16,
  },
  itemDetails: {
    marginHorizontal: 14,
    flex: 1,
  },
  title: {
    fontSize: 17,
    marginBottom: 4,
    textTransform: 'capitalize',
  },
  flag: {
    height: 18,
    width: 18,
    borderRadius: 9,
    position: 'absolute',
    bottom: 1,
    right: 1,
    borderWidth: 2,
    borderColor: '#fff',
  },
});

export default FavouriteListItem;
