import {View, StyleSheet, Dimensions} from 'react-native';
import React, {forwardRef, useEffect} from 'react';
import {GooglePlacesAutocomplete} from 'react-native-google-places-autocomplete';
import Config from 'react-native-config';
import TextCp from './TextCp';
import useGetLang from '../Hooks/useGetLang';
import useDanaStore from '../app_state/store';
import {translate} from '../utilities/Translate';
import {capitalizeFirstLetter} from '../utilities';

navigator.geolocation = require('@react-native-community/geolocation');
const height = Dimensions.get('screen').height;

const GooglePlaceSearch = forwardRef(
  ({onChange, address_line, setLocationValue, error}, ref) => {
    const lang = useGetLang();
    const temp_user = useDanaStore(state => state.temp_user);
    const countries = useDanaStore(state => state.countries);

    const [country, setCountry] = React.useState(
      countries.find(val => val.country_code === temp_user?.country_code),
    );

    const hasError = error !== '';

    useEffect(() => {
      if (address_line) {
        ref.current.setAddressText(address_line);
      }
    }, [address_line]);

    return (
      <>
        <GooglePlacesAutocomplete
          ref={ref}
          placeholder={translate('search_your_location')}
          onPress={(data, details = null) => {
            onChange({data, details});
          }}
          returnKeyType={'default'}
          fetchDetails={true}
          query={{
            key: Config.GOOGLE_MAP_API_KEY,
            language: lang,
            components: 'country:' + country?.code.toLowerCase(),
          }}
          onFail={error => console.log('')}
          enablePoweredByContainer={false}
          suppressDefaultStyles={true}
          textInputProps={{
            placeholder: translate('search_your_location'),
            style: styles.input,
            placeholderTextColor: 'rgba(0,0,0,.5)',
            returnKeyType: 'done',
            fontFamily: 'Inter-Regular',
            borderColor: hasError ? '#F58F96' : 'rgba(0, 0, 0, 0.2)',
          }}
          styles={{
            textInput: styles.input,
            listView: styles.listView,
            textInputContainer: {},
          }}
          renderRow={row => (
            <View style={styles.row}>
              <TextCp style={styles.rowText}>{row.description}</TextCp>
            </View>
          )}
        />
        {hasError && (
          <TextCp color="#F58F96" style={styles.error}>
            {capitalizeFirstLetter(error)}
          </TextCp>
        )}
      </>
    );
  },
);

const styles = StyleSheet.create({
  input: {
    width: '100%',
    height: height * 0.06,
    fontFamily: 'Inter-Regular',
    borderRadius: 8,
    color: '#444',
    backgroundColor: '#fff',
    paddingHorizontal: 17,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,.2)',
  },
  listView: {
    backgroundColor: '#f9f9f9',
    marginVertical: 0,
  },
  row: {
    paddingVertical: 8,
    backgroundColor: '#fff',
    flex: 1,
    flexWrap: 'wrap',
  },
  rowText: {
    fontSize: 13,
    color: '#333',
    fontFamily: 'Inter-Regular',
  },
  error: {
    fontSize: 12,
    marginLeft: 6,
    marginBottom: 4,
  },
});

export default GooglePlaceSearch;
