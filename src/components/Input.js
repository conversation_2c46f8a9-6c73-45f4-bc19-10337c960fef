import {View, Text, TextInput, StyleSheet, Dimensions} from 'react-native';
import React from 'react';
import TextCp from './TextCp';
import {capitalizeFirstLetter} from '../utilities';

const height = Dimensions.get('screen').height;

const Input = ({
  label,
  value,
  onChange,
  error = '',
  backgroundColor = 'rgba(0,0,0,.07)',
  keyboardType = 'default',
  marginVertical = 0,
  style = {},
  showLabel = false,
  testID = 'input',
  inputMode = 'text',
}) => {
  const hasError = error !== '';

  return (
    <>
      {showLabel && <TextCp>{label}</TextCp>}
      <TextInput
        onChangeText={onChange}
        value={value}
        style={[
          styles.input,
          {
            backgroundColor,
            marginVertical,
            borderColor: hasError ? '#F58F96' : 'rgba(0, 0, 0, 0.2)',
          },
          style,
        ]}
        placeholder={label}
        keyboardType={keyboardType}
        placeholderTextColor={'rgba(0,0,0,.5)'}
        returnKeyType="done"
        testID={testID}
        accessibilityLabel={testID}
        inputMode={inputMode}
      />
      {hasError && (
        <TextCp color="#F58F96" style={styles.error}>
          {capitalizeFirstLetter(error)}
        </TextCp>
      )}
    </>
  );
};
const styles = StyleSheet.create({
  input: {
    width: '100%',
    height: height * 0.06,
    fontFamily: 'Inter-Regular',
    paddingHorizontal: 16,
    color: '#000',
    borderWidth: 1,
    borderRadius: 10,
    borderColor: 'rgba(0, 0, 0, 0.2)',
  },
  error: {
    fontSize: 12,
    marginTop: 2,
  },
});

export default Input;
