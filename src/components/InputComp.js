import {View, Text, StyleSheet, TextInput, Dimensions} from 'react-native';
import React from 'react';
import TextCp from './TextCp';
const height = Dimensions.get('screen').height;
const InputComp = ({label, onChange, value, style = {}, placeholder}) => {
  return (
    <View style={styles.inputBox}>
      {label.length > 0 && <TextCp>{label}</TextCp>}
      <TextInput
        value={value}
        style={[styles.input, style]}
        onChangeText={onChange}
        placeholder={placeholder}
        returnKeyType="next"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  inputBox: {
    marginVertical: 16,
  },
  input: {
    width: '100%',
    height: height * 0.06,
    borderRadius: 8,
    paddingHorizontal: 17,
    fontFamily: 'Inter-Regular',
    backgroundColor: '#aaa',
    color: '#444',
  },
});

export default InputComp;
