import {View, TouchableOpacity, StyleSheet, Dimensions} from 'react-native';
import React, {useEffect, useMemo} from 'react';
import {translate} from '../utilities/Translate';
import TextCp from './TextCp';
import useDanaStore from '../app_state/store';
import Icon from 'react-native-vector-icons/Ionicons';
import MIcon from 'react-native-vector-icons/MaterialCommunityIcons';

const height = Dimensions.get('screen').height;

const KycStatus = ({goToKyc}) => {
  const getUserCurrentState = useDanaStore(state => state.getUserCurrentState);
  const user = useDanaStore(state => state.user);

  const {
    kyc_submitted_at = null,
    is_active = null,
    verification_result = null,
    on_boarding_status = null,
    is_rejected = null,
    is_verified = null,
    activation_level = null,
  } = user || {};

  const bannerObject = useMemo(() => {
    if (!user) return null; // Add early
    if (!kyc_submitted_at) {
      return {
        title: 'identification_required',
        message: 'identification_required_desc',
        color: '#45AA3D',
        bg: '#F7E4CD',
        textColor: '#000000',
        cta_text: 'identification_btn_text',
        iconColor: '#F5A05B',
        icon: 'card-account-details-outline',
        cta: () => {
          goToKyc();
        },
      };
    }

    if (kyc_submitted_at && verification_result == 'RED-RETRY') {
      return {
        title: 'required_actions',
        message: 'required_actions_desc',
        color: '#45AA3D',
        bg: '#F7E4CD',
        cta_text: 'required_actions_btn_text',
        textColor: '#000000',
        iconColor: '#F5A05B',
        icon: 'warning-outline',
        cta: () => {
          goToKyc();
        },
      };
    }

    if (kyc_submitted_at && !is_active) {
      return {
        title: 'Verification_in_progress',
        message: 'Verification_in_progress_desc',
        color: '#45AA3D',
        bg: '#F7E4CD',
        cta_text: '',
        textColor: '#000000',
        iconColor: '#F5A05B',
        icon: 'time-outline',
        cta: null,
      };
    }

    if ((is_verified && !is_active) || (!is_verified && is_active)) {
      return {
        title: 'wait_a_little_longer',
        message: 'wait_a_little_longer_desc',
        color: '#45AA3D',
        bg: '#F7E4CD',
        cta_text: 'cta_text_whatsapp',
        textColor: '#000000',
        iconColor: '#F5A05B',
        icon: 'time-outline',
        cta: null,
      };
    }

    if (is_rejected) {
      return {
        title: 'verification_rejected',
        message: 'rejected',
        color: '#45AA3D',
        bg: '#F7D1CD',
        cta_text: 'cta_text_whatsapp',
        textColor: '#000000',
        iconColor: '#F55B5B',
        icon: 'close-circle-outline',
        cta: null,
      };
    }

    if (activation_level?.toLowerCase() === 'yellow') {
      return {
        title: 'restricted_account',
        message: 'restricted_account_desc',
        color: '#45AA3D',
        bg: '#F7E4CD',
        cta_text: 'cta_text_whatsapp',
        textColor: '#000000',
        iconColor: '#F5A05B',
        icon: 'warning-outline',
        cta: null,
      };
    }

    return null;
  }, [user]);

  useEffect(() => {
    getUserCurrentState();
  }, [getUserCurrentState]);

  return (
    <View>
      {bannerObject && (
        <View
          style={{
            ...styles.bg,
            backgroundColor: bannerObject?.bg,
          }}>
          <View>
            <View style={styles.row}>
              {bannerObject?.icon === 'card-account-details-outline' ? (
                <MIcon
                  name={bannerObject?.icon}
                  color={bannerObject?.iconColor}
                  size={20}
                />
              ) : (
                <Icon
                  name={bannerObject?.icon}
                  color={bannerObject?.iconColor}
                  size={20}
                />
              )}

              <TextCp
                color={bannerObject?.textColor}
                textType={'bold'}
                style={styles.header}>
                {translate(bannerObject?.title)}
              </TextCp>
            </View>

            <TextCp color={bannerObject?.textColor} style={styles.para}>
              {translate(bannerObject?.message)}
            </TextCp>
          </View>
          {bannerObject?.cta && (
            <TouchableOpacity
              testID="verifyAccountButton"
              style={{
                ...styles.button,
                backgroundColor: '#000',
              }}
              onPress={() => {
                bannerObject?.cta();
              }}>
              <TextCp color={'#fff'} textType={'bold'}>
                {translate(bannerObject?.cta_text)}
              </TextCp>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  button: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    height: height * 0.06,
    paddingHorizontal: 20,
    marginTop: 8,
  },
  bg: {
    marginBottom: 16,
    padding: 10,
    borderRadius: 12,
  },
  header: {lineHeight: 23, marginLeft: 10, fontSize: 18},
  para: {lineHeight: 18, fontSize: 13},
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
});

export default KycStatus;
