import React from 'react';
import {View, StyleSheet, TouchableOpacity} from 'react-native';
import {scaleRatio, Width} from '../theme';
import {setI18nConfig, translate} from '../utilities/Translate';
import TextCp from './TextCp';
import * as Progress from 'react-native-progress';
import Icon from 'react-native-vector-icons/Feather';
import DanaButton from '../components/DanaButton';

const Limits = (props) => {
  const {toSend, sent, limit} = props;
  React.useEffect(() => {
    setI18nConfig();
  }, []);
  return (
    <>
      <View style={{justifyContent: 'center', alignItems: 'center'}}>
        <TouchableOpacity onPress={() => props.setShowLimitsData(false)}>
          <Icon
            name="chevrons-down"
            size={35 * scaleRatio}
            color="#000"
            style={{textAlign: 'center'}}
          />
        </TouchableOpacity>
      </View>

      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          paddingVertical: 20,
        }}>
        <TextCp textType="bold" style={styles.title}>
          {translate('limitTitle1')}
        </TextCp>
        <TextCp style={styles.subTitle}>
          {translate('limitText1')} {`${toSend}€`},{translate('limitText2_one')}
          {`${limit}€`} {translate('limitText2_two')}
        </TextCp>
      </View>
      <TextCp
        textType="bold"
        style={{fontSize: 15 * scaleRatio, marginBottom: 10}}>
        {sent}€ /{limit}€
      </TextCp>
      <Progress.Bar
        progress={sent / parseInt(limit)}
        width={Width - 52}
        height={24 * scaleRatio}
        unfilledColor="#D1D1D1"
        borderRadius={0}
        borderWidth={0}
        borderColor=""
        color={'rgba(255, 0, 0, 0.75)'}
      />

      <View style={{marginVertical: 30}}>
        <DanaButton
          onPress={() => props.startVerification()}
          title={translate('start')}
          theme="#282828"
        />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: 30 * scaleRatio,
    marginBottom: 17 * scaleRatio,
    width: 273,
    textAlign: 'center',
  },
  subTitle: {
    textAlign: 'center',
    fontSize: 15 * scaleRatio,
    width: 320,
  },
});
export default Limits;
