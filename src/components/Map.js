import React from 'react';
import {View, StyleSheet, TouchableOpacity, Dimensions} from 'react-native';
import TextCp from './TextCp';
import Icon from 'react-native-vector-icons/Feather';
import {scaleRatio} from '../theme';
// import MapView, {PROVIDER_GOOGLE, Marker} from 'react-native-maps';
import RNPickerSelect from 'react-native-picker-select';

const {height, width} = Dimensions.get('screen');

export default function Map({
  setShowCashOutPoints,
  translate,
  scaleRatio,
  cash_out_points,
}) {
  const [country, setCountry] = React.useState('Mali');
  const [region, setRegion] = React.useState({
    latitude: 12.6392,
    longitude: -8.0029,
    latitudeDelta: 0.015,
    longitudeDelta: 0.0121,
  });

  const fetchCities = (value) => {
    setCountry(value);
  };

  React.useEffect(() => {
    if (cash_out_points.length > 0 && cash_out_points[0].location !== null) {
      setRegion({
        latitude: cash_out_points[0].location.latitude,
        longitude: cash_out_points[0].location.longitude,
        latitudeDelta: 0.015,
        longitudeDelta: 0.0121,
      });
    }
  }, []);

  return (
    <View style={styles.modal}>
      <View style={styles.innerContainer}>
        <View style={{justifyContent: 'flex-end', alignItems: 'flex-end'}}>
          <TouchableOpacity onPress={() => setShowCashOutPoints(false)}>
            <Icon name="x-circle" style={{fontSize: 35 * scaleRatio}}></Icon>
          </TouchableOpacity>
        </View>
        <TextCp textType="bold" style={{fontSize: 30 * scaleRatio}}>
          {translate('pick_up_location')}
        </TextCp>
        <TextCp>{translate('pick_up_location_description')}</TextCp>
        <RNPickerSelect
          placeholder={{
            label: translate('country'),
            value: translate('country'),
          }}
          Icon={() => {
            return (
              <Icon name="chevron-down" style={{fontSize: 20}} color="#aaa" />
            );
          }}
          onValueChange={(value) => {
            fetchCities(value);
          }}
          items={[{label: 'Mali', value: 'Mali'}]}
          style={{
            inputIOS: {
              fontSize: 16,
              paddingVertical: 12,
              paddingHorizontal: 17,
              color: '#000',
              paddingRight: 30,
              height: 45,
              backgroundColor: 'rgba(0, 0, 0, 0.07)',
              borderColor: 'transparent',
              borderWidth: 1,
              borderRadius: 5,
              marginBottom: 6,
              marginTop: 6,
            },
            inputAndroid: {
              fontSize: 16,
              paddingHorizontal: 17,
              paddingVertical: 8,
              color: '#000',
              paddingRight: 30,
              height: 45,
              backgroundColor: 'rgba(0, 0, 0, 0.07)',
              borderColor: 'transparent',
              borderWidth: 1,
              borderRadius: 5,
              marginBottom: 6,
              marginTop: 6,
            },
            iconContainer: {
              top: 20,
              right: 15,
            },
            placeholder: {
              color: 'rgba(0,0,0,.31)',
              fontSize: 15,
            },
          }}
          useNativeAndroidPickerStyle={false}
          textInputProps={{underlineColor: '#eee'}}
          value={country}
        />
        <View style={styles.mapContainer}>
   
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    paddingHorizontal: 26,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16 * scaleRatio,
  },
  innerContainerTra: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16,
  },
  mapContainer: {
    height: height - 300,
    width: '100%',
    backgroundColor: '#eee',
    borderRadius: 14,
    marginBottom: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
});
