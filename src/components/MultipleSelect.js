import {
  View,
  TouchableOpacity,
  StyleSheet,
  Modal,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import React, {useEffect, useRef} from 'react';
import Icon from 'react-native-vector-icons/Feather';
import TextCp from './TextCp';
import {colors} from '../theme';
import {ScrollView} from 'react-native-gesture-handler';
import {translate} from '../utilities/Translate';

const MultipleSelect = ({values, onSelected, label, countries}) => {
  const [selected, setSelected] = React.useState(values.map(val => +val) || []);
  const [selectedCountries, setSelectedCountries] = React.useState([]);
  const [show, setShow] = React.useState(false);


  useEffect(() => {
    if (selected.length > 0) {
      const selectedCountryIds = countries.filter(country =>
        selected.includes(country.id),
      );
      setSelectedCountries(selectedCountryIds);
    }
  }, [selected]);

  return (
    <>
      <TouchableOpacity style={styles.button} onPress={() => setShow(!show)}>
        <View style={styles.left}>
          {selectedCountries.length === 0 && (
            <TextCp
              style={{...styles.labelText, paddingHorizontal: 10}}
              opacity={0.5}>
              {translate('select_countries')}
            </TextCp>
          )}
          {selectedCountries.map(value => (
            <View style={styles.pill}>
              <TextCp key={value.id} style={styles.labelText} color="#fff">
                {value.name}
              </TextCp>
            </View>
          ))}
        </View>
        <View style={styles.right}>
          <Icon name="chevron-down" size={24} color="black" />
        </View>
      </TouchableOpacity>
      <Modal visible={show} animationType="slide">
        <StatusBar barStyle="dark-content" backgroundColor="#fff" />
        <SafeAreaView style={styles.modal}>
          <View style={styles.header}>
            <View></View>
            <TouchableOpacity
              style={{
                ...styles.saveBtn,
                paddingVertical: 10,
                paddingHorizontal: 20,
                backgroundColor: '#eee',
              }}
              onPress={() => {
                onSelected(selected);
                setShow(false);
              }}>
              <TextCp style={{fontWeight: 'bold'}} color="teal">
                {translate('done')}
              </TextCp>
            </TouchableOpacity>
          </View>
          <View>
            <TouchableOpacity
              style={{
                ...styles.listItem,
                borderTopWidth: 0.7,
                borderTopColor: '#eee',
              }}
              onPress={() => {
                if (selected.length === countries.length) {
                  setSelected([]);
                } else {
                  setSelected(countries.map(country => country.id));
                }
              }}>
              <TextCp>
                {selected.length === countries.length
                  ? translate('clear_all')
                  : translate('select_all')}
              </TextCp>
            </TouchableOpacity>
          </View>
          <ScrollView>
            {countries.map(country => (
              <TouchableOpacity
                style={styles.listItem}
                key={country.id}
                onPress={() => {
                  if (selected.includes(country.id)) {
                    setSelected(selected.filter(item => item !== country.id));
                  } else {
                    setSelected([...selected, country.id]);
                  }
                }}>
                {selected.includes(country.id) ? (
                  <View
                    style={[
                      styles.checkBox,
                      {backgroundColor: colors.primary},
                    ]}>
                    <Icon name="check" size={16} color="#fff" />
                  </View>
                ) : (
                  <View style={styles.checkBox}></View>
                )}
                <TextCp>{country.name}</TextCp>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  labelText: {
    fontSize: 13,
    borderRadius: 5,
  },
  pill: {
    paddingHorizontal: 10,
    paddingVertical: 3,
    marginHorizontal: 2,
    backgroundColor: colors.primary,
    borderRadius: 20,
    marginVertical: 2,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    marginVertical: 8,
    backgroundColor: '#fff',
    borderRadius: 10,
    minHeight: 50,
    padding: 4,
  },
  left: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  right: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  checkBox: {
    width: 20,
    height: 20,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ddd',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 16,
    borderBottomWidth: 0.7,
    borderBottomColor: '#eee',
  },
  saveBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginBottom: 10,
    borderRadius: 10,
  },
  modal: {flex: 1, backgroundColor: '#fff'},
});

export default MultipleSelect;
