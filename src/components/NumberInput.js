import {View, Text, StyleSheet, TextInput} from 'react-native';
import React from 'react';
import TextCp from './TextCp';

const NumberInput = ({label, onChange, value, style={}, placeholder}) => {
  return (
    <View>
      {label.length > 0 && <TextCp>{label}</TextCp>}
      <TextInput
        value={value}
        style={[styles.input, style]}
        onChangeText={onChange}
        keyboardType="phone-pad"
        placeholder={placeholder}
        returnKeyType="done"
        testID={label}
        accessibilityLabel={label}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  inputBox: {
    marginVertical: 16,
  },
  label: {},
  input: {
    width: '100%',
    height: 56,
    borderRadius: 8,
    paddingHorizontal: 17,
    fontFamily: 'Inter-Bold',
    backgroundColor: '#aaa',
  },
});

export default NumberInput;
