import {View} from 'react-native';
import React, {useCallback} from 'react';
import OtpInputs from 'react-native-otp-inputs';

const OTPInput = ({onChange, testID}) => {
  const handleChange = useCallback((code) => {
    onChange(code);
  }, [onChange]);

  return (
    <View>
      <OtpInputs
        testIDPrefix={testID}
        handleChange={handleChange}
        numberOfInputs={6}
        inputStyles={{
          fontFamily: 'Inter-Bold',
          fontSize: 20,
          width: 50,
          height: 50,
          justifyContent: 'center',
          alignItems: 'center',
          borderBottomColor: '#333',
          borderBottomWidth: 1,
          paddingHorizontal: 18,
          color: '#282828',
        }}
        inputContainerStyles={{
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: 4,
          color: '#282828',
        }}
      />
    </View>
  );
};

export default React.memo(OTPInput);
