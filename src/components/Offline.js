import {StyleSheet, View} from 'react-native';
import React, {useEffect} from 'react';
import {useNetInfo} from '@react-native-community/netinfo';
import Icon from 'react-native-vector-icons/Ionicons';
import {setI18nConfig, translate} from '../utilities/Translate';
import TextCp from './TextCp';

const Offline = () => {
  const netInfo = useNetInfo();

  const isOffline =
    netInfo.type !== 'unknown' && netInfo.isInternetReachable === false;

  useEffect(() => {
    setI18nConfig();
  }, []);

  if (isOffline) {
    return (
      <View style={styles.container}>
        <Icon
          name="wifi-outline"
          size={100}
          color="#F57A89"
          style={{marginBottom: 10}}
        />
        <TextCp
          style={{fontSize: 20}}
          align="center"
          color="#037375"
          textType="bold">
          {translate('No_Internet_Connection')}
        </TextCp>
        <TextCp style={styles.text} type="bold" align="center" color={'#333'}>
          {translate('No_Internet_Connection_Note')}
        </TextCp>
      </View>
    );
  }

  return null;
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
    backgroundColor: '#f1f1f1',
    padding: 20,
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  },
  text: {
    fontSize: 16,
    marginTop: 10,
  },
});

export default Offline;
