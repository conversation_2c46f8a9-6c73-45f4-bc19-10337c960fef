import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Image,
  Platform,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import {setI18nConfig, translate} from '../utilities/Translate';
import TextCp from './TextCp';
import {flags} from '../images/flags';

const height = Dimensions.get('screen').height;

const PhoneInput = props => {
  useEffect(() => {
    setI18nConfig();
  }, [props]);

  return (
    <View style={styles.phoneInput}>
      {props.detecting ? (
        <View
          style={{...styles.country, paddingHorizontal: 16, marginRight: 8}}>
          <ActivityIndicator size="small" color="#666" />
        </View>
      ) : (
        <TouchableOpacity
          style={styles.country}
          accessibilityLabel={'countrySelect'}
          onPress={() => props.openModal()}>
          <Image
            source={
              flags[props?.country?.name?.toLowerCase()?.replace(/\s/g, '_')]
            }
            style={styles.flag}
          />
          <TextCp textType="bold" style={styles.country_code}>
            +{props?.country?.country_code}
          </TextCp>
        </TouchableOpacity>
      )}

      <View style={styles.number}>
        <TextInput
          style={[styles.input]}
          onChangeText={text => {
            props.setPhone(text);
          }}
          placeholder={translate('PhoneNumber')}
          value={props.phone}
          keyboardType="number-pad"
          returnKeyType="done"
          accessibilityLabel="phoneInput"
          testID="phoneInput"
          placeholderTextColor="rgba(0,0,0,.3)"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  phoneInput: {
    flexDirection: 'row',
  },
  country: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 8,
  },
  number: {flex: 1, paddingLeft: 2},
  flag: {
    height: height * 0.05,
    width: height * 0.05,
    // marginRight: 8,
  },
  input: {
    width: '100%',
    height: height * 0.065,
    marginVertical: 4,
    fontSize: 17,
    color: '#222',
    paddingHorizontal: 16,
    fontFamily: Platform.OS === 'ios' ? 'Inter-Bold' : 'Lato-Bold',
    borderWidth: 1,
    borderRadius: 8,
    borderColor: 'rgba(0, 0, 0, 0.2)',
  },
  country_code: {
    fontSize: 17,
    color: '#555',
    marginLeft: 8,
    marginRight: 8,
  },
});
export default PhoneInput;
