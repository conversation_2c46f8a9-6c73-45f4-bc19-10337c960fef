import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import TextCp from './TextCp';
import {colors, scaleRatio} from '../theme';
import {HeapIgnore} from '@heap/react-native-heap';

const width = Dimensions.get('screen').width;

const BUTTON_SIZE = (width - 64) / 3;

const PinCode = ({
  titleText,
  subTitleText,
  pinFinished,
  loading = false,
  loadingText,
  biometricMatch = null,
  checkingBio = false,
}) => {
  const [pinCode, setPinCode] = useState([]);

  const handleBtnClick = number => {
    setPinCode(prev => {
      if (prev.length === 4) {
        return prev;
      }
      return [...prev, number];
    });
    if (pinCode.length === 3) {
      pinFinished([...pinCode, number]);
    }
  };

  const removeBtn = () => {
    if (loading) return;
    if (pinCode.length > 0) {
      const current = [...pinCode];
      current.pop();
      setPinCode(current);
    }
  };

  return (
    <View>
      <View style={styles.header}>
        <TextCp style={styles.title}>{titleText}</TextCp>
        <TextCp style={styles.subTitle} align="center">
          {subTitleText}
        </TextCp>
      </View>

      {loading ? (
        <View style={styles.input}>
          <ActivityIndicator
            color="#282828"
            size="small"
            style={styles.loadingStateSpinner}
          />
          <TextCp style={styles.loadingStateText}>{loadingText}</TextCp>
        </View>
      ) : (
        <>
          {pinCode.length === 0 ? (
            <View style={styles.input}>
              <Text style={[styles.bullet, {color: '#aaa'}]}>{'\u2B24'}</Text>
              <Text style={[styles.bullet, {color: '#aaa'}]}>{'\u2B24'}</Text>
              <Text style={[styles.bullet, {color: '#aaa'}]}>{'\u2B24'}</Text>
              <Text style={[styles.bullet, {color: '#aaa'}]}>{'\u2B24'}</Text>
            </View>
          ) : (
            <View style={styles.input}>
              {pinCode.map((val, index) => (
                <Text key={index} style={styles.bullet}>
                  {'\u2B24'}
                </Text>
              ))}
            </View>
          )}
        </>
      )}
      <HeapIgnore>
        <View style={styles.keyPad}>
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 'biometric', 0, 'delete'].map(
            (val, index) => (
              <View style={styles.buttonBox} key={index}>
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 0].includes(val) && (
                  <TouchableOpacity
                    testID={`pinBtn_${val}`}
                    accessibilityLabel={`pinBtn_${val}`}
                    style={styles.button}
                    onPress={() => handleBtnClick(val)}>
                    <TextCp textType="bold" style={styles.buttonText}>
                      {val}
                    </TextCp>
                  </TouchableOpacity>
                )}
                {val === 'biometric' && (
                  <>
                    {biometricMatch && (
                      <TouchableOpacity
                        onPress={biometricMatch}
                        style={{
                          ...styles.button,
                        }}>
                        {checkingBio ? (
                          <ActivityIndicator
                            size="small"
                            color={colors.primary}
                          />
                        ) : (
                          <MaterialCommunityIcons
                            name={'face-recognition'}
                            size={20}
                            style={{color: 'rgba(0,0,0,.5)'}}
                          />
                        )}
                      </TouchableOpacity>
                    )}
                  </>
                )}
                {val === 'delete' && (
                  <TouchableOpacity
                    testID={`pinBtn_delete`}
                    accessibilityLabel={`pinBtn_delete`}
                    style={styles.button}
                    onPress={() => removeBtn()}>
                    <Icon
                      name={val}
                      size={30}
                      style={{color: 'rgba(0,0,0,.5)'}}
                    />
                  </TouchableOpacity>
                )}
              </View>
            ),
          )}
        </View>
      </HeapIgnore>
    </View>
  );
};

const styles = StyleSheet.create({
  input: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingBottom: Platform.OS === 'ios' ? 40 : 30,
  },
  header: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: Platform.OS === 'ios' ? 32 : 20,
  },
  subTitle: {
    fontSize: 16,
    textAlign: 'center',
  },
  title: {
    fontSize: 22,
    color: '#000',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  bullet: {
    fontSize: 14,
    marginRight: 10,
    color: '#027375',
  },
  buttonBox: {
    width: BUTTON_SIZE,
    height: BUTTON_SIZE,
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    width: BUTTON_SIZE / 1.4,
    height: BUTTON_SIZE / 1.4,
    borderRadius: BUTTON_SIZE,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,.25)',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 8,
  },
  buttonBlank: {
    width: 80,
    height: 80,
    borderRadius: 45,
    margin: 10,
  },
  buttonText: {
    color: 'rgba(0,0,0,.5)',
    fontSize: 30 * scaleRatio,
    // fontWeight: 'bold',
  },
  keyPad: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    alignItems: 'center',
    // backgroundColor: 'red',
  },

  loadingState: {
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingStateText: {
    marginBottom: 10,
    textAlign: 'center',
  },
  loadingStateSpinner: {
    marginBottom: 10,
    marginHorizontal: 16,
  },
});
export default PinCode;
