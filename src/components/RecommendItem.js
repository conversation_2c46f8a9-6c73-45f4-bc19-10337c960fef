import React, {useEffect} from 'react';
import {View, Image} from 'react-native';
import TextCp from './TextCp';
import {TouchableOpacity} from 'react-native-gesture-handler';
import {scaleRatio} from '../theme';
import UserAvatar from 'react-native-user-avatar';
import {translate, setI18nConfig} from '../utilities/Translate';

const RecommendItem = ({value}) => {
  useEffect(() => {
    setI18nConfig();
  }, []);
  return (
    <TouchableOpacity
      style={{flexDirection: 'row', marginVertical: 20 * scaleRatio}}>
      <View style={{flexDirection: 'row', alignItems: 'center', flexGrow: 1}}>
        {value.data && value.data.pic ? (
          <Image
            source={require('../images/user?.jpg')}
            style={{
              height: 60 * scaleRatio,
              width: 60 * scaleRatio,
              borderRadius: 28 * scaleRatio,
            }}
          />
        ) : (
          <UserAvatar
            size={55 * scaleRatio}
            name={value && value.name}
            bgColors={['#ccaabb', '#8ecae6', '#d5bdaf', '#4a4e69']}
            textColor={'#6a91ad'}
            src=""
          />
        )}
        <View style={{marginLeft: 5}}>
          <TextCp textType="medium" style={{fontSize: 16 * scaleRatio}}>
            {value && value.name}
          </TextCp>
          <TextCp
            style={{
              fontSize: 15 * scaleRatio,
              color: 'rgba(0, 0, 0, 0.5)',
            }}>
            {value.created_at}
          </TextCp>
        </View>
      </View>
      <View style={{flex: 1, alignItems: 'flex-end', justifyContent: 'center'}}>
        <TextCp
          textType="bold"
          style={{
            fontSize: 15 * scaleRatio,
            textAlign: 'right',
          }}>
          {translate('failed')}
        </TextCp>
      </View>
    </TouchableOpacity>
  );
};

export default RecommendItem;
