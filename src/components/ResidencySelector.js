import {View, StyleSheet,TouchableOpacity, } from 'react-native';
import React from 'react';
import TextCp from './TextCp';
import { translate } from '../utilities/Translate';
import { Icon } from 'react-native-vector-icons/Icon';

const ResidencySelector = ({item, selectedResidency, setSelectedResidency}) => {
  return (
    <View style={styles.menuItem}>
      <TouchableOpacity
        onPress={() => setSelectedResidency(item)}
        style={styles.menuItemRow}>
        <View
          style={[
            styles.menuItemRowBtn,
            {
              backgroundColor:
                item.value === selectedResidency?.value ? '#037375' : '#eee',
            },
          ]}>
          <Icon
            name="check"
            size={15}
            color={item.value === selectedResidency?.value ? '#fff' : '#aaa'}
          />
        </View>
        <View style={{flex: 1}}>
          <TextCp>{translate(item.label)}</TextCp>
        </View>
      </TouchableOpacity>
      {item.value === selectedResidency?.value && (
        <View style={styles.menuItemRowDetails}>
          <TextCp color="#aaa">{translate(item.description)}</TextCp>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  menuItemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#ccc',
    paddingHorizontal: 10,
    borderRadius: 6,
    minHeight: 70,
    // backgroundColor: 'red',
  },
  menuItemRowBtn: {
    height: 25,
    justifyContent: 'center',
    alignItems: 'center',
    width: 25,
    marginHorizontal: 10,
    borderRadius: 4,
  },
  menuItemRowDetails: {
    marginVertical: 10,
  },
});

export default ResidencySelector;
