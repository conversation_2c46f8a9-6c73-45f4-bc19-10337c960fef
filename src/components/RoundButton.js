import React from 'react';
import {TouchableHighlight} from 'react-native';
import TextCp from './TextCp';
import {scaleRatio} from '../theme';

const RoundButton = ({number, onPress}) => {
  return (
    <TouchableHighlight
      onPress={onPress}
      style={{
        width: 70,
        height: 70,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 35,
        borderWidth: 1,
        borderColor: 'rgba(0, 0, 0, 0.25)',
        backgroundColor: 'rgba(255, 255, 255, 0.5)',
        marginRight: 20,
        marginBottom: 20,
      }}>
      <TextCp
        textType="bold"
        style={{fontSize: 30 * scaleRatio, color: '#666'}}>
        {number}
      </TextCp>
    </TouchableHighlight>
  );
};

export default RoundButton;
