import {View, StyleSheet, Platform, Dimensions} from 'react-native';
import React from 'react';
import RNPickerSelect from 'react-native-picker-select';
import Dropdown from 'react-native-input-select';
import Icon from 'react-native-vector-icons/Feather';
import {capitalizeFirstLetter} from '../utilities';
import TextCp from './TextCp';
import {translate} from '../utilities/Translate';

const height = Dimensions.get('screen').height;

const SelectInput = ({
  items,
  value,
  onValueChange,
  placeholder,
  label,
  testID = 'selectInput',
  isMultiple = false,
  error = '',
}) => {
  const hasError = error !== '';

  return (
    <>
      {isMultiple ? (
        <Dropdown
          label={label}
          placeholder={placeholder}
          options={items}
          selectedValue={value}
          onValueChange={val => {
            onValueChange(val);
          }}
          primaryColor={'teal'}
          isMultiple
          isSearchable
          dropdownStyle={{
            borderWidth: 1,
            borderColor: hasError ? '#F58F96' : 'rgba(0, 0, 0, 0.2)',
            borderRadius: 8,
            // padding: 8,s
          }}
          labelStyle={{
            color: '#000',
            fontFamily: 'Inter-Regular',
          }}
          dropdownHelperTextStyle={{
            color: 'teal',
            fontWeight: '900',
            fontFamily: 'Inter-Regular',
          }}
          checkboxControls={{
            checkboxLabelStyle: {
              color: '#555',
              fontSize: 15,
              fontFamily: 'Inter-Regular',
            },
            checkboxUnselectedColor: 'white',
          }}
          listControls={{
            selectAllText: translate('select_all'),
            unselectAllText: translate('clear_all'),
          }}
          placeholderStyle={{
            fontFamily: 'Inter-Regular',
          }}
          searchControls={{
            textInputStyle: {
              fontWeight: '500',
              paddingHorizontal: 10,
              width: '100%',
              color: '#444',
              fontFamily: 'Inter-Regular',
              height: height * 0.06,
            },
            textInputContainerStyle: {
              justifyContent: 'center',
              alignItems: 'center',
              color: '#444',
              // height: 53,
              height: height * 0.06,
            },
            textInputProps: {
              placeholder: translate('search_for_country'),
              placeholderTextColor: '#666',
            },
          }}
        />
      ) : (
        <>
          {label?.length > 0 && <TextCp>{label}</TextCp>}
          <RNPickerSelect
            placeholder={placeholder}
            value={value}
            Icon={() => {
              return (
                <Icon name="chevron-down" style={{fontSize: 20}} color="#aaa" />
              );
            }}
            onValueChange={val => {
              if (!val) {
                return;
              }
              onValueChange(val);
            }}
            items={items}
            style={{
              ...pickerSelectStyles,
              iconContainer: styles.iconContainer,
              placeholder: styles.placeholder,
              inputIOS: {
                ...pickerSelectStyles.inputIOS,
                borderColor: hasError ? '#F58F96' : 'rgba(0, 0, 0, 0.2)',
              },
              inputAndroid: {
                ...pickerSelectStyles.inputAndroid,
                borderColor: hasError ? '#F58F96' : 'rgba(0, 0, 0, 0.2)',
              },
            }}
            useNativeAndroidPickerStyle={false}
            textInputProps={{underlineColor: 'yellow'}}
          />
        </>
      )}
      {hasError && (
        <TextCp color="#F58F96" style={styles.error}>
          {capitalizeFirstLetter(error)}
        </TextCp>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  iconContainer: {
    top: Platform.OS === 'ios' ? 22 : 22,
    right: 16,
  },
  placeholder: {
    color: '#aaa',
    fontSize: 15,
  },
  error: {
    fontSize: 12,
  },
});

const pickerSelectStyles = StyleSheet.create({
  inputIOS: {
    paddingHorizontal: 16,
    color: '#444',
    paddingRight: 16, // to ensure the text is never behind the icon
    height: height * 0.06,
    backgroundColor: '#fff',
    borderRadius: 10,
    marginVertical: 8,
    fontFamily: 'Inter-Regular',
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.2)',
  },
  inputAndroid: {
    paddingHorizontal: 16,
    color: '#444',
    paddingRight: 30, // to ensure the text is never behind the icon
    height: height * 0.06,
    backgroundColor: '#fff',
    borderRadius: 10,
    marginVertical: 8,
    fontFamily: 'Inter-Regular',
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.2)',
  },
});
export default SelectInput;
