import React from 'react';
import {View, Text} from 'react-native';

export default function Status({status, statusText}) {
  return (
    <View style={styles.statusItem}>
      <Text
        style={[
          styles.bullet,
          {
            color: transfer_status === 'completed' ? '#8AC926' : null,
          },
        ]}>
        {'\u2B24'}
      </Text>
      <TextCp
        style={{
          color: transfer_status === 'completed' ? '#8AC926' : null,
        }}>
        {translate(statusText)}
      </TextCp>
    </View>
  );
}
