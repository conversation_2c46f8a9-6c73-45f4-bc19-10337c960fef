import React, {useEffect} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import TextCp from './TextCp';
import {translate, setI18nConfig} from '../utilities/Translate';
import {scaleRatio} from '../theme';

export default function StatusTracker(props) {
  const {status, transfer} = props.transfer;

  const payment_status = status;
  let cashout_status = 'pending';
  let transfer_status = 'pending';

  try {
    transfer_status = transfer.status;
    cashout_status = transfer.cashout_status;
  } catch (error) {}

  useEffect(() => {
    setI18nConfig();
  });
  return (
    <View>
      <View style={styles.statusItem}>
        <Text style={[styles.bullet, {color: '#8AC926'}]}>{'\u2B24'}</Text>
        <TextCp style={{color: '#8AC926'}}>{translate('Status_one')}</TextCp>
      </View>

      <View style={styles.statusItem}>
        <Text
          style={[
            styles.bullet,
            {
              color:
                payment_status === 'started' || payment_status === 'completed'
                  ? '#8AC926'
                  : '#555',
            },
          ]}>
          {'\u2B24'}
        </Text>
        <TextCp
          style={{
            color:
              payment_status === 'started' || payment_status === 'completed'
                ? '#8AC926'
                : '#555',
          }}>
          {translate('Status_two')}
        </TextCp>
      </View>

      {payment_status === 'failed' ? (
        <View style={styles.statusItem}>
          <Text
            style={[
              styles.bullet,
              {
                color: payment_status === 'failed' ? 'red' : '#555',
              },
            ]}>
            {'\u2B24'}
          </Text>
          <TextCp
            style={{
              color: payment_status === 'failed' ? 'red' : '#555',
            }}>
            {translate('Status_seven')}
          </TextCp>
        </View>
      ) : null}

      {transfer_status === 'failed' ? (
        <View style={styles.statusItem}>
          <Text
            style={[
              styles.bullet,
              {
                color: payment_status === 'failed' ? 'red' : '#555',
              },
            ]}>
            {'\u2B24'}
          </Text>
          <TextCp
            style={{
              color: payment_status === 'failed' ? 'red' : '#555',
            }}>
            {translate('Status8')}
          </TextCp>
        </View>
      ) : null}

      <View style={styles.statusItem}>
        <Text
          style={[
            styles.bullet,
            {
              color:
                transfer_status === 'started' || transfer_status === 'completed'
                  ? '#8AC926'
                  : '#555',
            },
          ]}>
          {'\u2B24'}
        </Text>
        <TextCp
          style={{
            color:
              transfer_status === 'started' || transfer_status === 'completed'
                ? '#8AC926'
                : '#555',
          }}>
          {translate('Status_three')}
        </TextCp>
      </View>

      <View style={styles.statusItem}>
        <Text
          style={[
            styles.bullet,
            {
              color: transfer_status === 'completed' ? '#8AC926' : '#555',
            },
          ]}>
          {'\u2B24'}
        </Text>
        <TextCp
          style={{
            color: transfer_status === 'completed' ? '#8AC926' : '#555',
          }}>
          {translate('Status_four')}
        </TextCp>
      </View>

      <View style={styles.statusItem}>
        <Text
          style={[
            styles.bullet,
            {
              color: transfer_status === 'completed' ? '#8AC926' : '#555',
            },
          ]}>
          {'\u2B24'}
        </Text>
        <TextCp
          style={{
            color: transfer_status === 'completed' ? '#8AC926' : '#555',
          }}>
          {translate('Status_five')}
        </TextCp>
      </View>

      <View style={styles.statusItem}>
        <Text
          style={[
            styles.bullet,
            {color: cashout_status === 'completed' ? '#8AC926' : '#555'},
          ]}>
          {'\u2B24'}
        </Text>
        <TextCp
          style={{color: cashout_status === 'completed' ? '#8AC926' : '#555'}}>
          {translate('Status_six')}
        </TextCp>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  statusItem: {
    marginBottom: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  bullet: {
    fontSize: 24 * scaleRatio,
    marginRight: 10 * scaleRatio,
  },
});
