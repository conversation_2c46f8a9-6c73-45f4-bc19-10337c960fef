import React from 'react';
import {Text, StyleSheet, Platform} from 'react-native';

const TextCp = ({
  children,
  textType,
  style = {},
  opacity = 1,
  color = '#282828',
  align = 'left',
  testID = {children},
}) => {
  let textStyle = {};
  switch (textType) {
    case 'regular':
      textStyle = styles.regular;
      break;
    case 'bold':
      textStyle = styles.bold;
      break;
    case 'light':
      textStyle = styles.light;
      break;
    case 'italic':
      textStyle = styles.italic;
      break;
    case 'semiBold':
      textStyle = styles.semiBold;
      break;
    case 'medium':
      textStyle = styles.medium;
      break;
    case 'merriweather':
      textStyle = styles.merriweather;
      break;
    default:
      textStyle = styles.regular;
      break;
  }
  return (
    <Text style={[textStyle, {...style, opacity, color, textAlign: align}]}>
      {children}
    </Text>
  );
};

const styles = StyleSheet.create({
  regular: {
    fontFamily: 'Inter-Regular',
  },
  bold: {
    fontFamily: 'Inter-Bold',
    fontWeight: 'bold',
  },
  light: {
    fontFamily: 'Inter-Light',
  },
  lighter: {
    fontFamily: 'Inter-Thin',
  },
  semiBold: {
    fontFamily: 'Inter-SemiBold',
    fontWeight: 'bold',
  },
  medium: {
    fontFamily: 'Inter-Medium',
  },
  merriweather: {
    fontFamily: 'Merriweather-Bold',
  },
});

export default TextCp;
