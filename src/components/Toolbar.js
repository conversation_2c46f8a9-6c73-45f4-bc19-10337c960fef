import React from 'react';
import {StyleSheet, TouchableOpacity, Platform} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import {scaleRatio} from '../theme';

const Toolbar = (props) => {
  return (
    <TouchableOpacity
      style={[styles.tool, {height: props.height}]}
      onPress={() => props.goBack()}>
      <Icon name="x-circle" style={styles.icon} color="#444"></Icon>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  tool: {
    justifyContent: 'flex-end',
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 10,
  },
  icon: {
    fontSize: 35 * scaleRatio,
  },
});
export default Toolbar;
