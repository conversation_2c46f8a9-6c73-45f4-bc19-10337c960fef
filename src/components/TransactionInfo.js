import React, {useEffect} from 'react';
import {View, TouchableOpacity, StyleSheet} from 'react-native';
import {translate, setI18nConfig} from '../utilities/Translate';
import TextCp from './TextCp';
import Button from './Button';

const TransactionInfo = (props) => {
  const steps = [
    {
      name: translate('Status_one'),
    },
    {
      name: translate('Status_two'),
    },
    {
      name: translate('Status_three'),
    },
    {
      name: translate('Status_four'),
    },
    {
      name: translate('Status_five'),
    },
    {
      name: translate('Status_six'),
    },
  ];
  useEffect(() => {
    setI18nConfig();
  }, []);

  return (
    <View style={styles.transferContainer}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => props.closeModal()}
          style={styles.closeBtn}>
          <TextCp>Close</TextCp>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => props.downloadTransaction()}
          style={styles.closeBtn}>
          <TextCp>Download</TextCp>
        </TouchableOpacity>
      </View>

      <TextCp style={styles.date}>12/12/2020 10:05</TextCp>

      <View style={styles.userInfo}>
        <TextCp style={styles.title}>{translate('Beneficiary')}</TextCp>
        <TextCp textType="regular">Mohamed CISSOKO</TextCp>
        <TextCp>+223 54 56 65 66</TextCp>
        <TextCp>Mail > Bamako > Magnambougou</TextCp>
      </View>

      <View style={styles.bottom}>
        <View style={styles.left}>
          <TextCp style={styles.greyText}>{translate('amount')}</TextCp>
          <TextCp textType="bold" style={{fontSize: 23}}>
            1000€{' '}
          </TextCp>
          <TextCp textType="lighter" style={styles.greyText}>
            655.957 FCfa
          </TextCp>
          <TextCp textType="lighter" style={styles.greyText}>
            655.957.000K Danacoins
          </TextCp>
        </View>
        <View style={styles.right}>
          <TextCp style={styles.greyText}>{translate('fees')}</TextCp>
          <TextCp textType="bold" style={{fontSize: 23}}>
            2.5€{' '}
          </TextCp>
          <TextCp textType="lighter" style={styles.greyText}>
            {translate('delivery')} :{translate('yes')}
          </TextCp>
        </View>
      </View>
      <View style={styles.statuses}>
        <TextCp style={styles.title}>{translate('transaction_status')}</TextCp>
        {steps.map((value) => {
          return (
            <View style={styles.listItem} key={value.name}>
              <TextCp>{value.name}</TextCp>
            </View>
          );
        })}
      </View>

      <Button
        title={translate('repeat')}
        onPress={() => props.repeatTransaction()}
        theme="blue"
      />
    </View>
  );
};
const styles = StyleSheet.create({
  transferContainer: {
    backgroundColor: '#f1f1f1',
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    padding: 26,
  },
  bottom: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    shadowColor: '#666',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 2,
    padding: 16,
    borderRadius: 15,
  },
  left: {
    flex: 1,
  },
  right: {
    flex: 1,
  },
  middle: {
    paddingVertical: 3,
    marginBottom: 10,
  },
  greyText: {
    fontSize: 10,
    color: '#666',
    marginBottom: 5,
  },
  closeBtn: {
    marginBottom: 10,
  },
  date: {
    marginBottom: 10,
    fontSize: 10,
  },

  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  userInfo: {
    shadowColor: '#666',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 2,
    marginVertical: 10,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 15,
  },
  statuses: {
    shadowColor: '#666',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 2,
    marginVertical: 10,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 15,
    marginBottom: 20,
  },
  title: {
    fontSize: 13,
    color: '#555',
    marginBottom: 10,
    textTransform: 'capitalize',
  },
  listItem: {
    marginBottom: 4,
  },
});
export default TransactionInfo;
