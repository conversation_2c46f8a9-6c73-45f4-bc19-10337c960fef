import React, {useEffect} from 'react';
import {View, StyleSheet, TouchableOpacity} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import TextCp from './TextCp';
import {convertToUserTime} from '../utilities';
import useDanaStore from '../app_state/store';
import CashLayout from './CashLayout';
import useGetLang from '../Hooks/useGetLang';
import {ots} from '../transfer_data';

const statuses = {
  failed: {color: '#F00505', background: '#F7D1CD'},
  error: {color: '#F00505', background: '#F7D1CD'},
  aborted: {color: '#F00505', background: '#F7D1CD'},
  'in progress': {color: '#ED6307', background: 'rgba(229,146,21,.1)'},
  'pending': {color: '#ED6307', background: 'rgba(229,146,21,.1)'},
  completed: {color: '#14AD00', background: '#34E00010'},
  cancelled: {color: '#F00505', background: '#F7D1CD'},
  'action required': {color: '#F00505', background: '#F7D1CD'},
  'payment aborted': {color: '#F00505', background: '#F7D1CD'},
};

const TransactionItem = ({value, openOperationModal}) => {

  const lang = useGetLang();
  const setSelelctedTransfer = useDanaStore(
    state => state.setSelelctedTransfer,
  );

  const isSender = value.operation_direction?.toLowerCase() === 'sending';

  const viewTransactionProfile = () => {
    setSelelctedTransfer(null);
    setSelelctedTransfer(value);
    openOperationModal();
  };

  const icon = value.operation_type?.toLowerCase().replace(' ', '_');
  const status = value.transfer_status?.toLowerCase().replace(' ', '_');

  return (
    <TouchableOpacity
      testID={`transaction_${value.id}`}
      accessibilityLabel={`transaction_${value.id}`}
      onPress={viewTransactionProfile}
      style={styles.itemContainer}>
      <View style={styles.itemLeft}>
        <View style={{...styles.iconBox, backgroundColor: ots[icon]?.bgColor}}>
          <Icon
            name={ots[icon]?.icon}
            size={24}
            style={styles.icon}
            color={ots[icon]?.iconColor}
          />
        </View>
        <View>
          <TextCp
            style={{textTransform: 'capitalize', fontSize: 15}}
            textType={'medium'}>
            {isSender ? value.receiver : value.sender}
          </TextCp>
          <TextCp opacity={0.6} style={{fontSize: 12, marginTop: 3}}>
            {convertToUserTime(value.created_at)}
          </TextCp>
        </View>
      </View>
      <View style={styles.itemRight}>
        <CashLayout
          value={value.amount}
          fontSize={15}
          color={ots[icon]?.color}
        />
        {value.transfer_status && (
          <View
            style={{
              backgroundColor: statuses[status]?.background || '#eee',
              ...styles.pill,
            }}>
            <TextCp
              color={statuses[status]?.color || '#333'}
              style={{fontSize: 11}}
              textType={'semiBold'}>
              {lang === 'fr'
                ? value.operation_status_fr
                : value.transfer_status}
            </TextCp>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    backgroundColor: '#fff',
    paddingHorizontal: 12,
    marginBottom: 10,
    paddingHorizontal: 8,
    paddingVertical: 18,
    flexDirection: 'row',
    borderRadius: 10,
  },
  itemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  itemRight: {
    flex: 1,
    alignItems: 'flex-end',
  },
  iconBox: {
    height: 50,
    width: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    position: 'relative',
    backgroundColor: '#fff0b3',
  },
  icon: {
    position: 'absolute',
  },
  pill: {
    paddingHorizontal: 10,
    borderRadius: 20,
    marginTop: 5,
    paddingTop: 4,
    paddingBottom: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default TransactionItem;
