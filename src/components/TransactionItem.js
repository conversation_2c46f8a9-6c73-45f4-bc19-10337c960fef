import React, {useEffect, useState, useMemo} from 'react';
import {View, StyleSheet, TouchableOpacity} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import {ots, statues} from '../transfer_data/index';
import TextCp from './TextCp';
import {translate, setI18nConfig} from '../utilities/Translate';
import useDanaStore from '../app_state/store';
import CashLayout from './CashLayout';
import useGetLang from '../Hooks/useGetLang';
import {convertToUserTime} from '../utilities';

const statuses = {
  failed: {color: '#F00505', background: '#F7D1CD'},
  aborted: {color: '#F00505', background: '#F7D1CD'},
  'in progress': {color: '#ED6307', background: 'rgba(229,146,21,.1)'},
  completed: {color: '#14AD00', background: '#34E00010'},
  cancelled: {color: '#F00505', background: '#F7D1CD'},
  'action required': {color: '#F00505', background: '#F7D1CD'},
  'payment aborted': {color: '#F00505', background: '#F7D1CD'},
};

const TransactionItem = ({value, openOperationModal}) => {
  const lang = useGetLang();
  const user = useDanaStore(state => state.user);
  const [transaction, setTransaction] = useState({
    show: false,
    details: value,
  });
  const setSelelctedTransfer = useDanaStore(
    state => state.setSelelctedTransfer,
  );
  const {
    operation_type = null,
    amount_without_fee = null,
    status = null,
    created_at = null,
    source_user = null,
    transfer = null,
    id = null,
    operation_direction = null, // Add this line to destructure operation_direction with default value
  } = value;
  
  const isDemanded = operation_type === 'payment_demand';
  const isActiveSender = useMemo(() => {
    // Add null check for operation_direction
    if (!operation_direction) {
      return user?.id === source_user?.id;
    }
    return operation_direction?.toLowerCase() === 'sending';
  }, [operation_direction, user?.id, source_user?.id]);
  const cash = amount_without_fee;

  const returnUser = useMemo(() => {
    if (!isDemanded) {
      if (operation_type === 'deposit') {
        return {
          full_name: transaction.details.destination_user?.is_individual
            ? `${transaction.details.destination_user?.full_name}`
            : transaction.details.destination_user?.company?.name,
          country: transaction.details.destination_user?.country,
        };
      }

      if (operation_type === 'pay_out' || operation_type === 'withdraw') {
        return {
          full_name: transaction.details.destination_user?.is_individual
            ? `${transaction.details.destination_user?.full_name}`
            : transaction.details.destination_user?.company?.name,
          country: transaction.details.destination_user?.country,
        };
      } else if (operation_type === 'direct_transfer') {
        return {
          full_name: transaction.details.destination_user?.is_individual
            ? `${transaction.details.destination_user?.full_name}`
            : transaction.details.destination_user?.company?.name,
          country: transaction.details.destination_user?.country,
        };
      }
    }
    if (isActiveSender) {
      return {
        full_name: transaction.details.destination_user?.is_individual
          ? `${transaction.details.destination_user?.full_name}`
          : transaction.details.destination_user?.company?.name,
        country: transaction.details.destination_user?.country,
      };
    } else {
      return {
        full_name: transaction.details.source_user?.is_individual
          ? `${transaction.details.source_user?.first_name} ${transaction.details.source_user?.last_name} `
          : transaction.details.source_user?.company?.name,
        country: transaction.details.source_user?.country,
      };
    }
  }, [transaction]);

  const viewTransactionProfile = item => {
    if (operation_type.toLowerCase() === 'campaign_referral_reward') {
      return;
    }
    setSelelctedTransfer(null);
    setSelelctedTransfer(item);
    openOperationModal();
  };

  useEffect(() => {
    setI18nConfig();
  }, []);

  return (
    <TouchableOpacity
      testID={`transaction_${id}`}
      accessibilityLabel={`transaction_${id}`}
      onPress={() => viewTransactionProfile(value)}
      style={styles.itemContainer}>
      <View style={styles.itemLeft}>
        <View
          style={{
            ...styles.iconBox,
            backgroundColor:
              operation_type === 'instant_transfer'
                ? isActiveSender
                  ? ots[operation_type]?.bgColor
                  : ots['instant_transfer_receiver']?.bgColor
                : ots[operation_type]?.bgColor,
          }}>
          {operation_type === 'instant_transfer' ? (
            <>
              {isActiveSender ? (
                <Icon
                  name={ots[operation_type]?.icon}
                  size={24}
                  style={styles.icon}
                  color={ots[operation_type]?.iconColor}
                />
              ) : (
                <Icon
                  name={ots['instant_transfer_receiver'].icon}
                  size={24}
                  style={styles.icon}
                  color={ots['instant_transfer_receiver']?.iconColor}
                />
              )}
            </>
          ) : (
            <Icon
              name={ots[operation_type]?.icon || 'help-circle'}
              size={24}
              style={styles.icon}
              color={ots[operation_type]?.iconColor}
            />
          )}
        </View>

        <View>
          <TextCp
            style={{textTransform: 'capitalize', fontSize: 15}}
            textType={'medium'}>
            {returnUser &&
              returnUser !== 'undefined' &&
              returnUser['full_name']}
          </TextCp>
          <TextCp opacity={0.6} style={{fontSize: 12, marginTop: 3}}>
            {convertToUserTime(created_at)}
          </TextCp>
        </View>
      </View>
      <View style={styles.itemRight}>
        <CashLayout value={cash} fontSize={15} color="#037375" />
        {statues[status]?.text && (
          <View
            style={{
              backgroundColor:
                statuses[value?._details?.operation_status_en?.toLowerCase()]
                  ?.background,
              ...styles.pill,
            }}>
            <TextCp
              color={
                statuses[value?._details?.operation_status_en?.toLowerCase()]
                  ?.color
              }
              style={{
                fontSize: 11,
              }}
              textType={'semiBold'}>
              {lang === 'fr'
                ? value?._details?.operation_status_fr
                : value?._details?.operation_status_en}
            </TextCp>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    backgroundColor: '#fff',
    paddingHorizontal: 12,
    marginBottom: 10,
    paddingHorizontal: 8,
    paddingVertical: 18,
    flexDirection: 'row',
    borderRadius: 10,
  },
  item: {
    flexDirection: 'row',
    paddingVertical: 12,
    borderRadius: 8,
    borderColor: '#ddd',
    borderBottomWidth: 0.4,
    backgroundColor: '#fff',
    paddingHorizontal: 10,
    marginVertical: 4,
    alignItems: 'center',
  },
  itemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  itemRight: {
    flex: 1,
    // justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  name: {
    fontSize: 14,
  },
  iconBox: {
    height: 50,
    width: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    position: 'relative',
    backgroundColor: '#fff0b3',
  },
  icon: {
    position: 'absolute',
  },
  modal: {
    backgroundColor: '#f1f1f1',
    position: 'absolute',
    right: 0,
    left: 0,
    bottom: 0,
    margin: 0,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  list: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 5,
  },
  section: {
    marginVertical: 8,
    backgroundColor: '#fff',
    padding: 10,
    elevation: 1,
    borderRadius: 9,
    marginHorizontal: 20,
  },
  sectionHeader: {
    marginBottom: 10,
  },
  flag: {
    height: 15,
    width: 15,
    borderRadius: 9,
    position: 'relative',
    bottom: -16,
    right: -15,
  },
  pill: {
    paddingHorizontal: 10,
    borderRadius: 20,
    marginTop: 5,
    paddingTop: 4,
    paddingBottom: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default TransactionItem;
