import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import TextCp from '../components/TextCp';
import Icon from 'react-native-vector-icons/Feather';
import StatusTracker from './StatusTracker';
import DanaButton from './DanaButton';
import {gs, pagePadding, scaleRatio, Width} from '../theme';
import {translate, setI18nConfig} from '../utilities/Translate';

export default function TransferPopUpDetails({
  clickedTransaction,
  downloadTransaction,
  setShowTransaction,
  repeatTransaction,
}) {
  React.useEffect(() => {
    setI18nConfig();
  });

  return (
    <View style={styles.modal}>
      {clickedTransaction.status === 'd' &&
        clickedTransaction.data !== null && (
          <View style={styles.innerContainerTra}>
            <View style={{justifyContent: 'flex-end', alignItems: 'flex-end'}}>
              <TouchableOpacity onPress={() => setShowTransaction(false)}>
                <Icon
                  name="x-circle"
                  style={{fontSize: 35 * scaleRatio}}></Icon>
              </TouchableOpacity>
            </View>
            <View>
              <View style={styles.modalTitle}>
                <TextCp>
                  {clickedTransaction.data &&
                    clickedTransaction.data.created_at}
                </TextCp>
                <TouchableOpacity
                  onPress={() => downloadTransaction()}
                  style={styles.downloadBtn}>
                  <Icon name="download-cloud" style={styles.downloadIcon} />
                </TouchableOpacity>
              </View>
              <View style={styles.modalUser}>
                <TextCp style={styles.modalheader}>
                  {translate('Beneficiary')}
                </TextCp>
                <TextCp textType="regular" style={{fontSize: 20 * scaleRatio}}>
                  {clickedTransaction.data &&
                    ` ${clickedTransaction.data.data.recipient_first_name}  ${clickedTransaction.data.data.recipient_last_name}`}
                </TextCp>
                <TextCp
                  style={{
                    color: 'rgba(0, 0, 0, 0.5)',
                    fontSize: 14 * scaleRatio,
                  }}>
                  <Icon name="phone" />
                  {clickedTransaction.data &&
                    ` ${clickedTransaction.data.data.country_code} ${clickedTransaction.data.data.phone_number}`}
                </TextCp>
                <TextCp
                  style={{
                    color: 'rgba(0, 0, 0, 0.5)',
                    fontSize: 14 * scaleRatio,
                  }}>
                  <Icon name="map" />{' '}
                  {clickedTransaction.data &&
                    clickedTransaction.data.data.country}
                  <Icon name="chevron-right" />{' '}
                  {clickedTransaction.data &&
                    clickedTransaction.data.data.destination_city}
                  <Icon name="chevron-right" />{' '}
                  {clickedTransaction.data &&
                    clickedTransaction.data.data.destination_quarter}
                </TextCp>
              </View>
              <View style={styles.modalTransaction}>
                <View style={{flex: 3, borderRadius: 10}}>
                  <TextCp style={styles.modalheader}>
                    {translate('amount')}
                  </TextCp>
                  <TextCp textType="bold" style={styles.modalMoney}>
                    {clickedTransaction.data &&
                      clickedTransaction.data.data.amount_in_euros -
                        clickedTransaction.data.data.fee}
                    €
                  </TextCp>
                  <TextCp style={styles.modalConversion}>655.957 Fcfa</TextCp>
                  <TextCp style={styles.modalConversion}>
                    655.957 Danacoins
                  </TextCp>
                </View>
                <View style={{flex: 1.5}}>
                  <TextCp style={styles.modalheader}>
                    {translate('fees')}
                  </TextCp>
                  <TextCp textType="bold" style={styles.modalMoney}>
                    {clickedTransaction.data &&
                      clickedTransaction.data.data.fee}
                    €
                  </TextCp>
                  {clickedTransaction.data &&
                  clickedTransaction.data.data.payment_delivery ? (
                    <TextCp style={styles.modalConversion}>
                      {translate('delivery')}: {translate('yes')}
                    </TextCp>
                  ) : (
                    <TextCp style={styles.modalConversion}>
                      {translate('delivery')}: {translate('no')}
                    </TextCp>
                  )}
                </View>
              </View>
              <View style={styles.status}>
                <TextCp style={styles.modalheader}>
                  {translate('transaction_status')}
                </TextCp>
                <StatusTracker transfer={clickedTransaction.data} />
              </View>
            </View>
            <View style={{marginVertical: 20}}>
              <DanaButton
                onPress={() => repeatTransaction()}
                title={translate('repeat')}
                theme="#282828"
              />
            </View>
          </View>
        )}
      {clickedTransaction.status === 'l' && (
        <View style={styles.innerContainerTra}>
          <View style={{justifyContent: 'flex-end', alignItems: 'flex-end'}}>
            <TouchableOpacity onPress={() => setShowTransaction(false)}>
              <Icon name="x-circle" style={{fontSize: 35 * scaleRatio}}></Icon>
            </TouchableOpacity>
          </View>
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              padding: 30,
            }}>
            <ActivityIndicator size="large" color="#555" />
          </View>
        </View>
      )}
      {clickedTransaction.status === 'f' && (
        <View style={styles.innerContainerTra}>
          <View style={{justifyContent: 'flex-end', alignItems: 'flex-end'}}>
            <TouchableOpacity onPress={() => setShowTransaction(false)}>
              <Icon name="x-circle" style={{fontSize: 35 * scaleRatio}}></Icon>
            </TouchableOpacity>
          </View>
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              padding: 30,
            }}>
            <TextCp>{clickedTransaction.message}</TextCp>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    paddingHorizontal: 26,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16 * scaleRatio,
  },
  innerContainerTra: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16,
  },
  modalTitle: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginVertical: 10,
    alignItems: 'center',
  },
  downloadBtn: {
    backgroundColor: '#000',
    height: 50 * scaleRatio,
    width: 50 * scaleRatio,
    borderRadius: 25 * scaleRatio,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 25,
  },
  downloadIcon: {
    fontSize: 30 * scaleRatio,
    color: '#fff',
  },
  modalMoney: {
    color: '#000000',
    fontSize: 30 * scaleRatio,
  },
  modalheader: {
    color: 'rgba(0, 0, 0, 0.51)',
    fontSize: 14 * scaleRatio,
    marginBottom: 5,
  },
  modalConversion: {
    color: 'rgba(0, 0, 0, 0.5)',
    fontSize: 14 * scaleRatio,
  },
  bullet: {
    fontSize: 24 * scaleRatio,
    marginRight: 10 * scaleRatio,
  },
  statusItem: {
    marginBottom: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  status: {
    paddingHorizontal: 14 * scaleRatio,
    backgroundColor: '#fff',
    borderRadius: 10,
    paddingVertical: 8,
    ...gs.boxShadow,
  },
  modalUser: {
    marginBottom: 15,
    paddingHorizontal: 14,
    backgroundColor: '#fff',
    borderRadius: 10,
    paddingVertical: 8,
    ...gs.boxShadow,
  },
  modalTransaction: {
    flexDirection: 'row',
    marginBottom: 15,
    paddingHorizontal: 14,
    backgroundColor: '#fff',
    borderRadius: 10,
    paddingVertical: 8,
    ...gs.boxShadow,
  },
});
