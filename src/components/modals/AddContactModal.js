import {
  View,
  KeyboardAvoidingView,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';

import React, {forwardRef, useEffect, useState, useMemo} from 'react';
import {showMessage} from 'react-native-flash-message';
import {Modalize} from 'react-native-modalize';
import {setI18nConfig, translate} from '../../utilities/Translate';
import {colors, pagePadding, scaleRatio} from '../../theme';
import TextCp from '../TextCp';
import DanaButton from '../DanaButton';
import {addContact} from '../../apis/favorites';
import useDanaStore from '../../app_state/store';
import Input from '../Input';
import SelectInput from '../SelectInput';
import {extractError} from '../../utilities/errorReporting';
import {CompanyContactSchema, ContactSchema} from '../../validations';
import {extractDigits, extractYupErrors, isValid, validateEmail} from '../../utilities';
import {checkUserNameIfReal} from '../../apis/auth';
import useGetLang from '../../Hooks/useGetLang';
import Divider from '../Divider';

const AddContactModal = forwardRef(
  ({closeModalize, details, fetchContacts, createdUser = null}, ref) => {
    const state_countries = useDanaStore(state => state.countries);
    const [adding, setAdding] = useState(false);
    const [errors, setErrors] = useState(null);
    const [emailError, setEmailError] = useState(null);
    const [PhoneError, setPhoneError] = useState(null);
    const [contactDetails, setContactDetails] = useState({
      first_name: '',
      last_name: '',
      country: null,
      phone_number: '',
      company_name: '',
      account_type: '',
      email: '',
    });

    const emailRequired = useMemo(
      () =>
        contactDetails.country &&
        contactDetails?.country?.preferred_notification_channel === 'mail',
      [contactDetails],
    );

    const checkValidation = async () => {
      setPhoneError(null);

      setErrors(null);
      const body = {
        account_type: contactDetails.account_type,
        last_name: contactDetails.last_name,
        first_name: contactDetails.first_name,
        phone_number: extractDigits(contactDetails.phone_number),
        country: contactDetails.country?.name || '',
      };

      setEmailError(null);

      if (emailRequired) {
        body['email'] = contactDetails.email;
      }

      try {
        if (contactDetails.account_type === 'individual') {
          await ContactSchema.validate(body, {abortEarly: false});
          if (
            !isValid(
              contactDetails.country?.country_code,
              extractDigits(contactDetails.phone_number),
              contactDetails.country?.code,
            )
          ) {
            setPhoneError(translate('invalid_phone_number'));
            return;
          }

          if (emailRequired && !validateEmail(contactDetails.email.trim())) {
            setEmailError('email_is_invalid');
            return;
          }
        } else {
          await CompanyContactSchema.validate(
            {
              ...body,
              company_name: contactDetails.company_name,
            },
            {abortEarly: false},
          );
        }
        add_contact();
      } catch (error) {
        setErrors(prev => ({...prev, ...extractYupErrors(error)}));
      }
    };

    const add_contact = async () => {
      setAdding(true);

      const full_name = `${contactDetails?.first_name} ${contactDetails?.last_name}`;

      checkUserNameIfReal({full_name})
        .then(response => {
          if (!response.isRealName) {
            Alert.alert('', translate('wrong_name_desc'), [
              {
                text: translate('wrong_name_cancel'),
                onPress: () => {
                  setAdding(false);
                },
                style: 'cancel',
              },
              {
                text: 'Continuer',
                text: translate('wrong_name_continue'),
                onPress: () => {
                  _continueToAdd();
                },
              },
            ]);
          } else {
            _continueToAdd();
          }
        })
        .catch(error => {});
    };

    const _continueToAdd = () => {
      const contact = {
        phone_number: extractDigits(contactDetails.phone_number),
        country_code: contactDetails.country?.country_code,
        first_name: contactDetails.first_name,
        last_name: contactDetails.last_name,
        is_individual:
          contactDetails.account_type === 'individual' ? true : false,
        company_name: contactDetails.company_name,
        email: contactDetails.email,
      };

      if (contactDetails.account_type === 'individual') {
        delete contact.company_name;
      }

      addContact(contact)
        .then(resposne => {
          closeModalize();
          setAdding(false);
          showMessage({
            type: 'success',
            message: 'Contact was added successfully',
          });
          setContactDetails({
            first_name: '',
            last_name: '',
            country: null,
            phone_number: '',
            company_name: '',
            account_type: '',
            email: '',
          });
          fetchContacts();
          if (createdUser) {
            createdUser({
              ...resposne.beneficiary,
              country: resposne.transferCountry,
            });
          }
        })
        .catch(error => {
          setAdding(false);
          showMessage({
            type: 'danger',
            message: extractError(error),
          });
        });
    };

    const isCompanySelected = useMemo(
      () => contactDetails.account_type === 'company',
      [contactDetails],
    );

    useEffect(() => {
      setI18nConfig();
      setContactDetails(prev => ({...prev, ...details}));
    }, [details]);

    return (
      <Modalize ref={ref} adjustToContentHeight>
        <KeyboardAvoidingView
          enabled
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
          <ScrollView style={{padding: 20}}>
            <TextCp
              textType="bold"
              align="center"
              style={{
                fontSize: 20 * scaleRatio,
              }}>
              {translate('create_a_beneficiary')}
            </TextCp>

            <View style={styles.optionsView}>
              {['individual', 'company'].map(val => (
                <TouchableOpacity
                  style={styles.option}
                  onPress={() =>
                    setContactDetails(prev => ({
                      ...prev,
                      account_type: val,
                    }))
                  }>
                  <View
                    style={{
                      ...styles.checkBox,
                      backgroundColor:
                        contactDetails.account_type === val
                          ? colors.primary
                          : 'rgba(0,0,0,.2)',
                    }}>
                    {contactDetails.account_type === val && (
                      <Icon name="check" color="#fff" szie={30} />
                    )}
                  </View>
                  <TextCp>{translate(val)}</TextCp>
                </TouchableOpacity>
              ))}
              {errors?.account_type && (
                <TextCp
                  color="#c1121f"
                  style={{
                    fontSize: 12,
                  }}>
                  {errors?.account_type && translate(errors?.account_type)}
                </TextCp>
              )}
            </View>

            {isCompanySelected && (
              <>
                <Input
                  onChange={text =>
                    setContactDetails(prev => ({
                      ...prev,
                      company_name: text,
                    }))
                  }
                  label={translate('company_name')}
                  value={contactDetails.company_name}
                  backgroundColor={'#fff'}
                  error={
                    errors?.company_name && translate(errors?.company_name)
                  }
                />
                <Divider height={10} />
              </>
            )}

            <Input
              onChange={text =>
                setContactDetails(prev => ({
                  ...prev,
                  first_name: text,
                }))
              }
              label={
                isCompanySelected
                  ? translate('contact_first_name')
                  : translate('first_name')
              }
              value={contactDetails.first_name}
              backgroundColor={'#fff'}
              error={errors?.first_name && translate(errors?.first_name)}
            />
            <Divider height={10} />

            <Input
              onChange={text =>
                setContactDetails(prev => ({
                  ...prev,
                  last_name: text,
                }))
              }
              label={
                isCompanySelected
                  ? translate('contact_last_name')
                  : translate('last_name')
              }
              value={contactDetails.last_name}
              backgroundColor={'#fff'}
              error={errors?.last_name && translate(errors?.last_name)}
            />

            <SelectInput
              onValueChange={value => {
                const selected_country = state_countries.find(
                  val => val.id.toString() === value.toString(),
                );
                setContactDetails(prev => ({
                  ...prev,
                  country: selected_country,
                }));
              }}
              items={state_countries.map(val => {
                return {
                  label: val.name,
                  value: val.id,
                };
              })}
              placeholder={{
                label: translate('country'),
                value: translate('country'),
              }}
              value={contactDetails.country?.id}
              error={errors?.country && translate(errors?.country)}
            />

            <View style={{marginBottom: 8}}>
              <View style={{flexDirection: 'row'}}>
                <View style={{width: 100, marginRight: 8}}>
                  <Input
                    onChange={text => null}
                    label={translate('code')}
                    value={
                      contactDetails.country
                        ? `(+${contactDetails.country?.country_code})`
                        : 0
                    }
                    backgroundColor={'#fff'}
                  />
                </View>
                <View style={{flex: 1}}>
                  <Input
                    onChange={text =>
                      setContactDetails(prev => ({
                        ...prev,
                        phone_number: text,
                      }))
                    }
                    label={translate('PhoneNumber')}
                    value={contactDetails.phone_number}
                    backgroundColor={'#fff'}
                    keyboardType="number-pad"
                  />
                </View>
              </View>
              {(errors?.phone_number || PhoneError) && (
                <TextCp
                  color="#c1121f"
                  style={{
                    fontSize: 12,
                  }}>
                  {errors?.phone_number && translate(errors?.phone_number)}
                  {PhoneError && PhoneError}
                </TextCp>
              )}
            </View>

            <Input
              onChange={text =>
                setContactDetails(prev => ({
                  ...prev,
                  email: text,
                }))
              }
              label={
                isCompanySelected
                  ? emailRequired
                    ? translate('contact_email')
                    : translate('contact_email_optional')
                  : emailRequired
                  ? translate('email')
                  : translate('email_optional')
              }
              value={contactDetails.email}
              backgroundColor={'#fff'}
              error={emailError !== null ? translate(emailError) : ''}
            />

            <DanaButton
              title={`${translate('invite_contact')}`}
              theme="#282828"
              onPress={checkValidation}
              loading={adding}
            />
            <View style={{height: 20}} />
          </ScrollView>
        </KeyboardAvoidingView>
      </Modalize>
    );
  },
);

const styles = StyleSheet.create({
  optionsView: {
    backgroundColor: '#fff',
    padding: 10,
    borderRadius: 10,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,.1)',
  },
  option: {
    marginVertical: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkBox: {
    height: 30,
    width: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    marginRight: 10,
  },
});

export default AddContactModal;
