import React, {useEffect, memo, forwardRef} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import {Modalize} from 'react-native-modalize';
import FastImage from 'react-native-fast-image';
import useDanaStore from '../../app_state/store';
import {setI18nConfig, translate} from '../../utilities/Translate';
import {borderRadius, scaleRatio} from '../../theme';
import TextCp from '../TextCp';
import {flags} from '../../images/flags';

const CountryModals = forwardRef(({onCountrySelected, country}, ref) => {
  const countries = useDanaStore(state => state.countries);
  useEffect(() => {
    setI18nConfig();
  }, []);

  return (
    <Modalize ref={ref} adjustToContentHeight>
      <View>
        <View style={styles.innerContainer}>
          <TextCp
            textType="bold"
            style={{...styles.title, marginBottom: 8}}
            align="center">
            {translate('country')}
          </TextCp>
          <TextCp
            align="center"
            opacity={0.51}
            style={{
              fontSize: 15,
              marginBottom: 8,
            }}>
            {translate('select_country')}
          </TextCp>
          <ScrollView horizontal={true} showsHorizontalScrollIndicator={false}>
            {countries
              .sort((a, b) => a.name.localeCompare(b.name))
              .map((country, index) => {
                return (
                  <View
                    style={{justifyContent: 'center', alignItems: 'center'}}
                    key={index}>
                    <TouchableOpacity
                      key={country?.name}
                      style={styles.countryBox}
                      onPress={() => onCountrySelected(country)}>
                      <FastImage
                        style={styles.flag}
                        source={
                          flags[country?.name.toLowerCase().replace(/\s/g, '_')]
                        }
                        resizeMode={FastImage.resizeMode.contain}
                      />
                    </TouchableOpacity>
                    <TextCp>{country?.name}</TextCp>
                  </View>
                );
              })}
          </ScrollView>
          <TextCp
            align="center"
            style={{fontSize: 20, marginVertical: 32}}
            textType="bold">
            {country?.name}
          </TextCp>
        </View>
      </View>
    </Modalize>
  );
});

const styles = StyleSheet.create({
  countryBox: {
    marginHorizontal: 16,
    marginTop: 25,
    marginBottom: 8,
  },
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    right: 0,
    left: 0,
    padding: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  modalCountry: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    padding: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    paddingVertical: 30,
  },
  flag: {
    height: 70,
    width: 70,
  },
  image: {
    height: 507 * scaleRatio,
    width: '100%',
    borderRadius: borderRadius,
    marginBottom: 10 * scaleRatio,
  },
  modalContainer: {
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: 'red',
  },
  title: {
    fontSize: 26,
    marginBottom: 17,
    textAlign: 'center',
  },
});

export default memo(CountryModals);
