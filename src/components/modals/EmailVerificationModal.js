import React, {useEffect, useRef, memo, forwardRef, useState} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import {Modalize} from 'react-native-modalize';
import {setI18nConfig, translate} from '../../utilities/Translate';
import {borderRadius, scaleRatio} from '../../theme';
import TextCp from '../TextCp';
import OTPInput from '../OTPInput';
import DanaButton from '../DanaButton';
import FlashMessage from 'react-native-flash-message';

const EmailVerificationModal = forwardRef(
  (
    {
      onFulfill,
      handleVerifyCode,
      loadingStatus,
      resend,
      otpError,
      email,
      changeEmail,
    },
    ref,
  ) => {
    const modalFlash = useRef();
    const [timer, setTimer] = useState(59);

    useEffect(() => {
      if (!loadingStatus.resending) {
        setTimer(59);
      }
    }, [loadingStatus.resending]);

    useEffect(() => {
      setI18nConfig();
      const _timer = setInterval(() => {
        if (timer > 0) {
          setTimer(timer - 1);
        } else {
          clearInterval(_timer);
        }
      }, 1000);

      return () => {
        clearInterval(_timer);
      };
    }, [timer]);

    return (
      <Modalize
        ref={ref}
        adjustToContentHeight
        closeOnOverlayTap={false}
        onOpen={() => {
          setTimer(59);
        }}>
        <View>
          <View style={styles.modalheader}>
            <View />
            <TouchableOpacity onPress={changeEmail}>
              <TextCp textType={'bold'} color="#037375">
                {translate('changeEmail')}
              </TextCp>
            </TouchableOpacity>
          </View>
          <View style={styles.innerContainer}>
            <View style={{paddingHorizontal: 20, marginTop: 10}}>
              <TextCp textType="bold" style={styles.titleCode} align="center">
                {translate('email_verification')}
              </TextCp>
              <TextCp
                textType="light"
                style={styles.subtitleCode}
                align="center">
                {translate('existing_account_text')}
                <TextCp textType={'bold'}>{email}</TextCp>
              </TextCp>
            </View>

            <View style={{marginVertical: 10}}>
              <OTPInput onChange={code => onFulfill(code)} testID="otpInput" />
            </View>

            {otpError && (
              <TextCp color="red" align="center">
                {translate(otpError)}
              </TextCp>
            )}

            <View style={{marginVertical: 16}}>
              <DanaButton
                onPress={handleVerifyCode}
                title={translate('validate')}
                theme="#282828"
                loading={loadingStatus.sending}
                testID="verifyOtpButton"
              />

              <View style={{height: 10}} />
              {timer > 0 ? (
                <View style={styles.resendBox}>
                  <TextCp textType="light" style={{textAlign: 'center'}}>
                    {translate('resend_code_in')}{' '}
                    <TextCp
                      textType={'bold'}
                      style={{marginHorizontal: 4}}
                      color="#037375">
                      {`00:${timer > 9 ? timer : `0${timer}`}`}{' '}
                    </TextCp>
                    {translate('seconds')}
                  </TextCp>
                </View>
              ) : (
                <TouchableOpacity
                  style={{
                    padding: 20,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  onPress={() => {
                    resend();
                  }}>
                  {loadingStatus.resending ? (
                    <ActivityIndicator size="small" color="#037375" />
                  ) : (
                    <TextCp Color={'#037375'} textType={'bold'}>
                      {translate('resend_code')}
                    </TextCp>
                  )}
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
        <FlashMessage ref={modalFlash} position="top" />
      </Modalize>
    );
  },
);

const styles = StyleSheet.create({
  countryBox: {
    marginHorizontal: 16,
    marginTop: 25,
    marginBottom: 8,
  },
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    right: 0,
    left: 0,
    bottom: 0,
    padding: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  modalCountry: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    padding: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    padding: 20,
  },
  flag: {
    height: 70,
    width: 70,
  },
  image: {
    height: 507 * scaleRatio,
    width: '100%',
    borderRadius: borderRadius,
    marginBottom: 10 * scaleRatio,
  },
  modalContainer: {
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: 'red',
  },
  title: {
    fontSize: 20,
    marginBottom: 10,
    textAlign: 'center',
  },

  subtitleCode: {
    textAlign: 'center',
    color: '#000',
    fontSize: 13,
  },
  titleCode: {
    fontSize: 25,
    color: '#010101',
    textAlign: 'center',
    textTransform: 'capitalize',
  },
  resendBox: {
    paddingVertical: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalheader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
  },
});

export default memo(EmailVerificationModal);
