import React, {useEffect, useRef, memo, forwardRef, useState} from 'react';
import {View, StyleSheet, TouchableOpacity, Dimensions} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import {Modalize} from 'react-native-modalize';
import {setI18nConfig, translate} from '../../utilities/Translate';
import {borderRadius, colors, scaleRatio} from '../../theme';
import TextCp from '../TextCp';
import OTPInput from '../OTPInput';
import DanaButton from '../DanaButton';
import FlashMessage from 'react-native-flash-message';
import useDanaStore from '../../app_state/store';

const OPTCodeModal = forwardRef(
  (
    {
      onFulfill,
      handleVerifyCode,
      loadingStatus,
      sendUserOTP,
      resending,
      closeModal,
      channel,
      otpError,
    },
    ref,
  ) => {
    const modalFlash = useRef();
    const [timer, setTimer] = useState(59);
    const temp_user = useDanaStore(state => state.temp_user);

    useEffect(() => {
      setI18nConfig();

      const _timer = setInterval(() => {
        if (timer > 0) {
          setTimer(timer - 1);
        } else {
          clearInterval(_timer);
        }
      }, 1000);

      return () => {
        clearInterval(_timer);
      };
    }, [timer]);

    return (
      <Modalize
        ref={ref}
        adjustToContentHeight
        onOpen={() => {
          setTimer(59);
        }}>
        <View>
          <View style={styles.innerContainer}>
            <View style={{justifyContent: 'flex-end', alignItems: 'flex-end'}}>
              <TouchableOpacity onPress={closeModal}>
                <Icon
                  name="x-circle"
                  style={{fontSize: 35 * scaleRatio}}></Icon>
              </TouchableOpacity>
            </View>
            <View style={{paddingHorizontal: 20, marginTop: 10}}>
              <TextCp textType="bold" style={styles.titleCode} align="center">
                {translate('secret_code')}
              </TextCp>
              {channel === 'sms' ? (
                <TextCp
                  textType="light"
                  style={styles.subtitleCode}
                  align="center">
                  {translate('code_was_sent')}{' '}
                  <TextCp
                    textType={
                      'bold'
                    }>{`+${temp_user?.country_code}${temp_user?.phone_number}`}</TextCp>
                </TextCp>
              ) : (
                <TextCp
                  textType="light"
                  style={styles.subtitleCode}
                  align="center">
                  {translate('code_was_sent_whatsapp')}{' '}
                  <TextCp
                    textType={
                      'bold'
                    }>{`+${temp_user?.country_code}${temp_user?.phone_number}`}</TextCp>
                </TextCp>
              )}
            </View>

            <View style={{marginVertical: 10}}>
              <OTPInput onChange={code => onFulfill(code)} testID="otpInput" />
            </View>

      
            <View style={{marginVertical: 16}}>
              <DanaButton
                onPress={handleVerifyCode}
                title={translate('validate')}
                theme="#282828"
                loading={loadingStatus.verifyingCode}
                testID="verifyOtpButton"
              />

              <View style={{height: 10}} />
              {timer > 0 ? (
                <View style={styles.resendBox}>
                  <TextCp textType="light" style={{textAlign: 'center'}}>
                    {translate('resend_code_in')}{' '}
                    <TextCp
                      textType={'bold'}
                      color={colors.primary}
                      style={{marginHorizontal: 4}}>
                      {`00:${timer > 9 ? timer : `0${timer}`}`}{' '}
                    </TextCp>
                    {translate('seconds')}
                  </TextCp>
                </View>
              ) : (
                <TouchableOpacity
                  style={styles.resendBox}
                  onPress={() => {
                    sendUserOTP();
                    setTimer(59);
                  }}>
                  <TextCp
                    align="center"
                    textType={'bold'}
                    color={colors.primary}>
                    {translate('resend_code')}
                  </TextCp>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
        <FlashMessage ref={modalFlash} position="top" />
      </Modalize>
    );
  },
);

const styles = StyleSheet.create({
  countryBox: {
    marginHorizontal: 16,
    marginTop: 25,
    marginBottom: 8,
  },
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    right: 0,
    left: 0,
    bottom: 0,
    padding: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  modalCountry: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    padding: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    padding: 20,
  },
  flag: {
    height: 70,
    width: 70,
  },
  image: {
    height: 507 * scaleRatio,
    width: '100%',
    borderRadius: borderRadius,
    marginBottom: 10 * scaleRatio,
  },
  modalContainer: {
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: 'red',
  },
  title: {
    fontSize: 20,
    marginBottom: 10,
    textAlign: 'center',
  },

  subtitleCode: {
    textAlign: 'center',
    color: '#000',
    fontSize: 14,
  },
  titleCode: {
    fontSize: 24,
    color: '#010101',
    textAlign: 'center',
    textTransform: 'capitalize',
    marginBottom: 10,
  },
  resendBox: {
    paddingVertical: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default memo(OPTCodeModal);
