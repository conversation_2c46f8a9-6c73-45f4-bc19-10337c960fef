import {Dimensions, StyleSheet} from 'react-native';

const height = Dimensions.get('screen').height;

const defaultStyles = StyleSheet.create({
  toolbar: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  toolbarLeft: {
    width: '60%',
  },
  title: {
    fontSize: 23,
  },
  subTitle: {
    fontSize: 15,
    marginBottom: 10,
    color: '#000',
  },
  button: {
    backgroundColor: '#000',
    height: height * 0.065,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    flex: 1,
    marginHorizontal: 16,
    borderRadius: 10,
  },
  inputHeight: {
    height: height * 0.065,
  },
  footerWithButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    backgroundColor: 'transparent',
  },
  boxShow: {
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.06,
    shadowRadius: 20, // Matches the "20px" blur in the CSS
    elevation: 3, // Approximation for shadow on Android
  },
});

export default defaultStyles;
