{"": "", "loading": "Loading", "number": "Number", "receipts": "Receipts", "validate": "validate", "enter_phone": "Enter phone number", "Help": "Help", "choose_lng_to_watch": "Choose the language to view the help", "code_was_sent": "A secrete code has just been sent to you by SMS to ", "code_was_sent_whatsapp": "A secrete code has just been sent to you on WhatsApp to", "confirm_pin": "Confirm your PIN code", "no_records": "You have not yet completed a transaction", "cashoutpoint": "Cash out points", "transfer": "Transfer", "delivery": "Delivery", "amount": "Amount", "fees": "Fees", "delivery_fees": "Delivery fees", "return": "Back", "next": "Next", "previous": "Previous", "payment_success_title": "Payment by credit card", "payment_success_message": "Once your payment is validated we will send you a confirmation notification", "pick_up_location": "Our pick-up locations", "pick_up_location_description": "Select the country", "pay_info": "Payment information", "choose_pay_method": "Choose a desired payment method", "instant_payment": "Instant payment", "bank_card": "Bank Card", "bank_transfer": "Bank transfer", "enter_amount_info": "Enter the amount information", "transfer_accepted": "Your transfer has been taken into account", "select_delivery_district": "Select the district where you want to be delivered", "city": "City", "district": "District", "beneficiary_location": "Beneficiary location", "delivery_to_beneficiary_info": "You can have the Funds delivered to your beneficiary's home or office", "valid_identity": "Make sure the recipient has a valid identity.", "no_deliveries": "No delivery for me this time", "name_on_id": "Name (Presented on the identity card)", "first_name_on_id": "First name (Presented on the identity card)", "yes": "Yes", "no": "No", "secret_code": "Secret code", "Beneficiary": "Beneficiary", "transaction_status": "Transaction status", "repeat": "Repeat", "country": "Country", "beneficiary_information": "Beneficiary information", "delivery_fund": "I choose the delivery of funds for 1.5 €.", "Favorite": "Favorite", "pin_code": "Pin Code", "subtitleChoose": "To keep your information secure", "subtitleError": "Please try again", "titleConfirmFailed": "Your entries did not match", "no_contacts": "Add a contact to your favorites", "select_country": "Select Your current country of residence", "contacts": "Contacts", "beneficiary_information_more": "Enter your beneficiary's information or choose it from your contacts ", "Status_one": "Pending payment", "Status_two": "Payment in progress", "Status_three": "Payment validated", "Status_four": "Transfer in progress", "Status_five": "Transfer completed", "Status_six": "Funds withdrawn", "Status_seven": "Payment failed", "Status8": "Transfer failed", "tutorial_one": "Send money to your loved ones", "tutorial_two": "Send large amounts for your projects and business", "tutorial_three": "<PERSON><PERSON><PERSON> is committed to supporting you, whatever your needs", "title_one": "Money Transfer", "title_two": "Investment", "title_three": "And much more", "calcTitle": "Pay only € 1 transfer fee", "calcSubTitle": "Danapay reduces your money transfer fees to Africa", "calcPayMethod": "Payment method", "calcMoneyInEUR": "Amount in  EUR", "calcMoneyInCFA": "Amount in XOF", "calcAmountRange": "Amount range", "calcExchangeRate": "Exchange rate", "searchCountry": "Country search", "PhoneNumber": "Phone number", "Identity": "Identity", "MiniProfileSubTitle": "Enter your information as it appears on your ID", "lastname": "Last Name", "firstname": "First Name", "summary": "Summary", "summarySubTitle": "Please check the details of your transaction before proceeding to payment.", "totalToPay": "Total to pay", "transfer_not_accepted": "Your payment has been declined", "payment_fail_message": "Please start over with another payment method.", "home": "Home", "restart": "<PERSON><PERSON>", "Recommande": "RECOMMEND", "Prices": "PRICES", "AllTransfers": "Transfers", "Show_all": "Show All", "ContactTitle": "Contacts", "contactSubTitle": "Add one of your contacts to your favorites to automatically pre-fill your transfer form", "Confidentiality": "Confidentiality", "verify_your_identity": "Verify your identity", "reset_pin": "Reset your PIN code", "security": "Security", "personal_information": "Personal information", "Profile": "Profile", "disconnection": "Disconnection", "email": "Email address", "at": "at", "amountGrater": "This amount is greater than the maximum you can transfer", "amountLower": "The amount is lower than the minimum you can transfer", "funds": "Funds", "search": "Search by name", "all_error": "Please fill in all required fields", "contact_describe": "This app would like to view your contacts.", "logoutText": "Are you sure you want to log out?", "accountError": "Sorry, this app can only be used by users with customer accounts.", "cancel": "Cancel", "ok": "Okay", "VCRequired": "Verification Code required.", "PNRequired": "Phone number is required.", "pinError": "Invalid PIN code", "selectPay": "Please select the payment method", "transactions": "Transactions", "attention": "Attention", "attention_one": "It is important to respect your transaction wording so that we can execute your transaction quickly.", "attention_two": "It is important to send the exact amount specified above. Otherwise we could send you the funds back by deducting a management fee of 1€.", "attention_three": "We only accept SEPA credit transfers.", "attention_four": "Any incorrect code or amount will result in additional processing time...", "thankYou": "Thank you, ", "operation_accepted": "Your operation has been taken into account.", "please_send": "Please send us the amount ", "wording": " by SEPA bank transfer specifying the following transaction wording ", "bankInfo": "Danapay Banking Information", "accountHolder": "Account Holder", "address": "Address", "bankCode": "Bank Code", "agencyCode": "Agency Code", "accountNumber": "Account Number", "CheckOutYourPayment": "Check Out Your Payment", "invalidPin": "Invalid PIN code", "verifyTitle": "Identity verification", "verifySubTitle": "Please verify your identity, get your identity document ready", "start_verify": "Get Started", "limitTitle": "Transfer limit almost reached", "limitTitle1": "Transfer Limit", "start": "Start", "noTranslation": "No Transactions made yet", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "accepted": "Account Verified", "oldPin": "Old PIN code ", "newPin": "New PIN code ", "valid_email": "Email address is not valid.", "beneficiary_sub_title": "Please seize the beneficiary information", "paymentHeader": "Bank Card payment", "copy": "Copy", "BC_title": "Transfer Acknowledged", "BC_sub_title": "Now proceed with a bank transfer by following the instructions below", "bank_transfer_alert": "Important Information", "bank_transfer_alert_text": "Using a bank account that does not belong to you will result in the funds being returned, which will delay the transaction.", "BC_item1": "Reference", "downloadText": "Check your gallery for the PDF file contenting back information", "copyText": "Text has been copied", "payMo_needed": "Please complete all required fields", "failed": "Failed", "completed": "Completed", "processing": "processing", "started": "Pending", "cashout": "Cashed Out", "RIB": "Transfer details", "means_of_pay": "Payment Method", "confirm_add": "Add new contact", "add": "Add", "close": "Close", "limitText1": "By sending", "limitText2_one": "You will exceed your sending limit of ", "limitText2_two": " Start your verification", "limitSubTitle_one": "You are about to reach the limit of  ", "limitSubTitle_two": " sending. Start your verification.", "limitSubTitle1_one": "You have reached the limit of  ", "limitSubTitle1_two": "for sending. Start your verification.", "create_account": "Create an account first", "recommend_title": "Recommend <PERSON><PERSON><PERSON>", "recommend_title1": "<PERSON><PERSON>", "recommend_title2": "Recommended people", "recommend_title3": "show all", "recommend_btn": "Recommend", "list_page": "Recommend", "invite_btn": "Invite", "invitations": "Invitations", "sms": "Hello. I recommend you to use the Danapay application for you next transfers. You can sent up to 7000€ at 1€ fees. Clic on this link to get it www.danapay.io", "not_allowed_text": "Our services are not yet available in your country", "not_allowed_title": "Oops sorry", "not_allowed_footer": "Danapay, for a better Africa", "sending_country": "Sending Country", "receiving_country": "Receiving Country", "start_verification": "Start Verification", "email_optional": "Email address [optional]", "search_user": "Search", "add_contact": "Add Contact", "paying_in": "Paying in", "paying_out": "Paying out", "payment_failed": "Payment failed", "payment_started": "Payment started", "revoking_refund_confirmation": "Revoking", "payment_pending": "Payment pending", "payed_out": "Paid out", "payed_in": "Paid in", "download": "Download", "payment_completed": "Payment completed", "funds_received": "Funds received", "transfer_in_progress": "Transfer in progress", "deposit_failed": "Depo<PERSON><PERSON> failed", "deposit_completed": "Deposit completed", "deposit_in_progress": "Deposit in progress", "transfer_failed": "Transfer failed", "transfer_completed": "Transfer completed", "withdraw_requested": "Withdraw requested", "withdraw_in_progress": "Withdraw in progress", "withdraw_failed": "Withdraw failed", "withdraw_completed": "Withdraw completed", "instant_transfer": "Transfer", "card": "Bank Card", "enter_reason": "Enter reason", "make_withdraw": "Make  Withdraw", "make_deposit": "Make Deposit", "start_transfer": "Make Transfer", "FAQs": "FAQs", "our_prices": "Our Rates", "balance": "Balance", "all_contacts": "All Contacts", "all_contacts_text": "Add contacts and start making instant payments in one click", "view_history": "View History", "send_funds": "Send Funds", "send_funds_text": "Transfer funds to your beneficiary and they get it instantly.", "beneficiary_text": "Beneficiary details for the phone number", "registration_id": "Registration Id", "quarter": "Quarter", "phone_number": "Phone number", "contact_email": "Contact Email", "is_bene": "Please contact the support if this is not your recipient", "create_user_text": "Create a new contact with this number to continue", "beneficiary": "Beneficiary", "no_user": "No user was found with this phone number, create New user", "bank_payout": "Bank", "mobile_money_by_hub2": "Mobile", "instant_sepa": "Direct debit", "amount_in_euro": "Amount in EUR", "amount_in_cfa": "Amount in CFA", "continue": "Continue", "Done": "Cancel", "select_cashin_method": "Please select cash in method", "cfa_amount_required": "Please enter CFA amount.", "euro_amount_required": "Please enter EUR amount.", "manage_accounts": "Manage Accounts", "bank_accounts": "Bank accounts", "mobile_money_accounts": "Mobile money accounts", "bic": "BIC", "iban": "IBAN", "bank_name": "Bank Name", "operator": "Mobile Operator", "owner": "Account owner", "account_title": "Account title", "add_account": "Add Account", "little_balance": "You have insufficient balance", "pl_enter_amount": "Please enter amount", "verify_otp": "Verify Otp", "withdraw": "Withdraw", "manual_bank_transfer": "Manual Bank Transfer", "something_went_wrong": "Something went wong,please try again.", "code_sent": "OTP has been sent", "yob": "Your outstanding balance", "enter_withdraw_amount": "Enter amount you want to withdraw", "applied_fees": "Applied fees", "withdraw_all": "Withdraw All", "select_cashin": "Select cash in method", "exchange_rate": "Exchange Rate", "select_withdraw_method": "Select a withdraw method and account", "delete_account": "Delete", "edit_account": "Edit", "verify_withdraw": "Veri<PERSON>draw", "otp_code_form": "How do you want to receive the OTP code?", "enter_otp_code": "Enter the OTP code below", "by_email": "Receive code by Email", "by_sms": "Receive code by SMS", "enter_otp": "Enter OTP Code", "send_code": "Send Code", "withdraw_successful": "Withdraw Successful", "withdraw_successful_text": "Your withdraw has been taken into account and is being processed. you will be notified as soon as we have finalized the transactions.", "withdraws": "Withdraws", "transfers": "Transfers", "deposits": "Deposits", "deposit": "Deposits", "bulk_transfers": "Bulk transfers", "add_bank_account": "Add Bank Account", "add_mm_account": "Add Mobile money Account", "conditions": "Terms & Conditions", "total_withdraw_amount": "Total Withdraw amount", "edit_mm_account": "Edit Mobile Money Account", "edit_bank_account": "Edit Bank Account", "deleting_account": "Deleting Account", "deleting_account_desc": "Are you sure you want to delete this account?", "yes_delete": "Yes, Delete", "back_home": "Home", "select_bank": "Select a Bank", "select_bank_desc": "Select the bank to which you wish to transfer funds.", "reference": "Reference", "instant_transfers": "instant transfers", "all": "All transfers", "transaction_details": "Transaction details", "status": "Status", "fetching_statuses": "Fetching statuses", "name": "Full name", "details": "Details", "reason": "Reason", "payment_method": "Payment method", "sent_amount": "Sent amount", "amount_received": "Amount received", "mobile_money": "Mobile Money", "receiver": "Receiver", "sender": "Sender", "password": "Password", "enter_email": "Enter E-mail address", "enter_password": "Enter password", "verify": "Verify Ownership", "account_verification": "Account Verification", "account_verification_text": "Verify account ownership", "account_verification_description": "Enter the OTP Code  we sent to your email address.", "create_pin_title": "Create a Pin", "create_pin_desc": "Create a pin to protect your account", "confirm_pin_title": "Confirm  <PERSON>", "confirm_pin_desc": "Please enter identical codes.", "creating_pin": "creating pin ...", "pin_didnt_match": "<PERSON><PERSON> didn't match, please enter same pin", "code_required": "Verification Code required required", "logging_in": "Verifying pin...", "enter_pin_desc": "Enter 4 digit pin code", "enter_pin_text": "Pin Code", "pin_changed_successfully": "Pin code was changed successfully", "new_and_confirm_match": "New & confirm pins didn't match", "enter_old_pin": "Enter old Pin", "enter_old_pin_desc": "Enter pin your have been using.", "enter_new_pin": "Enter New PIn", "enter_new_pin_desc": "Set new pin for your account", "confirm_pin_title_desc": "Re-Enter pin to confirm your new pin", "changing_pin": "Changing pin...", "enter_pin_title": "Pin Code", "phone_numbers_didnt_match": "Phone number that was entered doesn't match this account", "please_enter_email": "Please enter your email", "please_enter_password": "Please enter your password", "no_transfer": "No Transfers", "no_transfer_desc": "All your transfers will be listed here", "pending": "Pending", "error": "Failed", "all_Favorites": "All Favorites", "search_by_name_phone_or_email": "Search contacts", "not_receiving_country": " is not a configured  receiving country for", "polling_processing_text": "Please wait while we process your payment.", "select_provider": "Select provider", "send_OTP": "Send OTP", "payment_Failed": "Payment Failed", "authentication_timeout": "The timeout for payment validation has been exceeded, the payment has been cancelled.", "authentication_failed": "Customer payment validation has failed, payment has been cancelled.", "unknown_reason": "Oops! something went wrong, Please contact <PERSON><PERSON>", "OTP_Verification": "OTP Verification", "USSD_Verification": "Verification using USSD", "MM_success": "", "customerinsufficientfunds": "You have insufficient balance to make this payment", "customeraccountLocked": "Your account is locked. Please contact the provider.", "forbiddenbyprovider": "Payment refused by the provider.", "timeout": "Oops! Payment timeout. Please try again later.", "toomanyrequest": "Too many requests. Please wait and try again later.", "msisdninvalid": "Please recheck your number and try again, or reach out to us for assistance.", "select_provider_txt": "Complete payment by selecting mobile money provider", "Successfully": "Successfully", "Successfully_text": "Our payment has been taken into account. As soon as we have received funds, you will be notified and we will execute the transfer.", "app_version": "Version ", "withdrawal_notice": "I specify how my beneficiary will withdraw the funds from Danapay upon receipt.", "Select_a_withdraw_mode": "How do you want the beneficiary to withdraw the money?", "withdraw_method": "Withdraw method", "Verify_CashOut": "Verify CashOut", "initiated": "Withdraw initiated", "history": "History", "select_provider_text": "Select mobile operation", "mm_ci_orange_sandboxotp": "Enter the opt message", "receipt": "Receipt", "save_share": "Save or share your receipt", "download_bd": "Download Bank Details", "download_bd_text": "Download and save bank details as an image", "save_exit": "Save & Exit", "save": "Save", "no_banks": "No Bank Accounts", "no_mm_accounts": "No mobile Money Accounts", "no_banks_text": " Go to your profile and add a bank account to continue.", "no_mm_accounts_text": "Go to your profile and add  a mobile money account to continue", "profile": "Profile", "instant_bank_transfer": "instant Bank transfer", "payment_reference_number": "Payment reference number", "verified": "Verified", "withdraw_success": "On the way!", "Withdraw_success_text": "Your withdrawal has been taken into account and is being processed. You will be notified as soon as we have finalized the transaction.", "withdrawal_fees": "<PERSON><PERSON><PERSON>", "verify_account": "Your account has not yet been verified. Please proceed to verification", "verify_btn": "Verify", "all_transfer": "All your transactions will be listed here", "all_transfer_title": "No Transactions", "payment_accepted": "Payment Accepted", "payment_in_progress": "Payment In Progress", "payment_cancelled": "Payment Cancelled", "transfer_started": "Transfer Started", "transfer_inprogress": "Transfer In Progress", "cashout_started": "Cashout Started", "cashout_inprogress": "Cashout In Progress", "cashout_completed": "Cashout Completed", "payout_started": "Payout Started", "payout_inprogress": "Payout In Progress", "payout_completed": "Payout Completed", "add_mm": "Add Mobile Money Account", "add_ba": "Add Bank Account", "the_deposit_is_in_progress": "Deposit is in progress", "the_payment_has_started": "Payment has started", "the_payment_has_failed": "Payment has failed", "the_deposit_is_completed": "Deposit is completed", "the_deposit_has_failed": "Deposit has failed", "the_cashout_is_in_progress": "Cashout is in progress", "the_cashout_is_completed": "Cashout is completed", "your_withdraw_has_failed": "Your withdrawal has failed", "your_withdraw_is_in_progress": "Your withdrawal is in progress", "your_withdraw_is_complete": "Your withdrawal is complete", "receiver_requested_a_payment": "Receiver requested a payment", "the_payment_is_completed": "Payment is completed", "the_transfer_is_in_progress": "Transfer is in progress", "the_transfer_is_completed": "Transfer is completed", "the_transfer_has_failed": "Transfer has failed", "suspicious": "Pending Approval", "please_select_bank": "Please select a bank to continue", "currency": "<PERSON><PERSON><PERSON><PERSON>", "ba_title": "Bank", "mm_title": "Mobile Money", "wrong_phone_title": "Phone Number Alert", "wrong_phone_message": "Your phone number doesn't match; we'll proceed with the number linked to your account. You can update it in the app.", "verification_pending": "Your Account verification is still pending", "individual": "Individual", "company": "Company", "please_accept_terms": "You have to accept Danapay Terms and Conditions to continue", "terms_and_conditions": "I accept the Danapay terms and conditions.", "resend_code": "Resend Code", "new_code_sent": "We have sent you another OTP code", "change_number": "Change Number", "yes_continue": "Yes, continue", "change_phone": "Change Phone Number", "change_phone_desc": "You will be sent an OTP to verify this phone number, so please ensure that the phone is nearby.", "update": "Update", "verification_was_successful": "Phone number was changed successfully", "phone_number_verification": "Phone Number verification", "phone_number_verification_desc": "We have send 6 digit  OTP code on this number, email it below", "phone_has_been_updated": "Phone number has been update successfully", "revenue": "Monthly Revenue Range", "job_type": "Job", "hear_about_us": "Hear About Us", "otp_code": "OTP Code", "other": "Where else did you hear about us?", "referralCode": "Referral Code", "personal_info": "Personal Information ", "personal_info_desc": "Tell us more about yourself", "revenue_source": "Revenue details", "revenue_source_desc": "Enter your revenue details", "hear_about_us_desc": "How did you hear about <PERSON><PERSON><PERSON>?", "Optional": "Optional", "use_otp": "OTP code", "use_password": "Password", "referred_by_someone": "Referred by someone", "Other": "Other", "wrong_email_or_password": "Wrong email or password.", "please_contact_us_for_help": "Please contact <PERSON><PERSON><PERSON> for assistance.", "reward": "<PERSON><PERSON>", "verify_header": "Verify your account ownership ", "verify_header_text": "You already have a Danapay account. Please enter the code you have just received by email", "verify_resend_code": "Receive a new code", "verify_sent_code": "Verify", "account_issue_text": "There appears to be an issue with your account; we kindly request you to get in touch with us for further assistance", "contact_danapay": "Issue with your account", "get_settings": "Go to setting and grant <PERSON><PERSON><PERSON> access to your contact", "only_for_individual": "The mobile app is only for individual users, Please login into the web app instead.", "No_Internet_Connection": "No Internet Connection", "No_Internet_Connection_Note": "Please check your internet connection", "No_Internet": "Try Again", "invalid_phone_number": "Phone number is not valid.", "phone_number_missing": "Please specify the number.", "account_added": "Account added successfully", "account_activated": "Your account has been activated", "account_deactivated": "Your account has been de-activated", "payment_status_change": "Your transaction status has been updated.", "account_activated_title": "Account Activation", "event": "From an event", "event_name": "Event", "waemu_cemac_person": "Living in and a resident of the WAEMU or CEMACs", "resend_code_in": "Resend code in", "seconds": "seconds", "other_job_type": "Others, please type job here.", "address_details": "Address details", "address_line": "Address Line", "address_details_desc": "Don't worry, your information is safe with us. ", "receiving_countries": "Transfer Countries", "receiving_countries_desc": "Specify the countries to which you plan to make transfers once registered.", "prestataires-de-service-des-activites-numerique": "Digital services providers", "banquemonnaie-electronique": "Banking/Electronic money", "informatique": "Information technology", "restaurationbar": "Restaurant/Bar", "sportsportif": "Sport/Sportsman", "societe-de-prevention-et-de-securite": "Prevention and security company", "auto-entrepreneur": "Self-employed contractor", "commerce-produits-alimentaires": "Trade in food products", "commerce-produits-luxes": "Luxury products trade", "commerce-autres-produits": "Trade in other products", "profession-liberale": "Self-employed", "financejuridique": "Finance / Legal", "manutention": "Materials handling", "technicien-de-surface": "Surface technicians", "autres-activites": "Other activities", "city_is_required": "City is required", "address_is_required": "Address line is required", "email_is_invalid": "Email is required and must be valid", "email_is_required": "Email is required", "first_name_is_required": "First name is required", "last_name_is_required": "Last name is required", "please_select_your_activity": "Please select your activity", "monthly_revenue_is_required": "Monthly revenue is required", "please_select_country": "Please select country", "should_be_an_array": "Countries should be an array", "job_is_required": "Job is required", "job": "Job / Activity", "please_select_where_you_heard_about_us": "Please select where you heard about us", "please_enter_event_name": "Please enter event name", "please_enter_other_source": "Please enter other source", "please_enter_referral_code": " Please enter referral code", "bank_payout_internal": "Bank", "account_not_verified": "Your account is not verified yet", "account_not_verified_desc": "To performany transaction on Danapay you need to verify your account", "waemu_cemac_person_description": "The Union Monétaire Ouest Africaine (UMO) residency option is available for users residing in the West African Economic and Monetary Union countries. Selecting this option means that the your financial activities will be regulated according to the UMO standards and regulations.", "select_residency": "Please select your residence", "kyc_pending_title": "A little more patience!", "kyc_pending_message": "We have received your documents and they are being verified. In the meantime, you have limited access to our services. Need help?", "kyc_success_title": "Thank you for your patience!", "kyc_success_message": "Your identity has been verified but your account requires further checking. Need help?", "kyc_start_title": "Ready to make your first transfer?", "kyc_start_message": "First, complete the verification of your account by clicking here!", "cta_text_whatsapp": "Send Us Message", "cta_text_complete": "Complete Verification", "validated": "Validated", "mobile_money_internal": "Mobile Money", "bank_transfer_instruction_title": "The cheapest", "bank_transfer_instruction_description": "Make a transfer to danapay. Our RIB will be provided to you at the last stage. To speed up your transaction, make an instant transfer from your bank. The transfer will be executed as soon as we have received the funds in our bank account", "bank_card_instruction_title": "The most convenient way", "bank_card_instruction_description": "You will be redirected to our partner Monext - Payline to make your payment The transfer will be executed immediately after your payment.", "otp_channel": "OTP Channel", "otp_channel_description": "Where do you want toreceive the OTP.", "verification_failed": "Incorrect code. Please try again.", "select_cash_in_method_title": "Select CashIn Method", "select_cash_in_method_body": "Choose how you'd like to add funds to your account.", "max_limit": "Maximum amount", "cash_in_method": "Cashin Method", "confirm_add_description": "Add a contact and start an instant payment to him in only one simple click.", "account_type": "Account Type", "company_name": "Company Name", "contact_first_name": "Contact first name", "contact_last_name": "Contact last name", "first_name": "First name", "last_name": "Last name", "invite_contact": "Create", "code": "code", "account_type_required": "Account type is required.", "phone_number_required": "Phone Number is required.", "first_name_required": "First name is required.", "last_name_required": "Last name is required.", "company_name_required": "Company name is required.", "country_required": "Please select country.", "contact_email_optional": "Contact email [Optional]", "both_names_required": "Both names are required.", "amount_out_of_range": "Maximum amount exceeded for this payment method. Please choose another payment method", "beneficiary_title": "Beneficiary Information", "beneficiary_subTitle": "Please select the beneficiary", "beneficiary_subTitle_form": "Or create a new beneficiary", "beneficiary_select_country": "Select a country", "create": "Create", "pay_info_title": "Payment Information", "pay_info_reason": "Specify the reason for your transfer", "pay_info_amount": "Enter the information related to the amount", "amount_to_send": "Amount sent", "amount_to_receive": "Amount received", "withdrawal_option_title": "Receiving Method", "withdrawal_option_desc": "Choose the method of making funds available to your beneficiary", "delay": "Delay", "limit": "Limit", "exceeds_limit": "You cannot use this method because the amount exceeds the authorized limit.", "mfi_by_aloohpay": "Microfinance", "mobile_money_title": "Make sure it’s the correct number as we do not perform any checks before sending.", "bank_title": "Your beneficiary must be the holder of the specified bank account.", "account_owner_text": "Account holder", "enter_iban": "Enter a new IBAN", "select_operator": "Select the operator", "select_bank_name": "click to select the bank", "account_owner_texr": "Account holder", "registered_id": "Registered ID", "bank_payout_external": "Bank", "reason_required": "Reason for transfer is required.", "amount_in_cfa_required": "Amount to received is required.", "amount_in_euro_required": "Amount to send is required.", "valid_iban": "The IBAN is invalid.", "mising_iban": "Please specify the IBAN.", "bank_required": "Please Select a bank", "operator_required": "Please selected the operator", "balance_low": "This payment method is unavailable because you have no balance.", "cashInTittle": "Payment Method", "cashInDesc": "Choose a payment method.", "comfirm_pay": "Confirm and Pay", "wrong_name_title": "", "wrong_name_desc": "The name entered does not appear to be a human name. Please enter a valid name.", "wrong_name_cancel": "Change", "wrong_name_continue": "Proceed", "create_a_beneficiary": "Create a beneficiary", "request_permission1": "Unable to load.", "request_permission2": "Please grant access to your contacts.", "request": "Allow", "mm_summary_text": "Your beneficiary should receive the funds in their mobile account within 5 minutes after your payment is received.", "t_fees": "Transfer fees", "w_fees": "Withdrawal fees", "bank_transfer_by_hub2": "Bank", "postal_code": "Postal Code", "search_your_location": "Address", "postal_code_required": "Your country requires a postal code. Please enter your postal code", "search_for_country": "Search for country", "select_all": "Select all", "clear_all": "Clear all", "postal_code_optional": "Postal code [optional]", "method_of_receiption": "Mode of reception", "transaction_detail": "Transaction details", "amount_sent": "Amount sent", "fee_paid": "Fee paid", "mode_de_payment": "Mode of payment", "bank": "Bank", "sending": "Sending", "receiving": "Receiving", "payment_initiated": "Payment initiated", "payment_initiated_message": "Payment initiated", "transfer_in_progress_message": "The transfer is in progress", "transfer_completed_message": "Fund deposited on the user Danapay wallet", "payment_completed_message": "Fund received by <PERSON><PERSON><PERSON>", "download_the_receipt": "Download the receipt", "file_downloaded": "Receipt has been downloaded to your downloads folder", "email_taken": "This email is already taken. Please use another one.", "montant_incorrect_lower": "Amount is below the minimum allowed limit", "montant_incorrect_higher": "Amount exceeds the maximum allowed limit", "funds_sent_to": "Funds sent to", "bank_selected_text": "Your beneficiary should receive the funds in their mobile account within 72 hours after your payment is received.", "delivery_selected_text": "Your beneficiary should receive the funds in their bank account within 6 hours after your payment is received.", "payment_failed_title": "Payment Failed", "payment_failed_description": "Oops! Something went wrong. Your account has not been debited.", "other_methods": "Try using one of the payment methods below.", "bank_transfer_option": "Kindly choose the bank to proceed.", "bank_transfer_subtitle": "Select the type of transfer you will make from your bank account.", "recommended": "Recommended", "cash_in_alert": "Your transfer will be executed as soon as we receive the funds", "transfer_success": "Transfer acknowledged", "pay": "Pay", "discovery": "Discovery", "discovery_text": "Please specify how you discovered us.", "identity": "Identity", "identity_text": "Enter your information as it appears on your identification document.", "income": "Income", "income_text": "Enter your income details.", "sending_country_text": "Specify the countries to which you will make transfers.", "rejected_title": "Verification Rejected", "rejected": "Following the review of your documents, we regret to inform you that we are unable to activate your account.", "isIndividual": "This application is exclusively for individual users. Please use our website if you are a business.", "select_a_response": "Select a response", "activity_or_employment": "Activity or employment", "monthly_income_range": "Monthly income range", "select_one_or_more_countries": "Select one or more countries", "non_eu_person": "I have a residence permit or a receipt", "eu_person": "I have a European National Identity", "verify_residency": "Residency", "verify_residency_text": "Specify your current residency status", "eu_person_description": "By selecting this option, you confirm that you hold the nationality of one of the European Union countries and reside there permanently.", "non_eu_person_description": "By selecting this option, you confirm that you are from a non-European Union country but reside there permanently.", "verification_rejected": "Verification rejected", "identification_required": "Identification required", "identification_btn_text": "Complete my verification.", "identification_required_desc": "To use our services, it is necessary for us to verify your identity.", "Verification_in_progress": "Verification in progress", "Verification_in_progress_desc": "We have received all your documents, and your account is currently under verification. You will receive an email once your account is activated.", "wait_a_little_longer": "Please wait a little longer.", "wait_a_little_longer_desc": "Our partner SumSub has successfully verified your identity, but we are conducting some additional internal checks. You will receive an email once your account is activated.", "required_actions": "Required actions.", "required_actions_btn_text": "Update my information", "required_actions_desc": "Your account is temporarily inaccessible. We need additional information or documents from you.", "restricted_account": "Restricted account", "restricted_account_desc": "In order to access all payment methods, please make an initial transfer by paying via bank transfer.", "existing_account": "Existing account", "existing_account_desc": "You already have a Danapay account.", "existing_account_text": "You have received a verification code at your email address ", "enter_the_code": "Enter the code", "email_verification": "<PERSON><PERSON>", "invalid_code": "Invalid verification code", "okay": "Okay", "changeEmail": "Change Email", "too_many_attempts": "Too many attempts", "your_pin_code_is_invalid": "Invalid PIN code", "transaction_failed": "Your payment has failed. Please try again with another payment method.", "transaction_cancelled": "Your payment has been cancelled. Please try again with another payment method.", "transaction_completed": "Did you enjoy your experience? Click here to recommend us.", "action_required_title": "Waiting for Documentation", "action_required_text": "Your transaction has been suspended. Please send your last three bank statements to the email address ", "delete_button_text": "Delete", "transaction_progress": "Your beneficiary will receive the funds in their Danapay account.", "use_another_method": "Use another method", "PhoneNumber_placeholder": "Enter a new number", "send_the_documents": "Send the documents", "transfer_succeeded": "Transfer completed successfully", "stand_by": "Stand By", "select_bank_place_holder": "Select the bank", "file_uploaded_successfully": "File uploaded successfully", "file_upload_failed": "The file upload was unsuccessful. Please send us an <NAME_EMAIL>.", "select_residency_error": "Please select a residency option", "please_enter_otp_code_sent_to_email": "Please enter code.", "cant_be_more_then_six": "The code can only be  six digits", "Phone number provided is invalid": "Phone number provided is invalid", "select_receiving_countries_error": "At least one country should be selected", "en.email_is_required.email_is_invalid": "Please enter a valid email address", "user_verified_notification": "Your verification has been successfully completed.", "user_activated_notification": "Your account has been successfully activated.", "method_of_receipt": "Method of receipt", "deposit_success": "<PERSON><PERSON><PERSON><PERSON> executed successfully", "withdrawal_success": "<PERSON><PERSON><PERSON> completed successfully", "select_countries": "Select one or more countries", "done": "Done", "end_reached": "Loading completed", "view_all": "View all", "get_started": "Get Started", "invalide_code": "Invalid Verifcation code.", "invoice_available_to_senders": "Invoice available for senders to send", "contact_name": "Contact name", "confirm": "Confirm", "delete_contact": "Remove from my favorites", "favorited_deleted": "Favorite deleted successfully", "thank_you": "We appreciate your feedback", "request failed with status code 500": "Oops! Something went wrong on our end. Please try again later.", "payment_updated": "Operation has been updated successfully.", "withdrawal_updated": "Withdrawal has been updated successfully.", "transfer_updated": "Operation has been updated successfully.", "user_updated": "User has been updated successfully.", "notification": "Notification", "bank_payout_by_hub_2": "Bank Account", "validation_unique_email": "This email is already in use. Try logging in or use a different one.", "validation_unique_phone": "Phone number is already taken", "network_error": "Please check your Internet connection and try again.", "server_error": "Server error: Please try again later.", "sumsubError": "Please try again later, or contact support", "validation_required_operator": "The operator is required.", "validation_required_bank_name": "The bank name is required.", "validation_required_institution_id": "The institution id is required.", "validation_required_iban": "The iban is required.", "validation_required_phone_number": "The phone number is required.", "validation_required_email": "The email is required.", "validation_required_phone": "The phone number is required.", "withdrawal_title": "<PERSON><PERSON><PERSON>", "withdrawal_description": "Specify how your beneficiary will withdraw the funds from Danapay upon receipt.", "withdraw_amount": "Withdraw amount", "mfi": "Microfinance", "wallet": "Wallet", "wallet_account_number": "Wallet Account Number", "owner_name": "Owner Name", "title": "Title", "add_neero_wallet": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "mobile_money_by_neero": "Mobile", "wallet_by_neero": "<PERSON><PERSON><PERSON>", "neero_description": "Your beneficiary will receive the funds in their ", "neero_wallet": "<PERSON><PERSON><PERSON>", "choose_phone_number": "Choose Phone Number", "account_number": "Wallet account number", "location_permission": "Location permission", "location_permission_description": "<PERSON><PERSON><PERSON> would like to use your location to detect your a country", "ask_me_later": "Ask me later", "validation_regex_first_name": "First name doest not look to be a human", "no_neero_wallet_number": "Please enter your Neero wallet number", "input_contains_invalid_characters": "Input contains invalid characters. Please use only letters, numbers, and basic punctuation.", "verification_code": "Verification code", "validation_max_string_phone_number": "Phone number is too long", "please_enter_valid_code": "Please enter a valid code", "otp_code_invalid": "Invalid OTP code", "otp_code_too_short": "OTP code is too short", "otp_code_too_long": "OTP code is too long", "otp_code_digits_only": "OTP code must contain only digits", "validation_regex_last_name": "Last name doest not look to be a human", "validation_operation_operator": "The operator is required", "mobile_money_external": "Mobile", "no_rate": "The rate is not set in the configuration", "amount_below_minimum_range": "Amount below minimum range", "amount_above_maximum_range": "Amount above maximum range", "reward_title": "Refer a friend and earn ", "reward_description": "You will receive your reward once they have made a transfer of at least €50 to the recipient of their choice", "reward_code": "Referral code", "reward_share": "Share via WhatsApp", "no_rewards": "No Rewards", "reward_list": "Referrals", "rewards": "Rewards", "reward_share_message": "Hello. I recommend you to use the Danapay application for you next transfers. You can sent up to 7000€ at 1€ fees. Sign upwith this referral code ", "reward_share_title": "Danapay Referral", "campaign_referral_reward": "<PERSON><PERSON>"}