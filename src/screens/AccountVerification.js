import {
  View,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  StatusBar,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
  Image,
  KeyboardAvoidingView,
  Alert,
} from 'react-native';
import React, {useState, useEffect} from 'react';
import {setI18nConfig, translate} from '../utilities/Translate';
import {colors, pagePadding} from '../theme';
import Icon from 'react-native-vector-icons/Feather';
import TextCp from '../components/TextCp';
import DanaButton from '../components/DanaButton';
import Input from '../components/Input';
import {app_logout, checkUser, verifyEmailForExistingUser} from '../apis/auth';
import useDanaStore from '../app_state/store';
import Divider from '../components/Divider';
import {showMessage} from 'react-native-flash-message';
import Intercom from '@intercom/intercom-react-native';
import {navigateWithNoHistory} from '../utilities/ForgetHistroty';
import {CommonActions} from '@react-navigation/native';

const keyboardVerticalOffset = Platform.OS === 'ios' ? 40 : 0;

const AccountVerification = props => {
  const temp_user = useDanaStore(state => state.temp_user);
  const resetStore = useDanaStore(state => state.resetStore);
  const [otp_code, setOtpCode] = useState('');
  const [resending, setResending] = useState(false);
  const [loading, setLoading] = useState(false);

  const verifyOTP = () => {
    if (!otp_code) {
      showMessage({
        type: 'danger',
        message: translate('please_enter_otp_code_sent_to_email'),
      });
      return;
    }

    if (otp_code.length !== 6) {
      showMessage({
        type: 'danger',
        message: translate('cant_be_more_then_six'),
      });
      return;
    }
    setLoading(true);
    verifyEmailForExistingUser({verification_code: +otp_code})
      .then(res => {
        props.navigation.navigate('Setpin');
        setLoading(false);
      })
      .catch(error => {
        setLoading(false);
        if(error.response.status === 400) {
          showMessage({
            type: 'danger',
            message: translate('invalide_code'),
          });
        }else{
          showMessage({
            type: 'danger',
            message: error.message,
          });
        }
       
      });
  };

  const resend_code = async () => {
    try {
      setResending(true);
      const obj = {
        phone_number: temp_user?.phone_number,
        country_code: temp_user?.country_code,
      };
      const res = await checkUser(obj);
      setResending(false);
    } catch (error) {
      setResending(false);
    }
  };

  const logout = async () => {
    Alert.alert(
      '',
      translate('logoutText'),
      [
        {
          text: translate('no'),
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: translate('yes'),
          onPress: () => {
            app_logout()
              .then(res => {
                try {
                  Intercom.logout();
                } catch (error) {}
                resetStore();
                navigateWithNoHistory(props, CommonActions, 'Tutorial');
              })
              .catch(error => {});
          },
        },
      ],
      {cancelable: false},
    );
  };

  useEffect(() => {
    setI18nConfig();
  }, []);

  return (
    <SafeAreaView style={styles.page}>
      <StatusBar backgroundColor={'#fff'} barStyle={'dark-content'} />
      <View style={styles.header}>
        <View>
          <TextCp style={{fontSize: 24, marginBottom: 10}} textType="bold">
            {translate('existing_account')}
          </TextCp>
          <TextCp>{translate('existing_account_desc')}</TextCp>
        </View>
        <TouchableOpacity onPress={logout}>
          <Icon name="x-circle" size={24} color={'#444'} />
        </TouchableOpacity>
      </View>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : ''}
        keyboardVerticalOffset={keyboardVerticalOffset}
        keyboardShouldPersistTaps={true}
        style={{
          flex: 1,
        }}>
        <ScrollView
          contentContainerStyle={{flexGrow: 1, paddingHorizontal: 16}}
          showsVerticalScrollIndicator={false}>
          <View style={{flexGrow: 1}}>
            <View style={{flex: 1}}>
              <Image
                source={require('../images/login.jpg')}
                style={{height: '100%', width: '100%', borderRadius: 8}}
              />
            </View>
            <View style={{marginVertical: 16}}>
              <TextCp>
                {`${translate('existing_account_text')}`}
                <TextCp textType={'bold'} color={colors.primary}>
                  {temp_user?.masked_email}
                </TextCp>
              </TextCp>
              <Divider height={16} />
              <Input
                label={translate('enter_the_code')}
                value={otp_code}
                onChange={text => setOtpCode(text)}
                showLabel={false}
                backgroundColor="#fff"
              />
              <Divider height={16} />

              <DanaButton
                onPress={() => verifyOTP()}
                title={translate('verify_btn')}
                theme="#000"
                loading={loading}
                textColor="#eee"
              />
              <Divider height={16} />

              <TouchableOpacity
                onPress={!resending && resend_code}
                style={{
                  height: 48,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                {resending ? (
                  <ActivityIndicator />
                ) : (
                  <TextCp
                    textType={'bold'}
                    color={colors.primary}
                    align="center">
                    {translate('verify_resend_code')}
                  </TextCp>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  page: {
    backgroundColor: '#fff',
    flex: 1,
  },
  toolbar: {
    height: 56,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: pagePadding,
    marginVertical: 8,
  },
  content: {
    padding: pagePadding,
    flexGrow: 1,
    backgroundColor: 'orange',
  },
  backBtn: {
    justifyContent: 'center',
    height: 56,
    width: 76,
  },
  input: {
    width: '100%',
    height: 56,
    backgroundColor: '#fff',
    borderRadius: 8,
    marginTop: 8,
    marginBottom: 16,
    paddingHorizontal: 16,
    fontFamily: 'Inter-Regular',
    color: '#444',
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});

export default AccountVerification;
