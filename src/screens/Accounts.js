import {
  View,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import Icon from 'react-native-vector-icons/Feather';
import {setI18nConfig, translate} from '../utilities/Translate';
import {titleFontSize} from '../theme';
import TextCp from '../components/TextCp';
import useDanaStore from '../app_state/store';
import AddAccountModal from '../components/AddAccountModal';
import AccountCard from '../components/AccountCard';

const Accounts = ({navigation, route}) => {
  const {type} = route.params;
  const mmAccounts = useDanaStore(state => state.mmAccounts);
  const bankAccounts = useDanaStore(state => state.bankAccounts);
  const getBankAccounts = useDanaStore(state => state.getBankAccounts);
  const getMMAccounts = useDanaStore(state => state.getMMAccounts);

  const [accounts, setAccounts] = useState({
    account_type: type,
  });
  const [show, setShow] = useState(false);

  const refreshList = () => {
    getMMAccounts();
    getBankAccounts();
  };

  useEffect(() => {
    setI18nConfig();
    refreshList();
  }, []);

  return (
    <SafeAreaView style={{flex: 1}}>
      <StatusBar backgroundColor={'#f1f1f1'} barStyle="dark-content" />
      <View style={styles.toolbar}>
        <TextCp style={styles.h1} textType="bold">
          {translate('manage_accounts')}
        </TextCp>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="x-circle" color="#333" size={32} />
        </TouchableOpacity>
      </View>
      <ScrollView
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}>
        <View style={styles.tabs}>
          <TouchableOpacity
            onPress={() =>
              setAccounts(prev => ({...prev, account_type: 'bank_accounts'}))
            }
            style={[
              styles.tab,
              {
                borderBottomWidth:
                  accounts.account_type === 'bank_accounts' ? 2 : 0,
                borderBottomColor:
                  accounts.account_type === 'bank_accounts'
                    ? '#037375'
                    : '#aaa',
              },
            ]}>
            <TextCp
              textType={
                accounts.account_type === 'bank_accounts' ? 'bold' : 'regular'
              }>
              {translate('ba_title')}
            </TextCp>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() =>
              setAccounts(prev => ({
                ...prev,
                account_type: 'mobile_accounts',
              }))
            }
            style={[
              styles.tab,
              {
                borderBottomWidth:
                  accounts.account_type === 'mobile_accounts' ? 2 : 0,
                borderBottomColor:
                  accounts.account_type === 'mobile_accounts'
                    ? '#037375'
                    : '#aaa',
              },
            ]}>
            <TextCp
              textType={
                accounts.account_type === 'mobile_accounts' ? 'bold' : 'regular'
              }>
              {translate('mm_title')}
            </TextCp>
          </TouchableOpacity>
        </View>
        <TouchableOpacity onPress={() => setShow(true)} style={styles.addBtn}>
          <TextCp textType={'bold'} color="#fff">
            <Icon name="plus" size={14} color="#fff" />
            {'  '}
            {accounts.account_type === 'mobile_accounts'
              ? translate('add_mm_account')
              : translate('add_bank_account')}
          </TextCp>
        </TouchableOpacity>
        <View>
          {accounts.account_type === 'mobile_accounts' ? (
            <>
              {mmAccounts.map((mm_account_details, index) => (
                <AccountCard
                  refreshList={refreshList}
                  details={mm_account_details}
                  key={index}
                  type={accounts.account_type}
                  translate={translate}
                  style={{}}
                />
              ))}
            </>
          ) : (
            <>
              {bankAccounts.map((ba_account_details, index) => (
                <AccountCard
                  refreshList={refreshList}
                  details={ba_account_details}
                  key={index}
                  type={accounts.account_type}
                  translate={translate}
                  style={{}}
                />
              ))}
            </>
          )}
        </View>
        <View style={{height: 100}}></View>
      </ScrollView>
      {/* Modal */}
      <AddAccountModal
        show={show}
        setShow={() => {
          setShow(false);
          getMMAccounts();
          getBankAccounts();
        }}
        onModalHide={() => {}}
        translate={translate}
        type={accounts.account_type}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  page: {
    flex: 1,
    backgroundColor: '#f1f1f1',
  },
  toolbar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    height: 56,
  },
  h1: {
    fontSize: titleFontSize,
    color: '#222',
  },
  content: {
    padding: 16,
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    borderBottomColor: '#aaa',
    borderBottomWidth: 0.4,
  },
  tab: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 15,
    flex: 1,
  },
  addBtn: {
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#037375',
    marginVertical: 10,
    flexDirection: 'row',
    elevation: 2,
    borderRadius: 8,
  },
  row: {
    flexDirection: 'row',
  },
  row_btn: {
    // flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 20,
  },
});

export default Accounts;
