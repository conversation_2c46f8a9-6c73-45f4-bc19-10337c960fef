import {
  View,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  TextInput,
  Image,
  ActivityIndicator,
  StatusBar,
} from 'react-native';
import {translate, setI18nConfig} from '../utilities/Translate';
import Icon from 'react-native-vector-icons/Feather';
import React, {useEffect, useState} from 'react';
import UserAvatar from 'react-native-user-avatar';
import TextCp from '../components/TextCp';
import useDanaStore from '../app_state/store';
import {flags} from '../images/flags';
import {showMessage} from 'react-native-flash-message';
import FavouriteListItem from '../components/FavouriteListItem';

const AllContacts = ({navigation}) => {
  const contacts = useDanaStore(state => state.contacts);
  const user = useDanaStore(state => state.user);
  const countries = useDanaStore(state => state.countries);
  const saveBeneficiary = useDanaStore(state => state.saveBeneficiary);
  const fetchContacts = useDanaStore(state => state.fetchContacts);
  const fetching_contacts = useDanaStore(state => state.fetching_contacts);
  const saveSendingCountry = useDanaStore(state => state.saveSendingCountry);
  const [clone, setClone] = useState(contacts);
  const [query, setQuery] = useState('');

  const goBack = () => {
    navigation.goBack();
  };

  const search = query_text => {
    setQuery(query_text);
    if (query_text.length > 0) {
      const result = clone.filter(
        val =>
          val.favorite.full_name
            ?.toLowerCase()
            .includes(query_text.toLowerCase()) ||
          val.favorite.email
            ?.toLowerCase()
            .includes(query_text.toLowerCase()) ||
          val.favorite.phone_number?.includes(query_text.toLowerCase()),
      );
      setClone(result);
    } else {
      setClone(contacts);
    }
  };

  const startTransfer = favorite => {
    if (user?.is_verified && user?.is_active) {
      const sending_country = countries.find(
        val => val.country_code === user?.country_code,
      );

      if (!sending_country) {
        return;
      }

      saveSendingCountry(sending_country);

      const country_object = sending_country?.receiving_countries?.find(
        val =>
          val.receiving_country?.name?.toLowerCase() ===
          favorite?.country?.toLowerCase(),
      );
      if (!country_object) {
        showMessage({
          type: 'danger',
          message: `${favorite?.country} ${translate('not_receiving_country')} ${
            user?.country
          }`,
        });
        return;
      }

      saveBeneficiary({...favorite, country: country_object?.receiving_country});
      navigation.navigate('BeneficiarySelection');
    } else {
      navigation.navigate('Home');
    }
  };

  useEffect(() => {
    setI18nConfig();
    fetchContacts();
  }, []);

  return (
    <SafeAreaView style={styles.page}>
      <StatusBar backgroundColor={'#fff'} barStyle={'dark-content'} />
      <View style={styles.toolbar}>
        <TextCp style={{fontSize: 20}} textType={'bold'}>
          {translate('all_Favorites')}
        </TextCp>
        <TouchableOpacity onPress={goBack} style={styles.btn}>
          <Icon name="x-circle" size={24} color="#000" />
        </TouchableOpacity>
      </View>

      <View style={styles.search}>
        <Icon name="search" size={20} color="#aaa" />
        <TextInput
          onChangeText={text => search(text)}
          value={query}
          placeholder={translate('search_by_name_phone_or_email')}
          style={styles.input}
          placeholderTextColor={'#aaa'}
        />
      </View>
      {fetching_contacts && (
        <View
          style={{justifyContent: 'center', alignItems: 'center', height: 100}}>
          <ActivityIndicator />
        </View>
      )}
      <FlatList
        data={clone.filter(
          user => user.favorite.full_name && user.favorite.phone_number,
        )}
        keyExtractor={item => item.favorite.id}
        renderItem={({item}) => (
          <FavouriteListItem
            flags={flags}
            item={item}
            startTransfer={item => startTransfer(item)}
          />
        )}
        contentContainerStyle={styles.content}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  page: {
    flex: 1,
    backgroundColor: '#fff',
  },
  toolbar: {
    paddingHorizontal: 16,
    marginVertical: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  content: {
    paddingHorizontal: 16,
  },
  btn: {
    height: 48,
    width: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    backgroundColor: '#ffffff66',
    marginVertical: 4,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  itemDetails: {
    marginHorizontal: 14,
    flex: 1,
  },
  title: {
    fontSize: 17,
    marginBottom: 4,
    textTransform: 'capitalize',
  },
  search: {
    marginHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    borderWidth: 1,
    borderRadius: 8,
    borderColor: 'rgba(0, 0, 0, 0.2)',
  },
  input: {
    paddingVertical: 8,
    paddingLeft: 16,
    height: 48,
    fontFamily: 'Inter-Regular',
    fontSize: 15,
    flex: 1,
    color: '#444',
  },
  flag: {
    height: 18,
    width: 18,
    borderRadius: 9,
    position: 'absolute',
    bottom: 1,
    right: 1,
    borderWidth: 2,
    borderColor: '#fff',
  },
});

export default AllContacts;
