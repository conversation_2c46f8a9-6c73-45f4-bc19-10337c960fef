import React, {useEffect} from 'react';
import {
  View,
  StyleSheet,
  StatusBar,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Dimensions,
} from 'react-native';
import DanaButton from '../components/DanaButton';
import TextCp from '../components/TextCp';
import {pagePadding, subtitleFontSize, scaleRatio} from '../theme';
import Clipboard from '@react-native-clipboard/clipboard';
import {setI18nConfig, translate} from '../utilities/Translate';
import Icon from 'react-native-vector-icons/Feather';
import useDanaStore from '../app_state/store';
import {CustomerIO} from 'customerio-reactnative';
import useGetLang from '../Hooks/useGetLang';
import LottieView from 'lottie-react-native';

const {width} = Dimensions.get('window');
const BankerTransfer = props => {
  const user = useDanaStore(state => state.user);
  const lang = useGetLang();
  const bankTransferType = useDanaStore(state => state.bankTransferType);
  const restoreTransfer = useDanaStore(state => state.restoreTransfer);
  const updateTransferTimer = useDanaStore(state => state.updateTransferTimer);
  const updateDepositTimer = useDanaStore(state => state.updateDepositTimer);
  const activeOperation = useDanaStore(state => state.activeOperation);
  const increaseTries = useDanaStore(state => state.increaseTries);

  useEffect(() => {
    setI18nConfig();
  }, []);

  const goHome = () => {
    restoreTransfer();
    if (activeOperation === 'transfer') {
      updateTransferTimer('reset');
      increaseTries({type: 'reset', operations: 'transfer'});
      CustomerIO.setProfileAttributes({
        'Transfer Done': true,
      });
      CustomerIO.track('Transfer Submitted');
    } else if (activeOperation === 'deposit') {
      updateDepositTimer('reset');
      increaseTries({type: 'reset', operations: 'deposit'});
      CustomerIO.setProfileAttributes({
        'Deposit Done': true,
      });
      CustomerIO.track('Deposit Submitted');
    }
    props.navigation.navigate('Home');
  };

  const copy = text => {
    Clipboard.setString(text);
  };

  return (
    <SafeAreaView style={styles.page}>
      <StatusBar barStyle="dark-content" backgroundColor={'#fff'} />

      <ScrollView
        style={styles.footer}
        contentContainerStyle={{paddingHorizontal: 16}}>
        <View style={styles.header}>
          <LottieView
            source={require('../lottie/success.json')}
            autoPlay
            style={{
              alignSelf: 'center',
              width: 80,
              height: 80,
              marginVertical: 20,
            }}
            loop={false}
          />
          <View style={styles.headerContent}>
            <TextCp style={styles.BC_title} textType="bold" align="center">
              {translate('BC_title')}
            </TextCp>

            <TextCp style={styles.BC_sub_title} align="center">
              {translate('BC_sub_title')}
            </TextCp>
          </View>
        </View>

        <View style={styles.alert}>
          <View style={styles.alert_header}>
            <Icon
              name="alert-triangle"
              color="#F5A05B"
              size={20}
              style={styles.alert_icon}
            />
            <TextCp textType="bold">{translate('bank_transfer_alert')}</TextCp>
          </View>
          {lang === 'fr' ? (
            <TextCp color="rgba(71,47,1,.75)">
              L'utilisation d'un{' '}
              <TextCp color="rgba(71,47,1,.75)" textType={'bold'}>
                compte bancaire qui ne vous appartient pas{' '}
              </TextCp>
              entraînera la restitution des fonds, ce qui retardera votre
              transaction.
            </TextCp>
          ) : (
            <TextCp color="rgba(71,47,1,.75)">
              The use of{' '}
              <TextCp color="rgba(71,47,1,.75)" textType={'bold'}>
                a bank account that does not belong to you
              </TextCp>{' '}
              will result in the return of the funds, which will delay your
              transaction.
            </TextCp>
          )}
        </View>

        <View style={styles.details}>
          <View>
            <View style={styles.item}>
              <TextCp style={styles.title} color="rgba(0,0,0,.5)">
                {translate('owner')}
              </TextCp>

              <TouchableOpacity
                style={styles.copy}
                onPress={() => copy(bankTransferType.owner_name)}>
                <TextCp style={styles.name} color="rgba(0,0,0,.75)">
                  {bankTransferType.owner_name}
                </TextCp>
                <Icon name="copy" color="rgba(0,0,0,.3)" size={15} />
              </TouchableOpacity>
            </View>
            <View style={styles.item}>
              <TextCp style={styles.title} color="rgba(0,0,0,.5)">
                {translate('bank_name')}
              </TextCp>

              <TouchableOpacity
                style={styles.copy}
                onPress={() => copy(bankTransferType.bank_name)}>
                <TextCp style={styles.name} color="rgba(0,0,0,.75)">
                  {bankTransferType.bank_name.split('-')[1]}
                </TextCp>
                <Icon name="copy" color="rgba(0,0,0,.3)" size={15} />
              </TouchableOpacity>
            </View>

            <View style={styles.item}>
              <TextCp style={styles.title} color="rgba(0,0,0,.5)">
                {translate('country')}
              </TextCp>

              <TouchableOpacity
                style={styles.copy}
                onPress={() => copy(bankTransferType.country)}>
                <TextCp style={styles.name} color="rgba(0,0,0,.75)">
                  {bankTransferType.country}
                </TextCp>
                <Icon name="copy" color="rgba(0,0,0,.3)" size={15} />
              </TouchableOpacity>
            </View>

            <View style={styles.item}>
              <TextCp style={styles.title} color="rgba(0,0,0,.5)">
                {translate('bic')}
              </TextCp>

              <TouchableOpacity
                style={styles.copy}
                onPress={() => copy(bankTransferType.bic)}>
                <TextCp style={styles.name} color="rgba(0,0,0,.75)">
                  {bankTransferType.bic}
                </TextCp>
                <Icon name="copy" color="rgba(0,0,0,.3)" size={15} />
              </TouchableOpacity>
            </View>

            <View style={styles.item}>
              <TextCp style={styles.title} color="rgba(0,0,0,.5)">
                {translate('iban')}
              </TextCp>
              <TouchableOpacity
                style={styles.copy}
                onPress={() => copy(bankTransferType.iban)}>
                <TextCp style={styles.name} color="rgba(0,0,0,.75)">
                  {bankTransferType.iban}
                </TextCp>
                <Icon name="copy" color="rgba(0,0,0,.3)" size={15} />
              </TouchableOpacity>
            </View>

            <View style={styles.item}>
              <TextCp style={styles.title} textType="bold" color="#037375">
                {translate('reference')}
              </TextCp>
              <TouchableOpacity
                style={styles.copy}
                onPress={() => copy(user?.payment_reference_number)}>
                <TextCp
                  textType="bold"
                  color="#037375"
                  align="right"
                  style={{marginRight: 10}}>
                  {user?.payment_reference_number}
                </TextCp>
                <Icon name="copy" color="rgba(0,0,0,.3)" size={15} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
      <View
        style={{
          flexDirection: 'row',
          marginBottom: 10,
          paddingHorizontal: 16,
        }}>
        <DanaButton
          onPress={goHome}
          title={translate('back_home')}
          theme="#282828"
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  page: {flex: 1, backgroundColor: '#fff'},
  circle: {
    height: 80,
    width: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  icc: {
    height: 40,
    width: 40,
    borderRadius: 20,
    backgroundColor: '#444',
    justifyContent: 'center',
    alignItems: 'center',
  },
  item: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  bankBtn: {
    marginVertical: 8,
    paddingVertical: 16,
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    paddingHorizontal: 16,
  },
  details: {
    backgroundColor: '#fff',
    paddingHorizontal: pagePadding,
    borderRadius: 8,
    marginBottom: 10,
    shadowColor: '#aaa',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    marginHorizontal: 1,
  },
  rib: {
    fontSize: 19,
  },
  contentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 25,
  },
  listItem: {
    marginBottom: 15,
  },
  title: {
    color: 'rgba(0, 0, 0, 0.75)',
    marginBottom: 3,
    fontSize: 12,
  },
  name: {
    color: 'rgba(0, 0, 0, 0.5)',
    fontSize: 14,
    marginRight: 10,
    flexWrap: 'wrap',
  },
  header: {
    flex: 1,
    paddingTop: 30,
  },
  footer: {
    flex: 1,
  },
  headerContent: {
    justifyContent: 'space-evenly',
    flexGrow: 1,
    alignItems: 'center',
    paddingBottom: 20,
  },
  BC_sub_title: {
    fontSize: subtitleFontSize,
    textAlign: 'center',
  },
  BC_title: {
    fontSize: 22,
    textAlign: 'center',
    marginBottom: 8,
  },
  item: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 10,
  },
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    paddingHorizontal: 26,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16 * scaleRatio,
  },
  alert: {
    backgroundColor: '#F7E4CD',
    padding: 12,
    marginBottom: 16,
    borderRadius: 8,
  },
  alert_header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  alert_icon: {
    marginRight: 6,
  },
  copy: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default BankerTransfer;
