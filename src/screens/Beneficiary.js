import React, {useEffect, useState, useReducer} from 'react';
import {
  View,
  StyleSheet,
  ImageBackground,
  Platform,
  KeyboardAvoidingView,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import Heap from '@heap/react-native-heap';

import {
  pagePadding,
  borderRadius,
  subtitleFontSize,
  scaleRatio,
} from '../theme';
import TextCp from '../components/TextCp';
import DanaButton from '../components/DanaButton';
import ContactList from '../components/ContactList';
import {setI18nConfig, translate} from '../utilities/Translate';
import Icon from 'react-native-vector-icons/Feather';
import useDanaStore from '../app_state/store';
import {CustomerIO} from 'customerio-reactnative';
import {addContact, checkContactExists} from '../apis/favorites';
import Modal from 'react-native-modal';
import UserAvatar from 'react-native-user-avatar';
import {showMessage} from 'react-native-flash-message';
import SelectInput from '../components/SelectInput';
import Input from '../components/Input';
import Offline from '../components/Offline';
import {extractDigits, isValid} from '../utilities';

const {width} = Dimensions.get('window');

const initial_state = {
  firstName: '',
  firstNameError: false,
  lastName: '',
  firstNameError: false,
  country: null,
  countries: [],
  phone: '',
  phoneError: false,
  email: '',
};

const reducer = (state = initial_state, action) => {
  switch (action.type) {
    case 'SET_LAST_NAME':
      return {...state, lastName: action.payload};
    case 'SET_EMAIL':
      return {...state, email: action.payload};
    case 'SET_EMAIL_ERROR':
      return {...state, emailError: action.payload};
    case 'SET_LAST_NAME_ERROR':
      return {...state, lastNameError: action.payload};
    case 'SET_FIRST_NAME':
      return {...state, firstName: action.payload};
    case 'SET_FIRST_NAME_ERROR':
      return {...state, firstNameError: action.payload};
    case 'SET_COUNTRY':
      return {...state, country: action.payload};
    case 'SET_COUNTRY_ERROR':
      return {...state, countryError: action.payload};
    case 'SET_PHONE':
      return {...state, phone: action.payload};
    case 'SET_PHONE_ERROR':
      return {...state, phoneError: action.payload};
    case 'SET_COUNTRIES':
      return {...state, countries: action.payload};
    case 'SET_BULK':
      return {...state, ...action.payload};
    default:
      break;
  }
};

const Beneficiary = props => {
  const state_countries = useDanaStore(state => state.countries);
  const saveBeneficiary = useDanaStore(state => state.saveBeneficiary);
  const beneficiary = useDanaStore(state => state.beneficiary);
  const token = useDanaStore(state => state.token);
  const user = useDanaStore(state => state.user);
  const sendingCountry = useDanaStore(state => state.sendingCountry);
  const saveTransfer = useDanaStore(state => state.saveTransfer);
  const receivingCountry = useDanaStore(state => state.receivingCountry);
  const activeOperationPage = useDanaStore(state => state.activeOperationPage);
  const setActiveOperationPage = useDanaStore(
    state => state.setActiveOperationPage,
  );

  const transferTimer = useDanaStore(state => state.transferTimer);
  const updateTransferTimer = useDanaStore(state => state.updateTransferTimer);
  const transferTries = useDanaStore(state => state.transferTries);

  const [state, dispatch] = useReducer(reducer, {
    ...initial_state,
    countries: state_countries,
    country: receivingCountry,
  });
  const [processing, setProcessing] = useState(false);
  const [show, setShow] = useState(false);
  const [isNew, setIsNew] = useState(false);
  const [beneficiaryData, setBeneficiaryData] = useState(null);
  const contacts = useDanaStore(state => state.contacts);

  const goBack = () => {
    saveBeneficiary({
      firstName: '',
      lastName: '',
      country_name: '',
      phone: '',
      country: null,
    });
    Heap.track('Transfer canceled');
    updateTransferTimer('reset');
    props.navigation.goBack();
  };

  useEffect(() => {
    setI18nConfig();
    if (beneficiary) {
      dispatch({
        type: 'SET_BULK',
        payload: {
          firstName: beneficiary.firstName,
          lastName: beneficiary.lastName,
          phone: beneficiary.phone,
          country: beneficiary.country,
        },
      });
    }
  }, []);

  const addNewContact = () => {
    let errorCount = 0;

    if (
      state.email === '' &&
      state.country?.receiving_country?.preferred_notification_channel ===
        'mail'
    ) {
      dispatch({type: 'SET_EMAIL_ERROR', payload: true});
      errorCount++;
    }

    if (state.lastName === '') {
      dispatch({type: 'SET_LAST_NAME_ERROR', payload: true});
      errorCount++;
    }

    if (state.firstName === '') {
      dispatch({type: 'SET_FIRST_NAME_ERROR', payload: true});
      errorCount++;
    }

    if (errorCount > 0) {
      showMessage({
        type: 'danger',
        message: translate('all_error'),
      });
      return;
    }
    setProcessing(true);

    const beneficiary = {
      firstName: state.firstName,
      lastName: state.lastName,
      country: state.country,
      country_name: state.country?.receiving_country?.name,
      phone: state.phone,
      email: state.email,
    };

    if (
      !isValid(
        state?.country?.receiving_country?.country_code,
        state.phone,
        state?.country?.receiving_country?.code,
      )
    ) {
      showMessage({
        type: 'danger',
        message: translate('invalid_phone_number'),
      });
      return;
    }

    addContact(
      {
        first_name: state.firstName,
        last_name: state.lastName,
        email: state.email,
        phone_number: extractDigits(state.phone),
        country_code: beneficiary?.country?.receiving_country?.country_code,
      },
      token,
    )
      .then(res => {
        saveBeneficiary({
          firstName: state.firstName,
          lastName: state.lastName,
          email: res.beneficiary.email,
          phone_number: state.phone,
          country_code: beneficiary?.country?.receiving_country?.country_code,
          country: beneficiary?.country,
          is_individual: res.beneficiary.is_individual,
          is_active: res.beneficiary.is_active,
          is_verified: res.beneficiary.is_verified,
          company: res.beneficiary.is_individual
            ? null
            : res.beneficiary.company,
        });
        setBeneficiaryData(res.beneficiary);
        setShow(true);
        setProcessing(false);
      })
      .catch(error => {
        setProcessing(false);
      });
  };

  const setBeneInfo = () => {
    let errorCount = 0;

    if (state.phone === '') {
      dispatch({type: 'SET_PHONE_ERROR', payload: true});
      errorCount++;
    }

    if (state.country === null) {
      dispatch({type: 'SET_COUNTRY_ERROR', payload: true});
      errorCount++;
    }

    if (errorCount > 0) {
      showMessage({
        message: translate('all_error'),
        type: 'danger',
      });

      return;
    }

    if (
      !isValid(
        state?.country?.receiving_country?.country_code,
        state.phone,
        state?.country?.receiving_country?.code,
      )
    ) {
      showMessage({
        type: 'danger',
        message: translate('invalid_phone_number'),
      });
      return;
    }

    const beneficiary = {
      firstName: state.firstName,
      lastName: state.lastName,
      country: state.country,
      country_name: state.country?.receiving_country?.name,
      phone: state.phone,
      email: state.email,
    };

    setProcessing(true);

    checkContactExists(
      {
        phone_number: state.phone,
        country_code: beneficiary?.country?.receiving_country?.country_code,
      },
      token,
    )
      .then(response => {
        if (response.exists) {
          setProcessing(false);
          saveBeneficiary({
            firstName: response.customer.first_name,
            lastName: response.customer.last_name,
            email: response.customer.email,
            phone_number: state.phone,
            country_name: state.country?.receiving_country?.name,
            country: state.country,
            is_individual: response.customer.is_individual,
            is_active: response.customer.is_active,
            is_verified: response.customer.is_verified,
            company: response.customer.is_individual
              ? null
              : response.customer.company,
          });
          setBeneficiaryData(response.customer);
          // Heap.track('Beneficiary data set ', response.customer);
          saveTransfer({
            w_country: state.country,
            w_phone: state.phone,
          });

          setShow(true);
        } else {
          dispatch({type: 'SET_EMAIL', payload: ''});
          dispatch({type: 'SET_FIRST_NAME', payload: ''});
          dispatch({type: 'SET_LAST_NAME', payload: ''});
          setProcessing(false);
          showMessage({
            message: translate('no_user'),
            type: 'danger',
          });
          setIsNew(true);
        }
      })
      .catch(error => {
        setProcessing(false);
      });
  };

  const goToContacts = user => {
    props.navigation.navigate('Contact');
  };

  const startTransfer = selected_user => {
    const {favorite} = selected_user;
    setIsNew(false);
    const country_object = sendingCountry.receiving_countries.find(
      val =>
        val.receiving_country.name.toLowerCase() ===
        favorite.country.toLowerCase(),
    );
    if (!country_object) {
      showMessage({
        type: 'danger',
        message: `${favorite.country} is not a configured  receiving country for ${user?.country}`,
      });
      return;
    }
    const beneficiary = {
      firstName: favorite.first_name,
      lastName: favorite.last_name,
      country_name: favorite.country,
      phone: favorite.phone_number,
      country: country_object,
      is_individual: favorite.is_individual,
      is_active: favorite.is_active,
      is_verified: favorite.is_verified,
      company: favorite.is_individual ? null : favorite.company,
    };

    dispatch({type: 'SET_PHONE', payload: favorite.phone_number});
    dispatch({type: 'SET_FIRST_NAME', payload: favorite.first_name});
    dispatch({type: 'SET_LAST_NAME', payload: favorite.last_name});
    dispatch({type: 'SET_COUNTRY', payload: country_object});
    saveBeneficiary(beneficiary);
  };

  useEffect(() => {
    let timer;
    timer = setInterval(() => {
      updateTransferTimer('increment');
      if (transferTimer === 60) {
        const currentDate = new Date();
        const dateString = currentDate.toLocaleString();
        CustomerIO.track('Transfer Unfinished In 10 Mins', {
          userType: 'individual',
          operationTYpe: 'transfer',
          channel: 'mobile',
          email: user?.email,
          'transfer start date': dateString,
          'phone number': `${user?.country_code}${user?.phone_number}`,
          'first name': user?.firstname,
          'last name': user?.lastname,
          page: activeOperationPage,
          tries: transferTries,
        });
        CustomerIO.setProfileAttributes({
          'Transfer Done': false,
        });
        updateTransferTimer('reset');
      }
    }, 10000);

    return () => {
      clearInterval(timer);
    };
  }, [transferTimer]);

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: '#f1f1f1',
        marginHorizontal: 16,
      }}>
      <Offline />
      <StatusBar barStyle="dark-content" backgroundColor={'#f1f1f1'} />
      <ScrollView showsVerticalScrollIndicator={false}>
        <KeyboardAvoidingView
          {...(Platform.OS === 'ios'
            ? {behavior: 'position'}
            : {behavior: 'position'})}
          style={{
            flexGrow: 1,
          }}
          enabled>
          <ImageBackground
            style={styles.ImageBackground}
            imageStyle={styles.ibImage}
            source={require('../images/userinfo.jpg')}>
            <TouchableOpacity onPress={goBack} style={styles.ibClose}>
              <Icon name="x-circle" color="#333" size={20} />
            </TouchableOpacity>
            <View style={styles.ibTitleBox}>
              <TextCp textType="bold" style={styles.h1}>
                {translate('beneficiary_information')}
              </TextCp>
              {/* <TextCp style={styles.p}>
              {translate('beneficiary_sub_title')}
            </TextCp> */}
            </View>
          </ImageBackground>
          <View style={styles.contactlist}>
            <ContactList
              contacts={contacts || []}
              goToContacts={goToContacts}
              startTransfer={startTransfer}
              selected={beneficiaryData}
            />
          </View>
          <View style={styles.form}>
            {isNew ? (
              <TextCp
                textType={'bold'}
                style={{color: '#333', marginBottom: 10}}>
                {translate('create_user_text')}
              </TextCp>
            ) : (
              <TextCp
                textType={'bold'}
                style={{color: '#333', marginBottom: 10}}>
                {translate('beneficiary_sub_title')}
              </TextCp>
            )}

            <SelectInput
              placeholder={{
                label: translate('country'),
                value: translate('country'),
              }}
              onValueChange={value => {
                if (value === 'Country') {
                  return;
                }
                const country = sendingCountry?.receiving_countries.find(
                  val => val.receiving_country.id === value,
                );

                dispatch({
                  type: 'SET_COUNTRY',
                  payload: country,
                });
                dispatch({type: 'SET_COUNTRY_ERROR', payload: false});
                saveBeneficiary({country: country?.receiving_country});
              }}
              items={
                sendingCountry?.receiving_countries.map(val => {
                  return {
                    label: val.receiving_country?.name,
                    value: val.receiving_country?.id,
                  };
                }) || []
              }
              value={state.country?.receiving_country?.id}
            />

            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <View
                style={{
                  ...styles.input,
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: 80,
                  marginRight: 10,
                  borderWidth: state.phoneError ? 1 : 0,
                  borderColor: state.phoneError ? '#ED6E78' : 'transparent',
                }}>
                <TextCp>
                  +{state?.country?.receiving_country?.country_code}
                </TextCp>
              </View>
              <View style={{flex: 1}}>
                <Input
                  style={[
                    styles.input,
                    {
                      borderWidth: 1,
                      borderColor: state.phoneError ? '#ED6E78' : 'transparent',
                    },
                  ]}
                  onChange={text => {
                    dispatch({type: 'SET_PHONE', payload: text});
                    dispatch({type: 'SET_PHONE_ERROR', payload: false});
                  }}
                  label={translate('PhoneNumber')}
                  value={state.phone}
                  keyboardType="number-pad"
                />
              </View>
            </View>
            {isNew && (
              <>
                <Input
                  onChange={text => {
                    dispatch({type: 'SET_FIRST_NAME', payload: text});
                    dispatch({type: 'SET_FIRST_NAME_ERROR', payload: false});
                  }}
                  placeholder={translate('name_on_id')}
                  value={state.firstName}
                  label={translate('name_on_id')}
                  style={[
                    {
                      borderWidth: 1,
                      borderColor: state.lastNameError
                        ? '#ED6E78'
                        : 'transparent',
                    },
                  ]}
                />
                <Input
                  onChange={text => {
                    dispatch({type: 'SET_LAST_NAME', payload: text});
                    dispatch({type: 'SET_LAST_NAME_ERROR', payload: false});
                  }}
                  value={state.lastName}
                  label={translate('first_name_on_id')}
                  style={[
                    {
                      borderWidth: 1,
                      borderColor: state.firstNameError
                        ? '#ED6E78'
                        : 'transparent',
                    },
                  ]}
                />

                <Input
                  onChange={text => {
                    dispatch({type: 'SET_EMAIL', payload: text});
                    dispatch({type: 'SET_EMAIL_ERROR', payload: false});
                  }}
                  value={state.email}
                  label={
                    state?.country?.preferred_notification_channel === 'mail'
                      ? translate('email')
                      : translate('email_optional')
                  }
                  style={[
                    {
                      borderWidth: 1,
                      borderColor: state.emailError ? '#ED6E78' : 'transparent',
                    },
                  ]}
                />
              </>
            )}
          </View>
        </KeyboardAvoidingView>
      </ScrollView>
      <View style={styles.bottom}>
        <View style={{flex: 2, paddingRight: 10, paddingLeft: 2}}>
          <DanaButton
            title={translate('return')}
            onPress={() => props.navigation.goBack()}
            theme="#fff"
            textColor="#282828"
          />
        </View>
        {/* <View style={{flex: 1}}></View> */}
        <View style={{flex: 2, paddingLeft: 10}}>
          {processing ? (
            <ActivityIndicator size={'small'} color="black" />
          ) : (
            <>
              {isNew ? (
                <DanaButton
                  title={translate('add_contact')}
                  onPress={() => addNewContact()}
                  theme="#282828"
                  testID="add_contact"
                />
              ) : (
                <DanaButton
                  title={translate('search_user')}
                  onPress={() => setBeneInfo()}
                  theme="#282828"
                  testID="search_user"
                />
              )}
            </>
          )}
        </View>
      </View>
      <Modal
        isVisible={show}
        animationType="fade"
        statusBarTranslucent={true}
        style={{backgroundColor: 'rgba(154,155,159, .3)', margin: 0}}>
        <View style={styles.modal}>
          <View style={styles.innerContainer}>
            <View
              style={{
                marginBottom: 16,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <TextCp textType="bold" style={{fontSize: 22}}>
                {translate('beneficiary')}
              </TextCp>
              <TextCp align="center" opacity={0.6}>
                {translate('beneficiary_text')}:{' '}
                {beneficiaryData?.full_phone_number}
              </TextCp>
            </View>
            <View style={{marginBottom: 40}}>
              <View
                style={{
                  elevation: 1,
                  backgroundColor: '#fff',
                  borderRadius: 10,
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    backgroundColor: '#fff',
                    paddingVertical: 16,
                    borderRadius: 10,
                    paddingHorizontal: 15,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      flex: 1,
                    }}>
                    <UserAvatar
                      size={40}
                      name={beneficiaryData?.full_name}
                      bgColors={[
                        '#4a4e69',
                        '#264653',
                        '#219ebc',
                        '#588157',
                        '#b5838d',
                      ]}
                    />
                    <View style={{marginLeft: 8}}>
                      <TextCp textType="bold" style={{fontSize: 17}}>
                        {beneficiaryData?.full_name}
                      </TextCp>
                      <TextCp style={{fontSize: 11}}>
                        {beneficiaryData?.is_individual
                          ? beneficiaryData?.country
                          : beneficiaryData?.company?.name}
                      </TextCp>
                    </View>
                  </View>
                  <TextCp style={{fontSize: 12}}>
                    {beneficiaryData?.is_individual
                      ? translate('individual')
                      : translate('company')}
                  </TextCp>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    marginVertical: 10,
                    paddingVertical: 10,
                    borderRadius: 10,
                    paddingHorizontal: 15,
                  }}>
                  <View style={{flex: 1}}>
                    {!beneficiaryData?.is_individual ? (
                      <>
                        <TextCp style={{fontSize: 12}} opacity={0.51}>
                          {translate('registration_id')}
                        </TextCp>
                        <TextCp textType="bold">
                          {beneficiaryData?.company?.registered_id || 'N/A'}
                        </TextCp>
                        <View style={{height: 10}} />

                        <TextCp style={{fontSize: 12}} opacity={0.51}>
                          {translate('quarter')}
                        </TextCp>
                        <TextCp textType="bold">
                          {beneficiaryData?.company?.quarter || 'Address'}
                        </TextCp>
                      </>
                    ) : (
                      <>
                        <TextCp style={{fontSize: 12}} opacity={0.51}>
                          {translate('phone_number')}
                        </TextCp>
                        <TextCp textType="bold">
                          {beneficiaryData?.full_phone_number || 'N/A'}
                        </TextCp>
                      </>
                    )}
                  </View>
                  <View style={{flex: 1}}>
                    {!beneficiaryData?.is_individual ? (
                      <>
                        {beneficiaryData?.email && (
                          <>
                            <TextCp style={{fontSize: 12}} opacity={0.51}>
                              {translate('contact_email')}
                            </TextCp>
                            <TextCp textType="bold">
                              {beneficiaryData?.email}
                            </TextCp>
                            <View style={{height: 10}} />
                          </>
                        )}

                        <TextCp style={{fontSize: 12}} opacity={0.51}>
                          City
                        </TextCp>
                        <TextCp textType="bold">
                          {beneficiaryData?.company?.address || 'Address'}
                        </TextCp>
                      </>
                    ) : (
                      <>
                        {beneficiaryData?.email && (
                          <>
                            <TextCp style={{fontSize: 12}} opacity={0.51}>
                              {translate('contact_email')}
                            </TextCp>
                            <TextCp textType="bold">
                              {beneficiaryData?.email}
                            </TextCp>
                            <View style={{height: 10}} />
                          </>
                        )}
                      </>
                    )}
                  </View>
                </View>
              </View>

              <View style={{marginVertical: 14}}>
                <TextCp
                  color="#ED6E78"
                  align="center"
                  textType={'bold'}
                  style={{fontSize: 12}}>
                  {translate('is_bene')}
                </TextCp>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginVertical: 14,
                }}>
                <View style={{flex: 1, paddingRight: 10}}>
                  <DanaButton
                    title={translate('return')}
                    onPress={() => setShow(false)}
                    theme="#fff"
                    textColor="#282828"
                    testID="backToHomePage"
                  />
                </View>
                <View style={{flex: 1}}>
                  <DanaButton
                    title={translate('next')}
                    onPress={() => {
                      Heap.addEventProperties({
                        'transfer type': beneficiaryData?.is_individual
                          ? 'C2C'
                          : 'C2B',
                      });
                      Heap.track('Transfer Recipient Details Confirmed');
                      setActiveOperationPage('Amount Confirmation Page');
                      props.navigation.navigate('CashInMethods');
                      setShow(false);
                    }}
                    theme="#282828"
                    testID="moveToPaymentScreen"
                  />
                </View>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  page: {
    paddingHorizontal: pagePadding,
    backgroundColor: '#fff',
    flex: 1,
  },
  image: {
    height: 189 * scaleRatio,
    width: '100%',
    borderRadius: borderRadius,
    marginBottom: 0,
  },
  h1: {
    fontSize: 24 * scaleRatio,
    color: '#222',
  },
  p: {
    fontSize: subtitleFontSize,
    marginBottom: 11,
  },
  input: {
    width: '100%',
    height: 48,
    backgroundColor: 'rgba(0, 0, 0, 0.07)',
    borderRadius: 8,
    paddingHorizontal: 17,
    fontFamily: 'Inter-Regular',
  },
  bottom: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Platform.OS === 'ios' ? 30 : 20,
    paddingHorizontal: pagePadding,
  },
  contactlist: {
    marginVertical: 17,
  },
  tint: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    padding: 16,
    backgroundColor: 'rgba(0,0,0,.7)',
    borderBottomLeftRadius: 14,
    borderBottomRightRadius: 14,
    flexDirection: 'row',
  },
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    paddingHorizontal: 26,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 20 * scaleRatio,
    backgroundColor: '#f1f1f1',
  },
  innerContainerTra: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    backgroundColor: '#f1f1f1',
    paddingHorizontal: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16,
  },
  modalTitle: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginVertical: 10,
    alignItems: 'center',
  },
  ImageBackground: {
    height: 200 * scaleRatio,
    width: width - 32,
    padding: 10,
    position: 'relative',
    marginVertical: 16,
    borderRadius: 12,
    elevation: 1,
  },
  ibImage: {
    borderRadius: 12,
  },
  ibClose: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 40,
    height: 40,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    borderTopRightRadius: 12,
    // zIndex: 200,
  },
  ibTitleBox: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    backgroundColor: '#ffffff88',
    padding: 12,
    right: 0,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  form: {
    marginTop: 16,
  },
});

export default Beneficiary;
