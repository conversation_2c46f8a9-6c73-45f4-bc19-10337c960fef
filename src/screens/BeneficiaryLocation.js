import React, {useEffect, useContext, useReducer} from 'react';
import {View, StyleSheet, Switch, Platform, StatusBar} from 'react-native';
import {translate, setI18nConfig} from '../utilities/Translate';
import {
  pagePadding,
  titleFontSize,
  subtitleFontSize,
  aspectRatio,
} from '../theme';
import TextCp from '../components/TextCp';
import DanaButton from '../components/DanaButton';
import Toolbar from '../components/Toolbar';
import {GlobalContext} from '../Context/Index';
import RNPickerSelect from 'react-native-picker-select';
import Icon from 'react-native-vector-icons/Feather';
import useDanaStore from '../app_state/store';
import {showMessage} from 'react-native-flash-message';

const initial_state = {
  district: '',
  districtError: false,
  cityError: false,
  city: '',
  cities: [],
  districts: [],
  isPaid: false,
};

const reducer = (state = initial_state, action) => {
  switch (action.type) {
    case 'SET_PAID_STATE':
      return {...state, isPaid: action.payload};
    case 'SET_DISTRICT':
      return {...state, district: action.payload};
    case 'SET_CITY':
      return {...state, city: action.payload};
    case 'SET_DISTRICTS':
      return {...state, districts: action.payload};
    case 'SET_CITIES':
      return {...state, cities: action.payload};
    case 'SET_CITY_ERROR':
      return {...state, cityError: action.payload};
    case 'SET_DISTRICT_ERROR':
      return {...state, districtError: action.payload};
    case 'SET_BULK':
      return {...state, ...action.payload};
    default:
      break;
  }
};

const BeneficiaryLocation = props => {
  const beneficiary = useDanaStore(state => state.beneficiary);
  const saveLocation = useDanaStore(state => state.saveLocation);
  const [state, dispatch] = useReducer(reducer, initial_state);
  const context = useContext(GlobalContext);

  const goBack = () => {
    props.navigation.goBack();
  };

  const getCities = () => {
    const country_data = beneficiary.country;
    if (country_data?.city_quarters) {
      const got_cities = Object.keys(country_data?.city_quarters).map(val => {
        return {label: val, value: val};
      });
      dispatch({type: 'SET_CITIES', payload: got_cities});
    }
  };

  const setFeesForDelivery = () => {
    const flag = !state.isPaid;
    dispatch({type: 'SET_PAID_STATE', payload: flag});
  };

  const addBeneLocation = () => {
    let errorCount = 0;
    if (state.district === '') {
      dispatch({type: 'SET_DISTRICT_ERROR', payload: true});
      errorCount++;
    }
    if (state.city === '') {
      dispatch({type: 'SET_CITY_ERROR', payload: true});
      errorCount++;
    }

    if (errorCount === 0) {
      const location = {
        city: state.city,
        district: state.district,
        isPaid: state.isPaid,
      };
      saveLocation(location);
      context.addBeneficiaryLocation(location);
      props.navigation.navigate('PaymentInfo');
    } else {
      showMessage({
        message: translate('all_error'),
        type: 'danger',
      });
    }
  };

  const onCitySelected = (cityName, index) => {
    dispatch({type: 'SET_CITY_ERROR', payload: false});
    dispatch({type: 'SET_CITY', payload: cityName});
    const country_data = beneficiary?.country;
    const quarters = country_data?.city_quarters[cityName].map(val => {
      return {label: val, value: val};
    });
    dispatch({type: 'SET_DISTRICTS', payload: quarters});
  };

  useEffect(() => {
    getCities();
    setI18nConfig();
    if (context.beneficiaryLocation) {
      dispatch({
        type: 'SET_BULK',
        payload: {
          city: context.beneficiaryLocation.city,
          district: context.beneficiaryLocation.district,
          isPaid: context.beneficiaryLocation.isPaid,
        },
      });
    }
  }, []);

  return (
    <View style={styles.page}>
      <StatusBar barStyle="dark-content" backgroundColor={'#fff'} />
      <View style={{flexGrow: 1}}>
        <Toolbar goBack={goBack} />
        <TextCp textType="bold" style={styles.h1}>
          {translate('beneficiary_location')}
        </TextCp>

        <TextCp style={styles.p}>
          {translate('delivery_to_beneficiary_info')}
        </TextCp>

        <View
          style={[
            styles.switch,
            {backgroundColor: state.isPaid ? '#282828' : '#D1D1D1'},
          ]}>
          {state.isPaid ? (
            <TextCp textType="bold" style={styles.fundTextActive}>
              {translate('delivery_fund')}
            </TextCp>
          ) : (
            <TextCp textType="bold" style={styles.fundText}>
              {translate('no_deliveries')}
            </TextCp>
          )}
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Switch
              trackColor={{false: '#767577', true: '#d1d1d1'}}
              thumbColor={state.isPaid ? '#fff' : 'rgba(0, 0, 0, 0.5)'}
              ios_backgroundColor="#3e3e3e"
              onValueChange={setFeesForDelivery}
              value={state.isPaid}
              style={{transform: [{scaleX: 2}, {scaleY: 2}]}}
            />
          </View>
        </View>

        {beneficiary?.country?.city_quarters === null && (
          <TextCp style={{textAlign: 'center', color: 'red', marginBottom: 13}}>
            The {beneficiary?.country.name} has no cities or quarter added yet.
          </TextCp>
        )}

        <RNPickerSelect
          placeholder={{
            label: translate('city'),
            value: null,
          }}
          Icon={() => {
            return (
              <Icon name="chevron-down" style={{fontSize: 20}} color="#aaa" />
            );
          }}
          onValueChange={(value, index) => {
            dispatch({type: 'SET_CITY', payload: value});
            dispatch({type: 'SET_CITY_ERROR', payload: false});
            onCitySelected(value, index);
          }}
          items={state.cities}
          style={{
            inputIOS: {
              fontSize: 16,
              paddingVertical: 12,
              paddingHorizontal: 17,
              color: '#000',
              paddingRight: 30,
              height: 45,
              backgroundColor: 'rgba(0, 0, 0, 0.07)',
              borderWidth: 1,
              borderColor: state.cityError ? '#BC4749' : 'transparent',
              borderRadius: 5,
              marginBottom: 17,
            },
            inputAndroid: {
              fontSize: 16,
              paddingHorizontal: 17,
              paddingVertical: 8,
              color: '#000',
              paddingRight: 30,
              height: 45,
              backgroundColor: 'rgba(0, 0, 0, 0.07)',
              borderWidth: 1,
              borderColor: state.cityError ? '#BC4749' : 'transparent',
              borderRadius: 5,
              marginBottom: 17,
            },
            iconContainer: {
              top: 10,
              right: 15,
            },
            placeholder: {
              color: 'rgba(0,0,0,.31)',
              fontSize: 15,
            },
          }}
          useNativeAndroidPickerStyle={false}
          textInputProps={{underlineColor: 'yellow'}}
          value={state.city}
        />

        <RNPickerSelect
          placeholder={{
            label: translate('district'),
            value: null,
          }}
          Icon={() => {
            return (
              <Icon name="chevron-down" style={{fontSize: 20}} color="#aaa" />
            );
          }}
          onValueChange={value => {
            dispatch({type: 'SET_DISTRICT', payload: value});
            dispatch({type: 'SET_DISTRICT_ERROR', payload: false});
          }}
          items={state.districts}
          style={{
            inputIOS: {
              fontSize: 16,
              paddingVertical: 12,
              paddingHorizontal: 17,
              color: '#000',
              paddingRight: 30,
              height: 45,
              backgroundColor: 'rgba(0, 0, 0, 0.07)',
              borderWidth: 1,
              borderColor: state.districtError ? '#BC4749' : 'transparent',
              borderRadius: 5,
              marginBottom: 17,
            },
            inputAndroid: {
              fontSize: 16,
              paddingHorizontal: 17,
              paddingVertical: 8,
              color: '#000',
              paddingRight: 30,
              height: 45,
              backgroundColor: 'rgba(0, 0, 0, 0.07)',
              borderWidth: 1,
              borderColor: state.districtError ? '#BC4749' : 'transparent',
              borderRadius: 5,
              marginBottom: 17,
            },
            iconContainer: {
              top: 10,
              right: 15,
            },
            placeholder: {
              color: 'rgba(0,0,0,.31)',
              fontSize: 15,
            },
          }}
          useNativeAndroidPickerStyle={false}
          textInputProps={{underlineColor: 'yellow'}}
          value={state.district}
        />
      </View>

      <View style={styles.bottom}>
        <View style={{flex: 2, paddingRight: 10, paddingLeft: 1}}>
          <DanaButton
            title={translate('return')}
            onPress={() => props.navigation.goBack()}
            theme="light"
          />
        </View>
        <View style={{flex: 1, paddingRight: 10}} />
        <View style={{flex: 2, paddingLeft: 10}}>
          <DanaButton
            title={translate('next')}
            onPress={() => addBeneLocation()}
            theme="#282828"
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  page: {
    paddingHorizontal: pagePadding,
    backgroundColor: '#fff',
    flex: 1,
  },
  h1: {
    fontSize: titleFontSize,
    marginBottom: 13,
  },
  p: {
    fontSize: subtitleFontSize,
    marginBottom: 42,
  },

  bottom: {
    flexDirection: 'row',
    height: '10%',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
  },

  switch: {
    height: '40%',
    borderRadius: 13,
    padding: 20,
    marginBottom: 30,
  },
  fundText: {
    fontSize: 36,
    marginBottom: 20 * aspectRatio,
    color: 'rgba(0, 0, 0, 0.5)',
  },
  fundTextActive: {
    fontSize: 36,
    marginBottom: 20 * aspectRatio,
    color: '#fff',
  },
});

const pickerSelectStyles = StyleSheet.create({
  inputIOS: {
    fontSize: 16,
    paddingVertical: 12,
    paddingHorizontal: 17,
    color: '#000',
    paddingRight: 30, // to ensure the text is never behind the icon
    height: 45,
    backgroundColor: 'rgba(0, 0, 0, 0.07)',
    borderRadius: 5,
    marginBottom: 10,
  },
  inputAndroid: {
    fontSize: 16,
    paddingHorizontal: 17,
    paddingVertical: 8,
    color: '#000',
    paddingRight: 30, // to ensure the text is never behind the icon
    height: 45,
    backgroundColor: 'rgba(0, 0, 0, 0.07)',
    borderRadius: 5,
    marginBottom: 10,
  },
});

export default BeneficiaryLocation;
