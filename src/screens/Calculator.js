import React, {useEffect, useReducer} from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TextInput,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  StatusBar,
  ActivityIndicator,
  SafeAreaView,
  ImageBackground,
  Dimensions,
  TouchableOpacity,
  Keyboard,
} from 'react-native';
import {toolBarHeight, scaleRatio} from '../theme';
import TextCp from '../components/TextCp';
import DanaButton from '../components/DanaButton';
import {setI18nConfig, translate} from '../utilities/Translate';
import Icon from 'react-native-vector-icons/Feather';
import {calculateFees} from '../apis/Transfers';
import {navigateWithNoHistory} from '../utilities/ForgetHistroty';
import {CommonActions} from '@react-navigation/native';
import crashlytics from '@react-native-firebase/crashlytics';
import useDanaStore from '../app_state/store';
import CashInput from '../components/CashInput';
import SelectInput from '../components/SelectInput';
import CashLayout from '../components/CashLayout';

const initial_state = {
  fetching: false,
  countries: [],
  selected_country: [],
  cash_in_method: '',
  cash_in_method_name: '',
  sending_country: '',
  receiving_country: '',
  receiving_countries: [],
  cash_in_methods: [],
  amount_in_euro: '',
  amount_in_cfa: '',
  fees_response: {
    fee: 0,
    exchange_rate: 665.967,
    net_exchange_rate: 665.967,
    spread: 0,
    received_amount: 0,
  },
};

const reducer = (state = initial_state, action) => {
  switch (action.type) {
    case 'ADD_COUNTRIES':
      return {...state, countries: action?.payload};
    case 'UPDATE_FETCHING':
      return {...state, fetching: action?.payload};
    case 'SET_SENDING_COUNTRY':
      return {
        ...state,
        sending_country: action?.payload?.id,
        cash_in_methods: action?.payload?.cash_in_methods,
        receiving_countries: action?.payload?.receiving_countries,
        selected_country: action?.payload,
      };
    case 'SET_CASH_IN_METHOD':
      return {
        ...state,
        cash_in_method: action?.payload?.cash_in_method?.id,
        cash_in_method_name: action?.payload?.cash_in_method?.name,
      };
    case 'SET_RECEIVING_COUNTRY':
      return {
        ...state,
        receiving_country: action.payload,
      };
    case 'SET_EURO_AMOUNT':
      return {
        ...state,
        amount_in_euro: action.payload,
      };
    case 'SET_CFA_AMOUNT':
      return {
        ...state,
        amount_in_cfa: action.payload,
      };
    case 'SET_FEES_RESPONSE':
      return {
        ...state,
        fees_response: action.payload,
      };
    default:
      break;
  }
};

const {width} = Dimensions.get('window');

const Calculator = props => {
  const state_countries = useDanaStore(state => state.countries);
  const fetchCountries = useDanaStore(state => state.fetchCountries);
  const fees = useDanaStore(state => state.fees);
  const saveFees = useDanaStore(state => state.saveFees);
  const saveBeneficiary = useDanaStore(state => state.saveBeneficiary);
  const saveReceivingCountry = useDanaStore(
    state => state.saveReceivingCountry,
  );
  const user = useDanaStore(state => state.user);
  const token = useDanaStore(state => state.token);

  const [state, dispatch] = useReducer(reducer, {
    ...initial_state,
    countries: state_countries,
  });

  const saveSendingCountry = useDanaStore(state => state.saveSendingCountry);
  const saveTransfer = useDanaStore(state => state.saveTransfer);

  const goBack = async () => {
    if (user !== null && token !== null) {
      props.navigation.goBack();
    } else {
      navigateWithNoHistory(props, CommonActions, 'Login');
    }
  };

  const setSendingCountry = () => {
    const selected_country = state_countries.find(
      val => val.name === user?.country,
    );
    saveSendingCountry(selected_country);
    dispatch({
      type: 'SET_SENDING_COUNTRY',
      payload: selected_country,
    });
  };

  const continueNextPage = async () => {
    if (user !== null && token !== null) {
      navigateWithNoHistory(props, CommonActions, 'Beneficiary');
    } else {
      navigateWithNoHistory(props, CommonActions, 'Login');
    }
  };

  const handleExchange = (cash, toCurrency) => {
    if (cash.length === 0) return '';
    if (toCurrency.toLowerCase() === 'eur') {
      return (parseFloat(cash) / fees.exchange_rate || 1).toFixed(2).toString();
    } else {
      return fees?.received_amount?.toString();
    }
  };

  const getAppliedFees = async () => {
    dispatch({type: 'UPDATE_FETCHING', payload: true});
    const body = {
      euro_amount: state.amount_in_euro,
      sending_country_id: state.sending_country,
      receiving_country_id: state.receiving_country,
      cashin_method_id: state.cash_in_method,
    };

    try {
      const result = await calculateFees(body, user?.id);
      const rateObject = result?.fee_calculation.find(val => val.exchange_rate);
      saveFees({...rateObject, fee: result.fee.toFixed(2)});
      dispatch({type: 'UPDATE_FETCHING', payload: false});
      dispatch({
        type: 'SET_FEES_RESPONSE',
        payload: {...rateObject, fees_response: result.fee.toFixed(2)},
      });
    } catch (error) {
      dispatch({type: 'UPDATE_FETCHING', payload: false});
    }
  };

  useEffect(() => {
    setI18nConfig();
    fetchCountries();
  }, []);

  useEffect(() => {
    getAppliedFees();
  }, [state.amount_in_euro]);

  const next = () => {
    crashlytics().log('Transfer : Moving from transfer to  Beneficiary');
    try {
      saveTransfer({
        cash_in_method: state.cash_in_method,
        amount_in_euro: state.amount_in_euro,
        amount_in_cfa: state.amount_in_cfa,
        cash_in_method_name: state?.cash_in_method_name,
      });

      continueNextPage();
    } catch (error) {
      crashlytics().log('Transfer : Error trying to navigate');
      crashlytics().recordError(error);
    }
  };

  useEffect(() => {
    if (user && token) {
      setSendingCountry();
    }
  }, []);

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: '#fff'}}>
      <StatusBar backgroundColor={'#fff'} barStyle="dark-content" />
      <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
        <KeyboardAvoidingView
          {...(Platform.OS === 'ios'
            ? {behavior: 'position'}
            : {behavior: 'position'})}
          style={{
            flex: 1,
            paddingHorizontal: 16,
            backgroundColor: '#fff',
          }}
          enabled>
          <View style={{height: '100%'}}>
            <View style={{flexGrow: 1}}>
              <ImageBackground
                style={styles.ImageBackground}
                imageStyle={styles.ibImage}
                source={require('../images/smiling_guy.jpeg')}>
                <TouchableOpacity onPress={goBack} style={styles.ibClose}>
                  <Icon name="x-circle" color="#333" size={20} />
                </TouchableOpacity>
                <View style={styles.ibTitleBox}>
                  <TextCp textType="bold" style={styles.h1}>
                    {translate('calcTitle')}
                  </TextCp>
                  <TextCp style={styles.p}>{translate('calcSubTitle')}</TextCp>
                </View>
              </ImageBackground>

              {!user && !token && (
                <SelectInput
                  onValueChange={value => {
                    const selected_country = state_countries.find(
                      val => val.id === value,
                    );
                    saveSendingCountry(selected_country);
                    dispatch({
                      type: 'SET_SENDING_COUNTRY',
                      payload: selected_country,
                    });
                  }}
                  items={state_countries.map(val => {
                    return {
                      label: val.name,
                      value: val.id,
                    };
                  })}
                  placeholder={{
                    label: translate('sending_country'),
                    value: translate('sending_country'),
                  }}
                  value={state.sending_country}
                />
              )}

              <SelectInput
                onValueChange={value => {
                  const selected_country = state_countries.find(
                    val => val.id === value,
                  );
                  saveBeneficiary({
                    country: selected_country,
                  });

                  saveReceivingCountry(selected_country);
                  dispatch({
                    type: 'SET_RECEIVING_COUNTRY',
                    payload: value,
                  });
                }}
                items={state.receiving_countries.map(val => {
                  return {
                    label: val?.receiving_country?.name,
                    value: val?.receiving_country?.id,
                  };
                })}
                placeholder={{
                  label: translate('receiving_country'),
                  value: translate('receiving_country'),
                }}
                value={state.receiving_country}
              />

              <SelectInput
                onValueChange={value => {
                  const cash_in_method = state.cash_in_methods.find(
                    val => val.cash_in_method.id === value,
                  );
                  dispatch({
                    type: 'SET_CASH_IN_METHOD',
                    payload: cash_in_method,
                  });
                }}
                items={state.cash_in_methods.map(val => {
                  return {
                    label: translate(
                      val.cash_in_method.name
                        .toLowerCase()
                        .split(' ')
                        .join('_'),
                    ),
                    value: val.cash_in_method.id,
                  };
                })}
                placeholder={{
                  label: translate('calcPayMethod'),
                  value: translate('calcPayMethod'),
                }}
                value={state?.cash_in_method?.cash_in_method?.id}
              />

              <CashInput
                value={state.amount_in_euro}
                onChange={text => {
                  dispatch({type: 'SET_EURO_AMOUNT', payload: text});
                  dispatch({
                    type: 'SET_CFA_AMOUNT',
                    payload: handleExchange(text, 'xof'),
                  });
                }}
                placeholder={translate('calcMoneyInEUR')}
                currency="EUR"
              />

              <View style={styles.calc}>
                <View style={styles.cash}>
                  <Text style={styles.bullet}>{'\u2B24'}</Text>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      flex: 1,
                    }}>
                    <TextCp style={styles.name}>
                      {translate('calcExchangeRate')}
                    </TextCp>
                    <TextCp textType="bold" style={styles.conversion}>
                      {`1€ =  ${
                        fees?.exchange_rate?.toFixed(2) ||
                        state.fees_response.exchange_rate
                      } XOF`}
                    </TextCp>
                  </View>
                </View>
                <View style={styles.cash}>
                  <Text style={styles.bullet}>{'\u2B24'}</Text>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      flex: 1,
                    }}>
                    <TextCp style={styles.name}>{translate('fees')}</TextCp>
                    {state.fetching ? (
                      <ActivityIndicator size="small" color="blue" />
                    ) : (
                      <>
                        {state.amount_in_euro === '' ? (
                          <CashLayout value={0} fontSize={16 * scaleRatio} />
                        ) : (
                          <CashLayout
                            value={fees.fee}
                            fontSize={16 * scaleRatio}
                          />
                        )}
                      </>
                    )}
                  </View>
                </View>
              </View>

              <CashInput
                value={state.amount_in_cfa}
                onChange={text => {
                  dispatch({
                    type: 'SET_EURO_AMOUNT',
                    payload: handleExchange(text, 'eur'),
                  });
                  dispatch({type: 'SET_CFA_AMOUNT', payload: text});
                }}
                placeholder={translate('calcMoneyInCFA')}
                style={{}}
                currency="XOF"
              />

              <View
                style={{
                  flexDirection: 'row',
                  paddingVertical: 6,
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                <TextCp
                  style={{color: 'rgba(0,0,0,.5)', fontSize: 16 * scaleRatio}}>
                  {translate('totalToPay')}
                </TextCp>

                <CashLayout
                  value={state.amount_in_euro || 0}
                  fontSize={22 * scaleRatio}
                  color="#037375"
                />
              </View>
            </View>

            <View style={{marginBottom: Platform.OS === 'ios' ? 40 : 20}}>
              <DanaButton
                title={translate('continue')}
                onPress={() => next()}
                theme="#282828"
              />
            </View>
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  page: {
    flex: 1,
  },
  ImageBackground: {
    height: 200 * scaleRatio,
    width: width - 32,
    padding: 10,
    position: 'relative',
    marginVertical: 16,
    borderRadius: 12,
  },
  ibImage: {
    borderRadius: 12,
  },
  ibClose: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 40,
    height: 40,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    // zIndex: 200,
  },
  ibTitleBox: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    backgroundColor: '#ffffff88',
    padding: 12,
    right: 0,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  toolbar: {
    height: toolBarHeight,
    marginBottom: 11 * scaleRatio,
  },
  h1: {
    fontSize: 20,
    color: '#222',
  },
  p: {
    fontSize: 14,
  },
  input: {
    width: '100%',
    height: 48,
    backgroundColor: 'rgba(0, 0, 0, 0.07)',
    borderRadius: 8,
    marginVertical: 5.5 * scaleRatio,
    paddingHorizontal: 17 * scaleRatio,
  },
  cash: {marginBottom: 16 * scaleRatio, flexDirection: 'row'},
  bullet: {
    color: '#dadcdc',
    marginRight: 15,
  },
  name: {
    height: 19,
    color: 'rgba(0, 0, 0, 0.5)',
    fontSize: 16 * scaleRatio,
    marginRight: 10,
  },
  conversion: {
    color: 'rgba(0, 0, 0, 0.5)',
    fontSize: 16 * scaleRatio,
  },
  calc: {
    marginLeft: 15,
    marginTop: 11 * scaleRatio,
  },
  error: {
    fontSize: 12,
    color: 'red',
  },
});

const pickerSelectStyles = StyleSheet.create({
  inputIOS: {
    fontSize: 16,
    paddingVertical: 12 * scaleRatio,
    paddingHorizontal: 17,
    color: '#000',
    paddingRight: 30, // to ensure the text is never behind the icon
    height: 48,
    backgroundColor: 'rgba(0, 0, 0, 0.07)',
    borderRadius: 8,
    marginBottom: 11 * scaleRatio,
  },
  inputAndroid: {
    fontSize: 16,
    paddingHorizontal: 17,
    paddingVertical: 8 * scaleRatio,
    color: '#000',
    paddingRight: 30, // to ensure the text is never behind the icon
    height: 48,
    backgroundColor: 'rgba(0, 0, 0, 0.07)',
    borderRadius: 8,
    marginBottom: 11 * scaleRatio,
  },
});

export default Calculator;
