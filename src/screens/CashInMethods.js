import React, {useState, useEffect, useReducer} from 'react';
import {View, StyleSheet, StatusBar, SafeAreaView} from 'react-native';
import {CustomerIO} from 'customerio-reactnative';
import {translate, setI18nConfig} from '../utilities/Translate';
import {titleFontSize, scaleRatio} from '../theme';
import TextCp from '../components/TextCp';
import DanaButton from '../components/DanaButton';
import {ScrollView} from 'react-native-gesture-handler';
import useDanaStore from '../app_state/store';
import {showMessage} from 'react-native-flash-message';
import CashInMethodItem from '../components/CashInMethodItem';

const initial_state = {
  payMethod: '',
  euros: '0',
  euros_error: false,
  cfa: '0',
  cfa_error: false,
  payMethodError: false,
  cash_in_methods: [],
  reason: '',
  calculating: false,
};

const reducer = (state = initial_state, action) => {
  switch (action.type) {
    case 'SET_PAY_METHOD':
      return {...state, payMethod: action.payload};
    case 'SET_EURO':
      return {...state, euros: action.payload};
    case 'SET_CFA':
      return {...state, cfa: action.payload};
    case 'SET_REASON':
      return {...state, reason: action.payload};
    case 'SET_EURO_ERROR':
      return {...state, euros_error: action.payload};
    case 'SET_CFA_ERROR':
      return {...state, cfa_error: action.payload};
    case 'SET_PAY_METHOD_ERROR':
      return {...state, payMethodError: action.payload};
    case 'SET_CASH_IN_METHODS':
      return {...state, cash_in_methods: action.payload};
    case 'CALCULATING_FEES':
      return {...state, calculating: action.payload};
    default:
      break;
  }
};

const CashInMethods = props => {
  const transferTimer = useDanaStore(state => state.transferTimer);
  const updateTransferTimer = useDanaStore(state => state.updateTransferTimer);
  const transfer = useDanaStore(state => state.transfer);
  const loggedInUser = useDanaStore(state => state.user);
  const saveTransfer = useDanaStore(state => state.saveTransfer);
  const beneficiary = useDanaStore(state => state.beneficiary);
  const sendingCountry = useDanaStore(state => state.sendingCountry);
  const transferTries = useDanaStore(state => state.transferTries);
  const activeOperationPage = useDanaStore(state => state.activeOperationPage);
  const setActiveOperationPage = useDanaStore(
    state => state.setActiveOperationPage,
  );

  const fetchSelectedCountryProviders = useDanaStore(
    state => state.fetchSelectedCountryProviders,
  );

  const user = useDanaStore(state => state.user);
  const [state, dispatch] = useReducer(reducer, initial_state);

  const setAllPaymentTypes = () => {
    const cash_in_methods = sendingCountry.cash_in_methods;
    dispatch({type: 'SET_CASH_IN_METHODS', payload: cash_in_methods});
  };

  const goBack = () => {
    props.navigation.goBack();
  };

  const setPaymentMethod = async () => {
    if (transfer?.cash_in_method.length === 0) {
      showMessage({
        type: 'danger',
        message: 'All fields are required',
      });
      return;
    }

    if (transfer?.cash_in_method_name.toLowerCase() === 'balance') {
      const balance = parseFloat(user?.client?.euro_balance);
      const sending = parseFloat(transfer?.amount_in_euro);
      // check if the balance is enough...
      if (balance < sending) {
        showMessage({
          message: "You don't have enough balance.",
          type: 'danger',
        });
        return;
      }
    }

    setActiveOperationPage('Transfer amount Page');
    props.navigation.navigate('PaymentInfo');
  };

  useEffect(() => {
    setI18nConfig();
    setAllPaymentTypes();
  }, []);

  useEffect(() => {
    fetchSelectedCountryProviders(transfer?.w_country?.receiving_country?.code);
  }, []);

  useEffect(() => {
    let timer;
    timer = setInterval(() => {
      updateTransferTimer('increment');
      if (transferTimer === 60) {
        const currentDate = new Date();
        const dateString = currentDate.toLocaleString();
        CustomerIO.track('Transfer Unfinished In 10 Mins', {
          userType: 'individual',
          operationTYpe: 'transfer',
          channel: 'mobile',
          email: loggedInUser?.email,
          'transfer start date': dateString,
          'phone number': `${loggedInUser?.country_code}${loggedInUser?.phone_number}`,
          'first name': loggedInUser?.firstname,
          'last name': loggedInUser?.lastname,
          page: activeOperationPage,
          'recipient first name ': beneficiary?.firstName,
          'recipient last name': beneficiary?.lastName,
          'recipient phone ': beneficiary.phone_number,
          'recipient email ': beneficiary.email,
          'transfer method': transfer?.cash_in_method_name,
          tries: transferTries,
        });
        CustomerIO.setProfileAttributes({
          'Transfer Done': false,
        });
        updateTransferTimer('reset');
      }
    }, 10000);

    return () => {
      clearInterval(timer);
    };
  }, [transferTimer]);

  return (
    <SafeAreaView style={styles.page}>
      <StatusBar barStyle="dark-content" backgroundColor={'#f1f1f1'} />
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={{flexGrow: 1, paddingVertical: 16}}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            <View style={{flex: 1}}>
              <TextCp textType="bold" style={{fontSize: 20}}>
                {translate('select_cash_in_method_title')}
              </TextCp>
              <TextCp
                style={{
                  color: state.payMethodError ? 'red' : '#282828',
                }}>
                {translate('select_cash_in_method_body')}
              </TextCp>
            </View>
          </View>

          <View style={styles.payOptionsList}>
            <ScrollView showsVerticalScrollIndicator={false}>
              {sendingCountry?.cash_in_methods?.map((option, index) => (
                <CashInMethodItem
                  option={option}
                  key={index}
                  transfer={transfer}
                  saveTransfer={item => saveTransfer(item)}
                />
              ))}
            </ScrollView>
          </View>
        </View>
      </ScrollView>
      <View style={styles.bottom}>
        <View style={{flex: 2, paddingRight: 10, paddingLeft: 1}}>
          <DanaButton
            title={translate('return')}
            onPress={goBack}
            theme="#fff"
            textColor="#282828"
          />
        </View>

        <View style={{flex: 2, paddingLeft: 10}}>
          <DanaButton
            title={translate('next')}
            onPress={setPaymentMethod}
            theme="#282828"
            testId="moveToSummaryScreenBtn"
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  page: {
    marginHorizontal: 16,
    backgroundColor: '#f1f1f1',
    flex: 1,
  },
  h1: {
    fontSize: titleFontSize,
    marginBottom: 17 * scaleRatio,
  },
  p: {
    fontSize: 15 * scaleRatio,
    marginBottom: 6 * scaleRatio,
  },
  input: {
    width: '100%',
    height: 56,
    backgroundColor: 'rgba(0, 0, 0, 0.07)',
    borderRadius: 8,
    marginVertical: 5.5,
    paddingHorizontal: 17 * scaleRatio,
  },
  bottom: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    paddingVertical: 16,
  },

  money: {
    fontSize: 25,
    color: '#000',
    marginBottom: 5,
  },

  payOptionsList: {
    marginVertical: 16,
  },
  amount: {
    color: 'rgba(0, 0, 0, 0.51)',
    fontSize: 12,
  },
  error: {
    fontSize: 12,
    color: 'red',
  },
  cash: {
    marginBottom: 16 * scaleRatio,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  bullet: {
    color: '#dadcdc',
    marginRight: 15,
  },
  name: {
    height: 19,
    color: 'rgba(0, 0, 0, 0.5)',
    fontSize: 16 * scaleRatio,
    marginRight: 10,
  },
  conversion: {
    color: 'rgba(0, 0, 0, 0.5)',
    fontSize: 16 * scaleRatio,
  },
  calc: {
    marginLeft: 15,
    marginTop: 11 * scaleRatio,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    paddingHorizontal: 26,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16 * scaleRatio,
  },
  innerContainerTra: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16,
  },
  modalTitle: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginVertical: 10,
    alignItems: 'center',
  },
  pay_img: {
    height: 40,
    width: 40,
    marginBottom: 8,
    position: 'absolute',
    right: 8,
    top: 8,
  },
});

export default CashInMethods;
