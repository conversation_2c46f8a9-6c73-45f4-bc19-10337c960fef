import {
  View,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  StatusBar,
  TouchableOpacity,
} from 'react-native';
import React, {useEffect, useState, useRef} from 'react';
import TextCp from '../components/TextCp';
import Icon from 'react-native-vector-icons/Feather';
import {setI18nConfig, translate} from '../utilities/Translate';
import {showMessage} from 'react-native-flash-message';
import FlashMessage from 'react-native-flash-message';
import useDanaStore from '../app_state/store';
import SelectInput from '../components/SelectInput';
import Input from '../components/Input';
import DanaButton from '../components/DanaButton';
import {
  sendCodeOnPhoneChange,
  updatePhone,
  verifyCodeForPhoneNumberChange,
} from '../apis/auth';
import {extractError} from '../utilities/errorReporting';
import {Modalize} from 'react-native-modalize';
import {ActivityIndicator} from 'react-native';

export default function ChangePhone({navigation}) {
  const getUserCurrentState = useDanaStore(state => state.getUserCurrentState);
  const user = useDanaStore(state => state.user);
  const countries = useDanaStore(state => state.countries);
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [verifyLoading, setVerifyLoading] = useState(false);
  const [country, setCountry] = useState('');
  const [timer, setTimer] = useState(0);
  const [resending, setResending] = useState(false);

  const ref = useRef();
  const modalFlashRef = useRef();

  const openModal = () => {
    ref.current?.open();
  };

  const closeModal = () => {
    ref.current?.close();
  };

  const startTimer = () => {
    setTimer(60);
    const interval = setInterval(() => {
      setTimer(prevTimer => {
        if (prevTimer <= 1) {
          clearInterval(interval);
          return 0;
        }
        return prevTimer - 1;
      });
    }, 1000);
    return () => clearInterval(interval);
  };

  const editPhone = async () => {
    try {
      if (!country || !phone) {
        showMessage({
          type: 'danger',
          message: translate('please_fill_all_fields'),
        });
        return;
      }

      setLoading(true);
      const payload = {
        country_code: +country.country_code,
        phone_number: phone,
      };
      const res = await sendCodeOnPhoneChange(payload);
      if ('message' in res) {
        openModal();
        startTimer();
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      showMessage({
        type: 'danger',
        message: extractError(error),
      });
    }
  };

  const resendCode = async () => {
    try {
      setResending(true);
      const payload = {
        country_code: +country.country_code,
        phone_number: phone,
      };
      const res = await sendCodeOnPhoneChange(payload);
      if ('message' in res) {
        startTimer();
        modalFlashRef.current?.showMessage({
          type: 'success',
          message: translate('code_sent_successfully'),
        });
      }
      setResending(false);
    } catch (error) {
      setResending(false);
      modalFlashRef.current?.showMessage({
        type: 'danger',
        message: extractError(error),
      });
    }
  };

  const handleVerifyCode = async () => {
    try {
      // Validate code is exactly 6 digits
      if (code.length < 6) {
        modalFlashRef.current?.showMessage({
          type: 'danger',
          message: translate('otp_code_too_short'),
        });
        return;
      } else if (code.length > 6) {
        modalFlashRef.current?.showMessage({
          type: 'danger',
          message: translate('otp_code_too_long'),
        });
        return;
      } else if (!/^\d{6}$/.test(code)) {
        modalFlashRef.current?.showMessage({
          type: 'danger',
          message: translate('otp_code_digits_only'),
        });
        return;
      }

      // If validation passes, proceed with verification
      setVerifyLoading(true);
      const payload = {
        country_code: +country.country_code,
        phone_number: phone,
        verification_code: code,
      };
      const res = await verifyCodeForPhoneNumberChange(payload);
      if ('message' in res) {
        saveChangePassword();
      }
    } catch (error) {
      setVerifyLoading(false);
      modalFlashRef.current?.showMessage({
        type: 'danger',
        message: extractError(error),
      });
    }
  };

  const saveChangePassword = async () => {
    try {
      const res = await updatePhone(
        {
          country_code: +country.country_code,
          phone_number: phone,
          reason_for_modification: 'Updating phone number from mobile app',
        },
        user?.id,
      );
      if ('user' in res) {
        closeModal();
        setVerifyLoading(false);
        getUserCurrentState();
        showMessage({
          type: 'success',
          message: translate('phone_has_been_updated'),
        });
        navigation.goBack();
      } else {
        setVerifyLoading(false);
        modalFlashRef.current?.showMessage({
          type: 'danger',
          message: res.message,
        });
      }
    } catch (error) {
      setVerifyLoading(false);
      modalFlashRef.current?.showMessage({
        type: 'danger',
        message: extractError(error),
      });
    }
  };

  useEffect(() => {
    const country = countries.find(
      val => val.country_code === user?.country_code,
    );
    setCountry(country);
    setPhone(user?.phone_number);
  }, []);

  useEffect(() => {
    setI18nConfig();
  }, []);

  return (
    <SafeAreaView style={styles.page}>
      <StatusBar barStyle={'dark-content'} backgroundColor={'#fff'} />

      {/* Header */}
      <View style={styles.toolbar}>
        <View style={styles.toolbarContent}>
          <TextCp textType={'bold'} style={styles.headerTitle}>
            {translate('change_phone')}
          </TextCp>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.closeButton}>
            <Icon name="x-circle" size={28} color="#444" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.descriptionContainer}>
          <TextCp style={styles.description}>
            {translate('change_phone_desc')}
          </TextCp>
        </View>

        {/* Country Selection */}
        <View style={styles.inputGroup}>
          <SelectInput
            placeholder={{
              label: translate('country'),
              value: translate('country'),
            }}
            onValueChange={value => {
              if (value === 'Country') return;
              const selectedCountry = countries.find(
                val => val.country_code === value,
              );
              setCountry(selectedCountry);
            }}
            items={
              countries.map(val => ({
                label: val.name,
                value: val.country_code,
              })) || []
            }
            value={country?.country_code}
          />
        </View>

        {/* Phone Input */}
        <View style={styles.phoneInputContainer}>
          <View style={styles.countryCode}>
            <TextCp>+{country?.country_code}</TextCp>
          </View>
          <View style={styles.phoneInput}>
            <Input
              style={styles.input}
              onChange={setPhone}
              label={translate('PhoneNumber')}
              value={phone}
              keyboardType="number-pad"
            />
          </View>
        </View>

        {/* Update Button */}
        <View style={styles.buttonContainer}>
          <DanaButton
            theme="#282828"
            title={translate('update')}
            onPress={editPhone}
            loading={loading}
          />
        </View>
      </ScrollView>

      {/* Verification Modal */}
      <Modalize ref={ref} adjustToContentHeight>
        <View style={styles.modal}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <TextCp textType="bold" style={styles.modalTitle}>
                {translate('phone_number_verification')}
              </TextCp>
              <TextCp textType="light" style={styles.modalSubtitle}>
                {translate('phone_number_verification_desc')} +
                {country?.country_code} {phone}
              </TextCp>
            </View>

            <View style={styles.verificationInputContainer}>
              <Input
                label={translate('verification_code')}
                value={code}
                onChange={text => {
                  // Only allow digits
                  const numericText = text.replace(/[^0-9]/g, '');
                  setCode(numericText);
                }}
                style={styles.verificationInput}
                maxLength={6}
                keyboardType="number-pad"
                placeholder="Enter 6-digit code"
              />
            </View>

            <View style={styles.modalButtonContainer}>
              <DanaButton
                onPress={handleVerifyCode}
                title={translate('validate')}
                theme="#282828"
                loading={verifyLoading}
              />
            </View>

            {timer > 0 ? (
              <View style={styles.resendContainer}>
                <TextCp textType="light" style={styles.resendText}>
                  {translate('resend_code_in')}{' '}
                  <TextCp textType="bold" style={styles.timerText}>
                    {`00:${timer > 9 ? timer : `0${timer}`}`}
                  </TextCp>
                </TextCp>
              </View>
            ) : (
              <TouchableOpacity
                style={styles.resendButton}
                onPress={resendCode}
                disabled={resending}>
                {resending ? (
                  <ActivityIndicator size="small" color="#037375" />
                ) : (
                  <TextCp textType="bold" style={styles.resendButtonText}>
                    {translate('resend_code')}
                  </TextCp>
                )}
              </TouchableOpacity>
            )}
          </View>
          <FlashMessage position="top" ref={modalFlashRef} />
        </View>
      </Modalize>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  page: {
    flex: 1,
    backgroundColor: '#fff',
  },
  toolbar: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  toolbarContent: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 60,
  },
  headerTitle: {
    fontSize: 20,
    color: '#000',
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
  },
  content: {
    flex: 1,
  },
  descriptionContainer: {
    padding: 16,
    paddingBottom: 24,
  },
  description: {
    fontSize: 15,
    color: '#666',
    lineHeight: 22,
  },
  inputGroup: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  countryCode: {
    width: 80,
    height: 48,
    marginRight: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  phoneInput: {
    flex: 1,
  },
  input: {
    height: 48,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  buttonContainer: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  modal: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    position: 'relative',
    paddingBottom: 20,
  },
  modalContent: {
    padding: 16,
    paddingTop: 32,
  },
  modalCloseButton: {
    alignSelf: 'flex-end',
    padding: 8,
    marginRight: 8,
    marginTop: 8,
  },
  modalHeader: {
    paddingHorizontal: 24,
    marginVertical: 16,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 20,
    color: '#000',
    marginBottom: 8,
    textAlign: 'center',
  },
  modalSubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 12,
  },
  phoneDisplay: {
    backgroundColor: 'rgba(3, 115, 117, 0.1)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginTop: 8,
  },
  phoneText: {
    color: '#037375',
    fontSize: 16,
  },
  verificationInputContainer: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  verificationInput: {
    textAlign: 'center',
    letterSpacing: 2,
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalButtonContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  resendContainer: {
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  resendText: {
    fontSize: 14,
    color: '#666',
  },
  timerText: {
    color: '#037375',
  },
  resendButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginTop: 8,
  },
  resendButtonText: {
    color: '#037375',
    fontSize: 14,
  },
});
