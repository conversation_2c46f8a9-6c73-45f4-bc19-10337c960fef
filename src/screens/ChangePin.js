import React, {useEffect, useState} from 'react';
import {View, StyleSheet, StatusBar, SafeAreaView} from 'react-native';
import {translate, setI18nConfig} from '../utilities/Translate';
import PinCode from '../components/PinCode';
import Toolbar from '../components/Toolbar';
import {pagePadding} from '../theme';
import {changePin} from '../apis/auth';
import {showMessage} from 'react-native-flash-message';
import useDanaStore from '../app_state/store';
import {extractError} from '../utilities/errorReporting';

const ChangePin = props => {
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const token = useDanaStore(state => state.token);
  const [pins, setPins] = useState({
    oldPin: '',
    newPin: '',
    newPin_confirmation: '',
  });

  useEffect(() => {
    setI18nConfig();
  }, []);

  const handleLastPinInput = pincode => {
    if (pins.newPin !== pincode) {
      showMessage('New and confirm pins didnt match');
      return;
    }

    const payload = {
      oldPin: pins.oldPin,
      newPin: pins.newPin,
      newPin_confirmation: pincode,
    };
    setLoading(true);
    changePin(payload)
      .then(res => {
        setLoading(false);
        showMessage({
          message: translate('pin_changed_successfully'),
          type: 'success',
        });
        // navigateWithNoHistory(props, CommonActions, 'Profile');
        props.navigation.goBack();
      })
      .catch(error => {
        setLoading(false);
        showMessage({
          message: extractError(error),
          type: 'danger',
        });
      });
  };

  const goBack = () => {
    props.navigation.goBack();
  };

  return (
    <SafeAreaView
      style={{
        backgroundColor: '#f1f1f1',
        flex: 1,
        marginHorizontal: pagePadding,
      }}>
      <StatusBar barStyle="dark-content" backgroundColor={'#f1f1f1'} />
      <Toolbar goBack={goBack} />
      {step === 1 && (
        <PinCode
          titleText={translate('enter_old_pin')}
          subTitleText={translate('enter_old_pin_desc')}
          pinFinished={result => {
            setPins(prev => ({...prev, oldPin: result.join('')}));
            setStep(prev => prev + 1);
          }}
        />
      )}
      {step === 2 && (
        <PinCode
          titleText={translate('enter_new_pin')}
          subTitleText={translate('enter_new_pin_desc')}
          pinFinished={result => {
            setPins(prev => ({...prev, newPin: result.join('')}));
            setStep(prev => prev + 1);
          }}
        />
      )}
      {step === 3 && (
        <PinCode
          titleText={translate('confirm_pin_title')}
          subTitleText={translate('confirm_pin_title_desc')}
          pinFinished={result => handleLastPinInput(result.join(''))}
          loadingText={translate('changing_pin')}
          loading={loading}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  spinnerTextStyle: {
    color: '#FFF',
  },
});

export default ChangePin;
