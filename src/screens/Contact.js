import React, {useEffect, useState, useRef} from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  TextInput,
  StatusBar,
  SafeAreaView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import Contacts from 'react-native-contacts';
import TextCp from '../components/TextCp';
import ContactItem from '../components/ContactItem';
import {colors, scaleRatio} from '../theme';
import {setI18nConfig, translate} from '../utilities/Translate';
import Icon from 'react-native-vector-icons/Feather';
import {showMessage} from 'react-native-flash-message';
import AddContactModal from '../components/modals/AddContactModal';
import {getNumberAndCountry} from '../utilities';
import useDanaStore from '../app_state/store';
import useContactPermission from '../Hooks/useContactPermission';

const Contact = props => {
  const {hasPermission, requestPermission, loading} = useContactPermission();
  const state_countries = useDanaStore(state => state.countries);
  const [contacts, setContacts] = useState([]);
  const [contactsClone, setContactsClone] = useState([]);
  const [searchTerm, setSearchTerm] = useState(null);
  const [details, setDetails] = useState({
    first_name: '',
    last_name: '',
    country: null,
    phone_number: '',
  });

  const modalizeRef = useRef(null);

  const onOpen = () => {
    modalizeRef.current?.open();
  };

  const closeModalize = () => {
    modalizeRef.current?.close();
  };


  const getContacts = () => {
    if (hasPermission) {
      Contacts.getAll()
        .then(res => {
          const filteredContacts = res.filter(
            user => user.phoneNumbers && user.phoneNumbers.length > 0
          );
          setContacts(filteredContacts);
          setContactsClone(filteredContacts);
        })
        .catch(error => {
          showMessage({
            message: error.message,
            type: 'danger',
          });
        });
    }
  };

  const contactClicked = contact => {
    if (contact) {
      const data = getNumberAndCountry(contact, state_countries);
      setDetails({
        first_name: contact?.familyName || contact?.middleName,
        last_name: contact?.givenName || contact?.middleName,
        ...data,
      });
    }

    onOpen();
  };

  const goBack = () => {
    props.navigation.goBack();
  };

  const search = searchText => {
    setSearchTerm(searchText);
    if (searchText && searchText?.trim() !== '') {
      const contactsFound = contactsClone?.filter(contact => {
        return (
          contact?.givenName
            ?.toLowerCase()
            ?.indexOf(searchText.trim().toLowerCase()) > -1
        );
      });
      setContactsClone(contactsFound);
    } else {
      setContactsClone(contacts);
    }
  };

  useEffect(() => {
    setI18nConfig();
  }, []);

  useEffect(() => {
    getContacts();
  }, [hasPermission]);

  return (
    <SafeAreaView style={styles.page}>
      <StatusBar backgroundColor={'#fff'} barStyle="dark-content" />
      <View
        style={{
          paddingHorizontal: 16,
        }}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            paddingVertical: 16,
          }}>
          <TextCp textType="bold" style={styles.title}>
            {translate('create_a_beneficiary')}
          </TextCp>
          <TouchableOpacity onPress={() => goBack()}>
            <Icon name="x-circle" color="#000" size={24} />
          </TouchableOpacity>
        </View>

        <TextCp style={styles.subTitle}>{translate('contactSubTitle')}</TextCp>

        <View style={styles.searchAndAdd}>
          <View style={styles.search}>
            <Icon name="search" size={20} color="#777" />
            <TextInput
              onChangeText={text => search(text)}
              value={searchTerm}
              placeholder={translate('search_by_name_phone_or_email')}
              style={styles.input}
              placeholderTextColor={'#aaa'}
            />
          </View>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => contactClicked(null)}>
            <Icon name="plus" color="#fff" size={28} />
          </TouchableOpacity>
        </View>
      </View>

      <FlatList
        showsVerticalScrollIndicator={false}
        data={contactsClone.sort((a, b) =>
          a.givenName.localeCompare(b.givenName),
        )}
        style={{flex: 1}}
        renderItem={({item}) => (
          <ContactItem contactClicked={contactClicked} item={item} />
        )}
        numColumns={1}
        keyExtractor={(item, index) => index}
        contentContainerStyle={{paddingHorizontal: 16}}
        ListEmptyComponent={
          !hasPermission && (
            <View style={styles.alert}>
              <TextCp align="center">{translate('request_permission1')}</TextCp>
              <TextCp align="center">{translate('request_permission2')}</TextCp>
              {loading ? (
                <View style={styles.allow_btn}>
                  <ActivityIndicator color={'#fff'} size="small" />
                </View>
              ) : (
                <>
                  <TouchableOpacity
                    style={styles.allow_btn}
                    onPress={requestPermission}>
                    <TextCp color="#fff" textType={'bold'}>
                      {translate('request')}
                    </TextCp>
                  </TouchableOpacity>
                </>
              )}
            </View>
          )
        }
      />
      <AddContactModal
        closeModalize={closeModalize}
        details={details}
        ref={modalizeRef}
        fetchContacts={() => getContacts()}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  contact: {
    flexDirection: 'row',
    marginBottom: 15 * scaleRatio,
  },

  input: {
    width: '100%',
    height: 45,
    backgroundColor: '#fff',
    borderRadius: 5,
    marginVertical: 5.5 * scaleRatio,
    paddingHorizontal: 17,
    fontSize: 13,
    color: '#222',
  },
  page: {
    // marginHorizontal: pagePadding,
    backgroundColor: '#fff',
    // paddingTop: Platform.OS === 'android' ? 1 : 0,
    flex: 1,
  },
  title: {
    fontSize: 22 * scaleRatio,
    marginBottom: 2 * scaleRatio,
  },
  list: {
    marginVertical: 5 * scaleRatio,
  },
  subTitle: {
    marginBottom: 6 * scaleRatio,
    width: '85%',
  },
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    paddingHorizontal: 26,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16 * scaleRatio,
  },
  innerContainerTra: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16,
  },
  modalTitle: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginVertical: 10,
    alignItems: 'center',
  },
  search: {
    marginVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffff',
    paddingHorizontal: 16,
    flex: 1,
    marginRight: 16,
    borderWidth: 1,
    borderRadius: 8,
    borderColor: 'rgba(0, 0, 0, 0.2)',
  },
  input: {
    paddingVertical: 8,
    paddingLeft: 16,
    height: 48,
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    flex: 1,
    color: '#444',
  },

  addButton: {
    height: 48,
    width: 48,
    borderRadius: 40,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchAndAdd: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  alert: {
    marginVertical: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F7E4CD',
    paddingHorizontal: 10,
    borderRadius: 8,
    paddingTop: 16,
  },
  allow_btn: {
    backgroundColor: colors.primary,
    paddingHorizontal: 29,
    height: 45,
    borderRadius: 8,
    marginVertical: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default Contact;
