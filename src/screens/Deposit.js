import {
  View,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Dimensions,
  Image,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {CustomerIO} from 'customerio-reactnative';
import Icon from 'react-native-vector-icons/Feather';
import {setI18nConfig, translate} from '../utilities/Translate';
import {titleFontSize} from '../theme';
import TextCp from '../components/TextCp';
import DanaButton from '../components/DanaButton';
import useDanaStore from '../app_state/store';
import {depositOnAccount, getAppliedFeesForDeposits} from '../apis/Transfers';
import ExternalPaymentsModal from '../components/ExternalPaymentsModal';
import {showMessage} from 'react-native-flash-message';
import CashInput from '../components/CashInput';
import {pay_method_images} from '../images/pay_method';
import CashLayout from '../components/CashLayout';
import Heap from '@heap/react-native-heap';
import Offline from '../components/Offline';

const width = Dimensions.get('window').width;
const RATE = 655.957;

const method_text = {
  'bank card': {
    title: 'bank_card_instruction_title',
    description: 'bank_card_instruction_description',
  },
  'bank transfer': {
    title: 'bank_transfer_instruction_title',
    description: 'bank_transfer_instruction_description',
  },
};

const Deposit = ({navigation}) => {
  const [show, setShow] = useState(false);
  const saveTransferResponse = useDanaStore(
    state => state.saveTransferResponse,
  );
  const depositTimer = useDanaStore(state => state.depositTimer);
  const updateDepositTimer = useDanaStore(state => state.updateDepositTimer);
  const depositTries = useDanaStore(state => state.depositTries);

  const transfer_response = useDanaStore(state => state.transfer_response);
  const sendingCountry = useDanaStore(state => state.sendingCountry);
  const storeOperationTrackingId = useDanaStore(
    state => state.storeOperationTrackingId,
  );
  const user = useDanaStore(state => state.user);

  const token = useDanaStore(state => state.token);
  const trackingId = useDanaStore(state => state.trackingId);
  const setDespositPayload = useDanaStore(state => state.setDespositPayload);
  const deposit_payload = useDanaStore(state => state.deposit_payload);
  const [fees, setFees] = useState(null);
  const [processing, setProcessing] = useState(false);

  const makeDeposit = async () => {
    const {selected_method, amount_in_euro, amount_in_cfa} =
      deposit_payload || {};

    if (amount_in_euro.length === 0) {
      showMessage({
        type: 'danger',
        message: translate('euro_amount_required'),
      });
      return;
    }

    if (selected_method === null) {
      showMessage({
        type: 'danger',
        message: translate('select_cashin_method'),
      });
      return;
    }

    if (amount_in_cfa.length === 0) {
      showMessage({
        type: 'danger',
        message: translate('cfa_amount_required'),
      });
      return;
    }

    try {
      setProcessing(true);
      const transactionData = {
        cashin_method_id: selected_method?.cash_in_method.id,
        amount_without_fees_in_euro: amount_in_euro,
        country_id: sendingCountry?.id,
        heapio_track_id: trackingId,
      };

      Heap.track('Deposit Payment Details Confirmed:');
      const response = await depositOnAccount({data: transactionData, token});
      storeOperationTrackingId(null);
      if (
        selected_method?.cash_in_method.name.toLowerCase() === 'bank transfer'
      ) {
        // navigate to the bank page
        navigation.navigate('BankerTransfer');
      } else {
        // load webView
        saveTransferResponse({body: response, nextPage: 'TransferSuccess'});
        setShow(true);
      }
      setProcessing(false);
    } catch (error) {
      setProcessing(false);
      const code = error.statusCode;
      showMessage({
        type: 'danger',
        message: error.message,
      });
    }
  };

  const renderImage = (imageSrc, selected) => {
    if (selected) {
      return (
        <Image
          source={pay_method_images[imageSrc]}
          style={[styles.pay_img]}
          resizeMode="contain"
        />
      );
    } else {
      return <TextCp></TextCp>;
    }
  };

  const calculateFees = async () => {
    try {
      const payloadData = {
        euro_amount: deposit_payload?.amount_in_euro,
        sending_country_id: sendingCountry?.id,
        cashin_method_id: deposit_payload?.selected_method?.cash_in_method.id,
      };
      const response = await getAppliedFeesForDeposits({
        data: payloadData,
        user_id: user?.id,
        token,
      });
      setFees(response);
    } catch (error) {}
  };

  useEffect(() => {
    setI18nConfig();
  }, []);

  useEffect(() => {
    calculateFees();
  }, [deposit_payload]);

  useEffect(() => {
    const timer = setInterval(() => {
      const counter = depositTimer + 1;
      updateDepositTimer('increment');
      if (counter === 60) {
        updateDepositTimer('reset');
        const currentDate = new Date();
        const dateString = currentDate.toLocaleString();
        CustomerIO.track('Deposit Unfinished in 10 minutes', {
          userType: 'individual',
          operationTYpe: 'deposit',
          channel: 'mobile',
          email: user?.email,
          'deposit start date': dateString,
          'phone number': `${user?.country_code}${user?.phone_number}`,
          'first name': user?.firstname,
          'last name': user?.lastname,
          page: 'Amount Confirmation Page',
          tries: depositTries,
        });
        CustomerIO.setProfileAttributes({
          'Deposit Done': false,
        });
      }
    }, 10000);

    return () => {
      clearInterval(timer);
    };
  }, [depositTimer]);

  return (
    <SafeAreaView style={{flex: 1}}>
      <Offline />
      <StatusBar backgroundColor={'#f1f1f1'} barStyle="dark-content" />
      <View style={styles.toolbar}>
        <TextCp style={styles.h1} textType="bold">
          {translate('make_deposit')}
        </TextCp>
        <TouchableOpacity
          onPress={() => {
            Heap.track('Deposit canceled');
            updateDepositTimer('reset');
            navigation.goBack();
          }}>
          <Icon name="x-circle" color="#333" size={30} />
        </TouchableOpacity>
      </View>

      <ScrollView
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}>
        <View style={styles.balanceBox}>
          <TextCp>{translate('yob')}</TextCp>
          <CashLayout
            value={user?.client?.euro_balance}
            color="#037375"
            fontSize={20}
          />
        </View>

        <TextCp>{translate('select_cashin')}</TextCp>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {sendingCountry?.cash_in_methods
            .filter(val => val.cash_in_method.name.toLowerCase() !== 'balance')
            .map((method, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.methodBox,
                  {
                    backgroundColor:
                      deposit_payload?.selected_method?.id == method.id
                        ? '#049D9F'
                        : 'rgba(0, 0, 0, 0.07)',
                  },
                ]}
                onPress={() =>
                  setDespositPayload({
                    selected_method: method,
                  })
                }>
                {renderImage(
                  method.cash_in_method?.name
                    .toLowerCase()
                    .split(' ')
                    .join('_'),
                  deposit_payload?.selected_method?.id == method.id,
                )}
                <TextCp
                  style={{
                    position: 'absolute',
                    left: 16,
                    bottom: 16,
                  }}
                  color={
                    deposit_payload?.selected_method?.id == method.id
                      ? '#f1f1f1'
                      : '#222'
                  }
                  textType={
                    deposit_payload?.selected_method?.id == method.id
                      ? 'bold'
                      : 'regular'
                  }>
                  {translate(
                    method.cash_in_method?.name
                      .toLowerCase()
                      .split(' ')
                      .join('_'),
                  )}
                </TextCp>
              </TouchableOpacity>
            ))}
        </ScrollView>
        {deposit_payload?.selected_method &&
          deposit_payload?.selected_method?.cash_in_method?.name?.toLowerCase() in
            method_text && (
            <View style={{marginBottom: 16, marginTop: 8}}>
              <TextCp color="#a3a3a3">
                <TextCp color="#a3a3a3" textStyle="bold">
                  {translate(
                    method_text[
                      deposit_payload.selected_method.cash_in_method?.name?.toLowerCase()
                    ]?.title,
                  )}{' '}
                  :
                </TextCp>{' '}
                {translate(
                  method_text[
                    deposit_payload.selected_method.cash_in_method?.name?.toLowerCase()
                  ]?.description,
                )}
              </TextCp>
            </View>
          )}

        <CashInput
          value={deposit_payload?.amount_in_euro?.toString()}
          onChange={text => {
            setDespositPayload({
              amount_in_euro: text,
              amount_in_cfa:
                text.length > 0 ? (parseFloat(text) * RATE).toFixed(2) : '',
            });
          }}
          placeholder={translate('calcMoneyInEUR')}
          style={{}}
          currency="EUR"
        />

        <CashInput
          value={deposit_payload?.amount_in_cfa?.toString()}
          onChange={text => {
            setDespositPayload({
              amount_in_cfa: text,
              amount_in_euro:
                text.length > 0 ? (parseFloat(text) / RATE).toFixed(2) : '',
            });
          }}
          placeholder={translate('calcMoneyInCFA')}
          style={{}}
          currency="XOF"
        />

        <View style={styles.row}>
          <TextCp>{translate('exchange_rate')}</TextCp>
          <TextCp textType={'bold'} style={{color: '#037375'}}>
            1 EUR - {RATE} CFA
          </TextCp>
        </View>

        <View style={styles.row}>
          <TextCp>{translate('applied_fees')}</TextCp>

          <CashLayout
            value={fees ? fees.fee : 0}
            color="#037375"
            fontSize={16}
          />
        </View>
        <DanaButton
          onPress={makeDeposit}
          title={translate('continue')}
          theme="#282828"
          loading={processing}
          testID="startDepositBtn"
        />
      </ScrollView>
      <ExternalPaymentsModal
        show={show}
        navigation={navigation}
        connect_url={transfer_response?.body?.connect_url}
        translate={translate}
        setShow={setShow}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  page: {
    flex: 1,
    backgroundColor: '#f1f1f1',
  },
  toolbar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    height: 48,
    marginVertical: 8,
  },
  h1: {
    fontSize: titleFontSize,
    color: '#222',
  },
  content: {
    paddingHorizontal: 16,
  },
  balanceBox: {
    minHeight: 60,
    justifyContent: 'center',
    marginBottom: 8,
  },
  methodBox: {
    height: 98,
    width: width / 2.2,
    marginRight: 16,
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 16,
    marginTop: 8,
  },
  input: {
    width: '100%',
    height: 56,
    borderRadius: 8,
    paddingHorizontal: 17,
    fontFamily: 'Inter-Bold',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 10,
  },
  cashRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
  },
  cashRowLeft: {
    marginHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
    height: 56,
  },
  currency: {
    color: '#aaa',
  },
  pay_img: {
    height: 40,
    width: 40,
    marginBottom: 8,
    position: 'absolute',
    right: 16,
    top: 16,
  },
});

export default Deposit;
