import React, {useEffect, useState, useRef} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Image,
  ImageBackground,
  SafeAreaView,
  FlatList,
  ActivityIndicator,
  Platform,
  useColorScheme,
} from 'react-native';
import 'react-native-get-random-values';
import {v4 as uuidv4} from 'uuid';
import {useFocusEffect, useTheme} from '@react-navigation/native';
import analytics from '@react-native-firebase/analytics';
import {colors, scaleRatio} from '../theme';
import TextCp from '../components/TextCp';
import {translate} from '../utilities/Translate';
import Icon from 'react-native-vector-icons/Feather';
import AntDesignIcon from 'react-native-vector-icons/Ionicons';
import ContactList from '../components/ContactList';
import TransactionItem from '../components/TransactionItem';
import Modal from 'react-native-modal';
import UserAvatar from 'react-native-user-avatar';
import DanaButton from '../components/DanaButton';
import useDanaStore from '../app_state/store';
import {showMessage} from 'react-native-flash-message';
import CurrencySwitch from '../components/CurrencySwitch';
import CashLayout from '../components/CashLayout';
import {CommonActions} from '@react-navigation/native';
import {navigateWithNoHistory} from '../utilities/ForgetHistroty';
import {_getAllUserTransactions} from '../apis/Transfers';
import Heap from '@heap/react-native-heap';
import Offline from '../components/Offline';
import {CustomerIO} from 'customerio-reactnative';
import KycStatus from '../components/KycStatus';
import OperationDetailComp from './transfer/components/OperationDetailComp';
import {api_getUserCurrentState} from '../apis/auth';
import Intercom, {Visibility} from '@intercom/intercom-react-native';
import {tiktokLogEvents} from '../utilities/tiktok';

const Home = props => {
  const [showModal, setShowModal] = useState(false);
  const user = useDanaStore(state => state.user);
  const storeOperationTrackingId = useDanaStore(
    state => state.storeOperationTrackingId,
  );
  const countries = useDanaStore(state => state.countries);
  const currency = useDanaStore(state => state.currency);
  const saveChangeCurrency = useDanaStore(state => state.saveChangeCurrency);
  const saveSendingCountry = useDanaStore(state => state.saveSendingCountry);
  const saveBeneficiary = useDanaStore(state => state.saveBeneficiary);
  const getUserCurrentState = useDanaStore(state => state.getUserCurrentState);
  const fetchContacts = useDanaStore(state => state.fetchContacts);
  const contacts = useDanaStore(state => state.contacts);
  const updateHNTVP = useDanaStore(state => state.updateHNTVP);
  const updateDepositTimer = useDanaStore(state => state.updateDepositTimer);
  const updateTransferTimer = useDanaStore(state => state.updateTransferTimer);
  const increaseTries = useDanaStore(state => state.increaseTries);
  const updateUser = useDanaStore(state => state.updateUser);
  const [topOperations, setTopOperations] = useState([]);
  const [loadingOps, setLoadingOps] = useState(true);

  const store_fetch_all_transfers = useDanaStore(
    state => state.store_fetch_all_transfers,
  );

  const fetching_all_transfers = useDanaStore(
    state => state.fetching_all_transfers,
  );

  const amountHidden = useDanaStore(state => state.amountHidden);
  const updateAmountHidden = useDanaStore(state => state.updateAmountHidden);

  const setActiveOperationPage = useDanaStore(
    state => state.setActiveOperationPage,
  );

  const updateOnboardingTimer = useDanaStore(
    state => state.updateOnboardingTimer,
  );

  const [fetching, setFetching] = useState(false);

  const setActiveOperationType = useDanaStore(
    state => state.setActiveOperationType,
  );

  const hasNavigatedToVerifyPage = useDanaStore(
    state => state.hasNavigatedToVerifyPage,
  );

  const operationModalRef = useRef(null);

  const openOperationModal = () => {
    operationModalRef.current?.open();
  };

  const closeOperationModal = () => {
    operationModalRef.current?.close();
  };

  const canPerformTasks = user?.is_verified && user?.is_active;

  const goToContacts = () => {
    if (!canPerformTasks) {
      setShowModal(true);
      return;
    }
    props.navigation.navigate('Contact');
  };

  const getTopOperations = async () => {
    const response = await _getAllUserTransactions({
      page: 1,
      per_page: 10,
    });
    setTopOperations(response?.results.slice(0, 3) || []);
    setLoadingOps(false);
  };

  const startTransfer = async () => {
    if (!canPerformTasks) {
      setShowModal(true);
      return;
    }
    await analytics().logEvent('add_to_cart', {
      from: 'start_transfer_button',
      Platform: Platform.OS,
    }); //when the user clics on "Make a transfer"
    await tiktokLogEvents(
      'AddToCart',
      user,
      {
        from: 'start_transfer_button',
        Platform: Platform.OS,
      },
      Platform.OS,
    );

    const sending_country = countries.find(
      val => val.country_code === user?.country_code,
    );
    if (sending_country) {
      saveSendingCountry(sending_country);
      startTransferTracking('Transfer Initiated');
      updateTransferTimer('reset');
      increaseTries({type: 'increment', operations: 'transfer'});
      setActiveOperationType('transfer');
      setActiveOperationPage('Beneficiary Confirmation Page');
      props.navigation.navigate('BeneficiarySelection');
    }
  };

  const goToProfile = () => {
    if (canPerformTasks) {
      props.navigation.navigate('Profile');
    }
  };

  const showAll = () => {
    props.navigation.navigate('Transfers');
  };

  const startTransferFromUser = async selected_user => {
    if (!canPerformTasks) {
      setShowModal(true);
      return;
    }

    const {favorite} = selected_user;
    const sending_country = countries.find(
      val => val.country_code === user?.country_code,
    );

    if (!sending_country) {
      return;
    }
    saveSendingCountry(sending_country);

    const country_object = sending_country.receiving_countries.find(
      val =>
        val.receiving_country.name.toLowerCase() ===
        favorite.country.toLowerCase(),
    );

    if (!country_object) {
      showMessage({
        type: 'danger',
        message: `${favorite.country} ${translate('not_receiving_country')} ${
          user?.country
        }`,
      });
      return;
    }

    saveBeneficiary({...favorite, country: country_object.receiving_country});
    await analytics().logEvent('add_to_cart', {
      Platform: Platform.OS,
      from: 'favorite_contact',
    }); //when the user clics on "Make a transfer"
    await tiktokLogEvents(
      'AddToCart',
      user,
      {
        from: 'favorite_contact',
        Platform: Platform.OS,
      },
      Platform.OS,
    );
    props.navigation.navigate('BeneficiarySelection');
  };

  const startVerification = () => {
    setShowModal(false);
    props.navigation.navigate('Sumsub', {toPage: 'Home'});
  };

  const handleSoftLogout = () => {
    navigateWithNoHistory(props, CommonActions, 'Lock');
  };

  const startTransferTracking = eventTracked => {
    const trackingId = uuidv4();
    Heap.clearEventProperties();
    storeOperationTrackingId(`${user?.external_user_id}${trackingId}`);
    Heap.addEventProperties({
      'track ID': `${user?.external_user_id}${trackingId}`,
      'user type': 'individual',
    });
    Heap.track(eventTracked);
  };

  useEffect(() => {
    if (!canPerformTasks && !hasNavigatedToVerifyPage) {
      updateHNTVP(true);
      props.navigation.navigate('Sumsub', {toPage: 'Home'});
    }
  }, [hasNavigatedToVerifyPage]);

  useEffect(() => {
    Intercom.setLauncherVisibility(Visibility.VISIBLE);
    updateOnboardingTimer('reset');
    getTopOperations();
    // store_fetch_all_transfers(1);
    getUserCurrentState();
    fetchContacts();
  }, []);

  // called when the page is focused again
  useFocusEffect(
    React.useCallback(() => {
      getTopOperations();
      // store_fetch_all_transfers(1);
      getUserCurrentState();
      fetchContacts();
    }, []),
  );

  const onRefresh = React.useCallback(() => {
    getUserCurrentState();
    getTopOperations();
  }, [getUserCurrentState]);

  useEffect(() => {
    if (canPerformTasks) {
      CustomerIO.setProfileAttributes({
        'Onboarding Done': true,
      });
    }
  }, [user]);

  const pollUserState = async () => {
    if (!user?.is_verified || !user?.is_active) {
      try {
        const response = await api_getUserCurrentState();

        if (response?.is_verified && !user?.is_verified) {
          showMessage({
            type: 'success',
            message: translate('user_verified_notification'),
          });
        }

        if (response?.is_active && !user?.is_active) {
          showMessage({
            type: 'success',
            message: translate('user_activated_notification'),
          });
        }

        updateUser(response);
      } catch (error) {
        console.log(error);
      }
    }
  };

  useEffect(() => {
    let isPolling = true;

    const poll = async () => {
      while (isPolling) {
        await pollUserState();
        await new Promise(resolve => setTimeout(resolve, 10000)); // Poll every 10 seconds
      }
    };

    poll();

    return () => {
      isPolling = false;
    };
  }, [user?.is_verified, user?.is_active]);

  useEffect(() => {
    if (user?.is_rejected) {
      navigateWithNoHistory(props, CommonActions, 'Rejected');
    }
  }, [user]);

  return (
    <>
      <SafeAreaView style={styles.page}>
        <Offline />
        <StatusBar barStyle="dark-content" backgroundColor={'#f1f1f1'} />
        <View style={styles.toolbar}>
          <TouchableOpacity
            style={styles.userBox}
            onPress={goToProfile}
            testID="goToProfileButton">
            <UserAvatar
              size={40 * scaleRatio}
              name={user?.full_name}
              bgColors={['#4a4e69', '#264653', '#219ebc', '#b5838d']}
              textColor={'#fff'}
              src=""
              textStyle={{fontSize: 16}}
              style={styles.user}
            />
            <View style={styles.userBoxDetails}>
              <TextCp textType={'bold'} align="left" style={styles.user_name}>
                {user?.full_name}
              </TextCp>
              <TextCp style={styles.user_type} align="left">
                {user?.is_individual
                  ? user?.email
                    ? user?.email
                    : user?.full_phone_number
                  : user?.company?.name}
              </TextCp>
            </View>
          </TouchableOpacity>

          <View style={styles.toolbarLeft}>
            <CurrencySwitch
              onPress={e => saveChangeCurrency(e)}
              value={currency}
              title={translate('currency')}
            />
            <TouchableOpacity
              onPress={handleSoftLogout}
              style={styles.logoutBtn}
              testID="softLogOutButton">
              <AntDesignIcon name="power-sharp" size={26} color={'#370617'} />
            </TouchableOpacity>
          </View>
        </View>
        <KycStatus
          goToKyc={() => {
            props.navigation.navigate('Sumsub');
          }}
        />
        {canPerformTasks && (
          <FlatList
            showsVerticalScrollIndicator={false}
            onRefresh={onRefresh}
            refreshing={fetching}
            ListHeaderComponent={
              <>
                <View style={styles.balance_buttons}>
                  {/* Background image positioned bottom right */}
                  <Image
                    source={require('../images/bg_image.png')}
                    style={{
                      position: 'absolute',
                      bottom: 0,
                      right: -20,
                      width: '100%', // adjust as needed
                      height: 120, // adjust as needed
                      zIndex: 0,
                      opacity: 0.35, // optional: make it subtle
                      resizeMode: 'contain',
                    }}
                    pointerEvents="none"
                  />
                  <View
                    style={{
                      ...styles.balance,
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      zIndex: 1,
                    }}>
                    <View style={styles.balance_header}>
                      <View>
                        <TouchableOpacity
                          onPress={() => updateAmountHidden(!amountHidden)}
                          style={{flexDirection: 'row', alignItems: 'center'}}>
                          <TextCp style={styles.balance_text} color="#fff">
                            {translate('balance')}
                          </TextCp>
                          {amountHidden ? (
                            <Icon
                              name="eye"
                              style={{marginLeft: 10}}
                              color="#fff"
                            />
                          ) : (
                            <Icon
                              name="eye-off"
                              style={{marginLeft: 10}}
                              color="#fff"
                            />
                          )}
                        </TouchableOpacity>

                        <CashLayout
                          value={user?.client?.euro_balance}
                          fontSize={22}
                          color="#fff"
                          amountHidden={amountHidden}
                        />
                      </View>
                    </View>

                    <View>
                      <TouchableOpacity
                        onPress={() => props.navigation.navigate('Rewards')}
                        style={{
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}>
                        <AntDesignIcon name="gift" size={26} color={'#fff'} />
                        <TextCp align="center" color="#fff">
                          {translate('rewards')}
                        </TextCp>
                      </TouchableOpacity>
                    </View>
                  </View>
                  <View style={{padding: 8, marginTop: 8, zIndex: 1}}>
                    <DanaButton
                      onPress={startTransfer}
                      title={translate('start_transfer')}
                      theme="#000"
                      textColor="#eee"
                      icon={'arrow-up-right'}
                      testID="startTransferButton"
                    />
                  </View>
                </View>

                <View style={[styles.contacts, styles.uniSPace]}>
                  <View style={styles.contacts_btn}>
                    <TextCp textType={'bold'}>
                      {translate('contacts')} ({contacts.length})
                    </TextCp>
                    <TouchableOpacity
                      testID="goToAllContactsButton"
                      onPress={() => {
                        props.navigation.navigate('AllContacts');
                      }}>
                      <TextCp textType={'bold'} color="#037375">
                        {translate('view_all')}
                      </TextCp>
                    </TouchableOpacity>
                  </View>
                  <TextCp style={{marginVertical: 10}} opacity={0.8}>
                    {translate('all_contacts_text')}
                  </TextCp>

                  <ContactList
                    contacts={contacts}
                    goToContacts={goToContacts}
                    startTransfer={startTransferFromUser}
                  />
                </View>

                <View>
                  <View style={styles.listHeader}>
                    <TextCp textType={'bold'}>{translate('transfers')}</TextCp>
                    <TouchableOpacity onPress={() => showAll()}>
                      <TextCp textType={'bold'} color="#037375">
                        {translate('history')}
                      </TextCp>
                    </TouchableOpacity>
                  </View>
                </View>

                {(loadingOps) && (
                  <View style={{padding: 10}}>
                    <ActivityIndicator size="small" color={colors.primary} />
                  </View>
                )}
              </>
            }
            data={topOperations}
            renderItem={({item}) => (
              <TransactionItem
                value={item}
                openOperationModal={openOperationModal}
              />
            )}
            keyExtractor={(item, index) => item?.id || index.toString()}
            ListEmptyComponent={
              <View style={styles.empty}>
                {topOperations ? (
                  <>
                    <ActivityIndicator size="large" color="#037375" />
                  </>
                ) : (
                  <>
                    <TextCp textType={'bold'}>
                      {translate('no_transfer')}
                    </TextCp>
                    <TextCp align="center" opacity={0.5}>
                      {translate('no_transfer_desc')}
                    </TextCp>
                  </>
                )}
              </View>
            }
            ListFooterComponent={
              <View
                style={{
                  height: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <TouchableOpacity onPress={() => showAll()}>
                  <TextCp color="#037375">
                    {translate('view_all_transfers')}
                  </TextCp>
                </TouchableOpacity>
              </View>
            }
            extraData={topOperations}
          />
        )}

        <Modal
          isVisible={showModal}
          animationType="fade"
          statusBarTranslucent={true}
          style={styles.modal_outter_view}>
          <View style={styles.modal}>
            <View style={styles.innerContainer}>
              <View style={styles.centerView}>
                <TouchableOpacity onPress={() => setShowModal(false)}>
                  <Icon
                    name="chevrons-down"
                    size={32 * scaleRatio}
                    color="#4a4a4a"
                    style={{textAlign: 'center'}}
                  />
                </TouchableOpacity>
              </View>
              <View
                style={{
                  marginVertical: 16,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Image
                  source={require('../images/id.png')}
                  style={styles.verification_image}
                />
                <TextCp
                  style={styles.notVeirifiedText}
                  color="#BC4749"
                  align="center"
                  opacity={0.8}
                  textType="bold">
                  {translate('account_not_verified')}
                </TextCp>
                <TextCp align="center">
                  {translate('account_not_verified_desc')}
                </TextCp>
              </View>
              <View style={{marginVertical: 24}}>
                <DanaButton
                  onPress={() => startVerification()}
                  title={translate('start_verification')}
                  theme="#282828"
                  testID="startVerificationButton"
                />
              </View>
            </View>
          </View>
        </Modal>
      </SafeAreaView>

      <OperationDetailComp
        ref={operationModalRef}
        closeOperationModal={() => {
          closeOperationModal();
          getTopOperations();
        }}
        navigation={props.navigation}
      />
    </>
  );
};

const styles = StyleSheet.create({
  page: {
    flex: 1,
    backgroundColor: '#f1f1f1',
    marginHorizontal: 16,
    paddingVertical: 16,
  },
  toolbar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    alignItems: 'center',
    // paddingHorizontal: 8,
  },
  modal_outter_view: {backgroundColor: 'rgba(154,155,159, 0.3)', margin: 0},
  centerView: {justifyContent: 'center', alignItems: 'center'},
  notVeirifiedText: {
    marginBottom: 10,
    fontSize: 20,
  },
  user_name: {
    fontSize: 16,
    textTransform: 'capitalize',
  },
  verification_image: {
    height: 100,
    width: 100,
    marginBottom: 18,
  },
  listHeader: {
    paddingTop: 16,
    paddingBottom: 16,
    backgroundColor: '#f1f1f1',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userBox: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 2,
  },
  user: {
    height: 50,
    width: 50,
    borderRadius: 30,
    marginRight: 10,
  },
  user_type: {
    fontSize: 11,
  },
  userBoxDetails: {
    flex: 1,
    marginRight: 10,
  },
  balance_buttons: {
    backgroundColor: '#023428',
    paddingHorizontal: 8,
    paddingVertical: 8,
    borderRadius: 10,
    marginBottom: 16,
    position: 'relative',
    // paddingVertical: 40,
    // position: 'absolute',
    // bottom:0
  },
  imgInIb: {},

  balance_text: {
    fontSize: 14,
    color: '#f1f1f1',
  },
  balance_amount: {
    fontSize: 22,
    color: '#f1f1f1',
  },
  balance_header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 2,
  },

  buttons: {
    flexDirection: 'row',
    marginVertical: 8,
    marginTop: 20,
  },
  balance: {
    paddingHorizontal: 8,
    marginVertical: 8,
  },
  button: {
    flex: 1,
    marginHorizontal: 4,
  },

  transfer_section: {
    backgroundColor: '#fff',
    borderRadius: 8,
    paddingBottom: 9,
    paddingHorizontal: 9,
    paddingVertical: 9,
  },
  transfer_section_details: {
    marginVertical: 9,
  },
  contacts: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
  },
  contacts_btn: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  gray_text: {
    color: '#4a4a4a',
  },
  empty: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  uniSPace: {
    marginVertical: 8,
  },
  modal: {
    backgroundColor: '#f1f1f1',
    position: 'absolute',
    right: 0,
    left: 0,
    bottom: 0,
    margin: 0,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    paddingVertical: 32,
    paddingHorizontal: 20,
  },
  prices: {
    backgroundColor: '#fff',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 4,
  },
  toolbarLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  logoutBtn: {
    height: 36,
    width: 36,
    paddingLeft: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderLeftWidth: 0.5,
    borderLeftColor: '#bbb',
  },
});

export default Home;
