import {View, StyleSheet, SafeAreaView, Image, StatusBar} from 'react-native';
import React, {useEffect} from 'react';
import {setI18nConfig, translate} from '../utilities/Translate';
import TextCp from '../components/TextCp';
var pjson = require('../../package.json');

const Init = ({navigation}) => {
  useEffect(() => {
    setI18nConfig();
    navigation.navigate('Loading');
  }, []);

  return (
    <SafeAreaView style={styles.page}>
      <StatusBar backgroundColor={'#fff'} barStyle={'dark-content'} />
      <View style={styles.footer}>
        <TextCp style={styles.version}>V{pjson.version}</TextCp>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  page: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    backgroundColor: '#fff',
    padding: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  version: {
    fontSize: 10,
    color: '#666',
  },
});

export default Init;
