import React, {useEffect} from 'react';
import {View, StatusBar, StyleSheet, SafeAreaView, Text, Platform} from 'react-native';
import SplashScreen from 'react-native-splash-screen';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {navigateWithNoHistory} from '../utilities/ForgetHistroty';
import {CommonActions} from '@react-navigation/native';
import useDanaStore from '../app_state/store';
import {getTransferCountries} from '../apis/Transfers';
import FastImage from 'react-native-fast-image';
import analytics from '@react-native-firebase/analytics';

const Loading = props => {
  const updateConfiguredCounntries = useDanaStore(
    state => state.updateConfiguredCounntries,
  );

  const getCountries = async () => {
    try {
      await analytics().logEvent('launch_app', {
        timestamp: new Date().toISOString(),
        platform: Platform.OS,
      });
      const response = await getTransferCountries();
      updateConfiguredCounntries(response.data);
    } catch (error) {}
  };

  const navigate = () => {
    AsyncStorage.getItem('dana_store')
      .then(data => {
        if (data) {
          const {state} = JSON.parse(data);

          if (!state?.token || !state?.user) {
            SplashScreen?.hide();
            navigateWithNoHistory(props, CommonActions, 'Tutorial');
          } else {
            SplashScreen?.hide();
            if (!state?.temp_user?.has_pin) {
              navigateWithNoHistory(
                props,
                CommonActions,
                'AccountVerification',
              );
            } else {
              navigateWithNoHistory(props, CommonActions, 'Lock');
            }
          }
        } else {
          SplashScreen?.hide();
          navigateWithNoHistory(props, CommonActions, 'Tutorial');
        }
      })
      .catch(error => {
        console.log('error', error);
      });
  };

  useEffect(() => {
    getCountries();
    setTimeout(() => {
      navigate();
    }, 3500);
  }, []);

  return (
    <SafeAreaView style={styles.page}>
      <StatusBar barStyle="dark-content" backgroundColor={'#fff'} />
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <FastImage
          style={{width: 300, height: 300}}
          source={require('../lottie/logo.gif')}
          resizeMode={FastImage.resizeMode.contain}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  image: {
    height: 50,
  },
  page: {
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
});

export default Loading;
