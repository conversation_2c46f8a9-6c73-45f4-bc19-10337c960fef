import React, {useEffect, useState} from 'react';
import {View, Alert, Platform, StatusBar, StyleSheet} from 'react-native';
import Intercom from '@intercom/intercom-react-native';
import {pagePadding} from '../theme';
import {translate, setI18nConfig} from '../utilities/Translate';
import PinCode from '../components/PinCode';
import {CustomerIO} from 'customerio-reactnative';
import {navigateWithNoHistory} from '../utilities/ForgetHistroty';
import {CommonActions} from '@react-navigation/native';
import DanaButton from '../components/DanaButton';
import useDanaStore from '../app_state/store';
import {app_logout, getLastRegisterationStep, loginUser} from '../apis/auth';
import {showMessage} from 'react-native-flash-message';
import {extractError} from '../utilities/errorReporting';
import Offline from '../components/Offline';
import ReactNativeBiometrics from 'react-native-biometrics';
import useBiometric from '../Hooks/useBiometric';
import {toLowerCaseWordsOnly} from '../utilities';
import analytics from '@react-native-firebase/analytics';


const rnBiometrics = new ReactNativeBiometrics();

const Lock = props => {
  const toggleSoftLogout = useDanaStore(state => state.toggleSoftLogout);
  const resetStore = useDanaStore(state => state.resetStore);
  const setRejectReason = useDanaStore(state => state.setRejectReason);
  const saveUser = useDanaStore(state => state.saveUser);
  const saveToken = useDanaStore(state => state.saveToken);
  const heapIOUserId = useDanaStore(state => state.heapIOUserId);
  const getUserCurrentState = useDanaStore(state => state.getUserCurrentState);
  const setMissingData = useDanaStore(state => state.setMissingData);
  const user = useDanaStore(state => state.user);
  const token = useDanaStore(state => state.token);
  const updateOnboardingTimer = useDanaStore(
    state => state.updateOnboardingTimer,
  );
  const availableBiometrics = useBiometric();

  const [loading, setLoading] = useState(false);
  const [checkingBio, setCheckingBio] = useState(false);

  useEffect(() => {
    setI18nConfig();
  }, []);

  const biometricMatch = async () => {
    setCheckingBio(true);
    if (availableBiometrics.hasFaceId) {
      rnBiometrics
        .simplePrompt({promptMessage: 'FaceID'})
        .then(resultObject => {
          const {success} = resultObject;
          if (success) {
            getUserCurrentState();
            setCheckingBio(false);
            navigateWithNoHistory(props, CommonActions, 'Home');
          } else {
            setCheckingBio(false);
            showMessage({
              message: 'Authentication failed',
              type: 'danger',
            });
          }
        })
        .catch(() => {
          setCheckingBio(false);
          showMessage({
            message: 'biometrics failed',
            type: 'danger',
          });
        });
    } else if (availableBiometrics.hasTouchId) {
      rnBiometrics
        .simplePrompt({promptMessage: 'TouchID'})
        .then(resultObject => {
          const {success} = resultObject;
          if (success) {
            getUserCurrentState();
            setCheckingBio(false);
            navigateWithNoHistory(props, CommonActions, 'Home');
          } else {
            setCheckingBio(false);
            showMessage({
              message: 'Authentication failed',
              type: 'danger',
            });
          }
        })
        .catch(() => {
          setCheckingBio(false);
          showMessage({
            message: 'biometrics failed',
            type: 'danger',
          });
        });
    }
  };

  const onPressLearnMore = pin => {
    const obj = {
      country_code: user?.country_code,
      phone_number: user?.phone_number,
      pincode: pin,
      heapio_user_id: heapIOUserId,
    };
    setLoading(true);
    loginUser(obj)
      .then(res => {
        if ('access_token' in res) {
          const registeredUserObj = {
            ...res.user,
            token: res.access_token,
          };

          saveUser(registeredUserObj);
          saveToken(res.access_token);
          toggleSoftLogout();

          // Check if the user is company
          if (!res.user?.is_individual) {
            setRejectReason('individual');
            navigateWithNoHistory(props, CommonActions, 'Rejected');
            return;
          }

          // Check if the user is rejected
          if (res.user?.is_rejected) {
            setRejectReason('rejected');
            navigateWithNoHistory(props, CommonActions, 'Rejected');
            return;
          }

          getLastRegisterationStep()
            .then(async (registratioProgress) => {
              setMissingData(registratioProgress);
              if (
                !registratioProgress?.completed &&
                res?.user?.client?.type === 'temporary-customer'
              ) {
                navigateWithNoHistory(props, CommonActions, 'Register');
                setLoading(false);
              } else {
                CustomerIO.identify(registeredUserObj.external_user_id, {
                  email: registeredUserObj.email,
                  'last name': registeredUserObj.last_name,
                  'first name': registeredUserObj.first_name,
                });

                if (!res.user?.kyc_submitted_at) {
                  navigateWithNoHistory(props, CommonActions, 'Sumsub');
                  setLoading(false);
                  return;
                } else {
                  updateOnboardingTimer('reset');
                  await analytics().logEvent('login');
                  navigateWithNoHistory(props, CommonActions, 'Home');
                  setLoading(false);
                }
              }
            })
            .catch(error => {
              setLoading(false);
            });
        } else {
          setLoading(false);
          showMessage({
            type: 'danger',
            message: translate('something_went_wrong'),
          });
        }
      })
      .catch(error => {
        setLoading(false);
        showMessage({
          type: 'danger',
          message: translate(toLowerCaseWordsOnly(extractError(error))),
        });
      });
  };

  const logout = () => {
    Alert.alert(
      '',
      translate('logoutText'),
      [
        {
          text: translate('no'),
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: translate('yes'),
          onPress: () => {
            app_logout(token)
              .then(res => {
                try {
                  Intercom.logout();
                } catch (error) {}
                resetStore();
                navigateWithNoHistory(props, CommonActions, 'Tutorial');
              })
              .catch(error => {});
          },
        },
      ],
      {cancelable: false},
    );
  };

  return (
    <View style={styles.container}>
      <Offline />
      <StatusBar barStyle="dark-content" backgroundColor={'#fff'} />
      <PinCode
        titleText={translate('enter_pin_text')}
        subTitleText={translate('enter_pin_desc')}
        loadingText={translate('logging_in')}
        pinFinished={result => onPressLearnMore(result.join(''))}
        loading={loading}
        biometricMatch={null}
        checkingBio={checkingBio}
      />
      <View style={styles.logoutBtn}>
        <DanaButton
          theme="#282828"
          title={translate('disconnection')}
          onPress={() => logout()}
          testID="logOutButton"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    flex: 1,
    paddingVertical: Platform.OS === 'ios' ? 50 : 15,
    paddingHorizontal: pagePadding,
  },
  logoutBtn: {
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? 40 : 20,
    width: '100%',
    left: pagePadding,
  },
});

export default Lock;
