import React, {useEffect, useState, useMemo, useCallback} from 'react';
import {
  View,
  StatusBar,
  StyleSheet,
  ScrollView,
  Platform,
  KeyboardAvoidingView,
  TouchableOpacity,
  Image,
  SafeAreaView,
} from 'react-native';
import {borderRadius, titleFontSize, subtitleFontSize} from '../theme';
import {setI18nConfig, translate} from '../utilities/Translate';
import Icon from 'react-native-vector-icons/Feather';
import Heap from '@heap/react-native-heap';
import TextCp from '../components/TextCp';
import DanaButton from '../components/DanaButton';
import useDanaStore from '../app_state/store';
import {showMessage} from 'react-native-flash-message';
import Input from '../components/Input';
import SelectInput from '../components/SelectInput';
import {extractYupErrors} from '../utilities';
import {CustomerIO} from 'customerio-reactnative';
import Divider from '../components/Divider';
import {
  registerWithEmailSchema,
  jobWithOtherSchema,
  jobSchema,
  locationSchema,
  CountriesSchema,
  hearWitOtherSchema,
  hearWitEventSchema,
  hearSchema,
  hearWitRefSchema,
  registerSchema,
  locationWithPostalCodeSchema,
} from '../validations';
import GooglePlaceSearch from '../components/GooglePlaceSearch';
import {emailExists} from '../apis/auth';

const keyboardVerticalOffset = Platform.OS === 'ios' ? 40 : 0;

const special_HA = ['event', 'Other', 'referred'];

const special_HAMapping = {
  event: 'event',
  Other: 'other',
  referred: 'referralCode',
};

const MiniProfile = props => {

  const [page, setPage] = useState('Personal Information Setting Page');
  const temp_user = useDanaStore(state => state.temp_user);
  const onboardingTimer = useDanaStore(state => state.onboardingTimer);
  const onboardingTries = useDanaStore(state => state.onboardingTries);
  const updateOnboardingTimer = useDanaStore(
    state => state.updateOnboardingTimer,
  );

  const configuredActivities = useDanaStore(
    state => state.configuredActivities,
  );
  
  const setTemp = useDanaStore(state => state.setTemp);
  const countries = useDanaStore(state => state.countries);
  const [acceptedTerms, setAcceptTerms] = useState(false);
  const [userSelectedCountry, setUserSelectedCountry] = useState(null);
  const [step, setStep] = useState(0);
  const [error, setError] = useState(null);
  const [emailTaken, setEmailTaken] = useState(null);
  const [loading, setLoading] = useState(false);

  const postal_code_required = useMemo(() => {
    return (
      userSelectedCountry?.requires_post_code ||
      userSelectedCountry?.requires_post_code === 1 ||
      userSelectedCountry?.country_code[0] !== '2'
    );
  }, [userSelectedCountry]);

  const [user_object, setUserObject] = useState({
    firstname: '',
    lastname: '',
    email: '',
    monthly_revenue: '',
    referralCode: '',
    job: '',
    hear_about_us: '',
    userType: 'individual',
    reason_for_modification: 'Profile set up from the mobile app',
    other: '',
    city: '',
    country: '',
    address_line: '',
    activity_id: '',
    receiving_countries_ids: [],
    postal_code: '',
  });

  const menu = useMemo(
    () => [
      {
        name: translate('personal_info'),
        subtitle: translate('personal_info_desc'),
      },
      {
        name: translate('revenue_source'),
        subtitle: translate('revenue_source_desc'),
      },
      {
        name: translate('hear_about_us'),
        subtitle: translate('hear_about_us_desc'),
      },
      {
        name: translate('address_details'),
        subtitle: translate('address_details_desc'),
      },
      {
        name: translate('receiving_countries'),
        subtitle: translate('receiving_countries_desc'),
      },
    ],
    [],
  );

  useEffect(() => {
    setI18nConfig();
    findCountry();
  }, []);

  const gotToSetPin = () => {
    setTemp({...user_object});
    props.navigation.navigate('Pin');
  };

  const findCountry = () => {
    const country = countries.find(
      val => val.country_code === temp_user?.country_code,
    );
    setUserSelectedCountry(country);
  };

  const collectUserBioData = useCallback(async () => {
    if (!acceptedTerms) {
      showMessage({
        type: 'danger',
        message: translate('please_accept_terms'),
      });
      return;
    }

    setEmailTaken(null);

    const results = {
      lastname: user_object.lastname,
      firstname: user_object.firstname,
    };

    try {
      if (userSelectedCountry.preferred_notification_channel === 'mail') {
        results['email'] = user_object.email;
        await registerWithEmailSchema.validate(results, {abortEarly: false});
      } else {
        if (user_object.email) {
          results['email'] = user_object.email;
          await registerWithEmailSchema.validate(results, {abortEarly: false});
        } else {
          await registerSchema.validate(results, {abortEarly: false});
        }
      }

      if (user_object?.email) {
        setLoading(true);
        return emailExists({email: user_object.email})
          .then(res => {
            setLoading(false);
            if (res.exists) {
              setEmailTaken('email_taken');
              return;
            } else {
              navigateAfterEmailCheck(results);
            }
          })
          .catch(error => {
            setLoading(false);
          });
      } else {
        navigateAfterEmailCheck(results);
      }
    } catch (error) {
      setError(extractYupErrors(error));
    }
  }, [user_object, userSelectedCountry, acceptedTerms]);

  const collectRevenueData = useCallback(async () => {
    const results = {
      activity_id: user_object.activity_id,
      monthly_revenue: user_object.monthly_revenue,
    };
    try {
      if (user_object.activity_id === 15) {
        results['job'] = user_object.job;
        await jobWithOtherSchema.validate(results, {abortEarly: false});
      } else {
        await jobSchema.validate(results, {abortEarly: false});
      }
      setError(null);

      Heap.track('Revenue information set');
      setPage('Origin Setting Page');
      setStep(prev => prev + 1);
    } catch (error) {
      setError(extractYupErrors(error));
    }
  }, [user_object]);

  const navigateAfterEmailCheck = results => {
    setError(null);
    setTemp({...results});
    setUserObject(prev => ({...prev, ...results}));
    CustomerIO.setProfileAttributes({
      created_at: Math.floor(Date.now() / 1000),
      email: 'email' in results ? user_object.email : null,
      'user type': 'individual',
      mobile: true,
    });
    Heap.track('Identity Information Set');
    setPage('Revenue Setting Page');

    setStep(prev => prev + 1);
  };

  const collectAddress = useCallback(async () => {
    // if (locationValue) {
    let results = null;

    if (postal_code_required) {
      results = {
        address_line: user_object.address_line?.trim(),
        city: user_object.city?.trim(),
        postal_code: user_object.postal_code?.trim(),
      };
    } else {
      results = {
        address_line: user_object.address_line?.trim(),
        city: user_object.city?.trim(),
      };
    }

    try {
      if (
        userSelectedCountry.requires_post_code ||
        userSelectedCountry.requires_post_code === 1
      ) {
        await locationWithPostalCodeSchema.validate(results, {
          abortEarly: false,
        });
      } else {
        await locationSchema.validate(results, {abortEarly: false});
      }
      setError(null);
      Heap.track('Location information set');
      setPage('Receiving Countries Setting Page');
      setStep(prev => prev + 1);
    } catch (error) {
      setError(extractYupErrors(error));
    }
    // }
  }, [user_object]);

  const collectHearAboutUs = useCallback(async () => {
    try {
      if (user_object.hear_about_us === 'Other') {
        await hearWitOtherSchema.validate(
          {
            hear_about_us: user_object.hear_about_us,
            other: user_object.other?.trim(),
          },
          {abortEarly: false},
        );
      }

      if (user_object.hear_about_us === 'event') {
        await hearWitEventSchema.validate(
          {
            hear_about_us: user_object.hear_about_us,
            event: user_object.event?.trim(),
          },
          {abortEarly: false},
        );
      }

      if (user_object.hear_about_us === 'referred') {
        await hearWitRefSchema.validate(
          {
            hear_about_us: user_object.hear_about_us,
            referralCode: user_object.referralCode,
          },
          {abortEarly: false},
        );
      }

      if (!['referred', 'event', 'Other'].includes(user_object.hear_about_us)) {
        await hearSchema.validate(
          {
            hear_about_us: user_object.hear_about_us,
          },
          {abortEarly: false},
        );
      }

      const selectedItem = special_HA.includes(user_object.hear_about_us)
        ? `${special_HAMapping[user_object.hear_about_us]} - ${
            user_object[special_HAMapping[user_object.hear_about_us]]
          }`
        : user_object.hear_about_us;

      setError(null);
      Heap.addEventProperties({'Origin Type': selectedItem});
      Heap.track('Origin Set');
      setPage('Address Setting Page');
      setStep(prev => prev + 1);
    } catch (error) {
      setError(extractYupErrors(error));
    }
  }, [user_object]);

  const collectPreferences = useCallback(async () => {
    const results = {
      receivingCountries: user_object.receiving_countries_ids,
    };

    try {
      await CountriesSchema.validate(results, {abortEarly: false});
      setError(null);
      Heap.track('Receiving countries set');
      gotToSetPin();
    } catch (error) {
      setError(extractYupErrors(error));
    }
  }, [user_object]);

  const setHABValue = useCallback(value => {
    setUserObject(prev => ({...prev, hear_about_us: value}));
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      const counter = onboardingTimer + 1;
      updateOnboardingTimer('increment');
      if (counter === 120) {
        updateOnboardingTimer('reset');
        const currentDate = new Date();
        const dateString = currentDate.toLocaleString();
        CustomerIO.track('Registration Unfinished In 20 Mins', {
          userType: 'individual',
          channel: 'mobile',
          email: user_object?.email?.trim(),
          'onboarding start date': dateString,
          'phone number': `${temp_user?.country_code}${temp_user?.phone_number}`,
          'first name': user_object?.firstname?.trim(),
          'last name': user_object?.lastname?.trim(),
          page,
          tries: onboardingTries,
        });
      }
    }, 10000);

    return () => {
      clearInterval(timer);
    };
  }, [onboardingTimer]);

  const formatGoogleAddress = address => {
    const postal_code = address?.details?.address_components?.find(
      item =>
        item.types === 'postal_code' || item.types?.includes('postal_code'),
    );

    const city_details = address?.details?.address_components?.find(
      item => item.types === 'locality' || item.types?.includes('political'),
    );

    setUserObject(prev => ({...prev, city: city_details.short_name || ''}));

    setUserObject(prev => ({
      ...prev,
      address_line: address?.details?.name,
    }));

    setUserObject(prev => ({
      ...prev,
      postal_code: postal_code?.long_name || '',
    }));
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: '#fff',
      }}>
      <StatusBar barStyle="dark-content" backgroundColor={'#fff'} />
      <ScrollView
        style={{
          height: '100%',
        }}
        contentContainerStyle={{
          paddingHorizontal: 16,
        }}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps={true}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : ''}
          keyboardVerticalOffset={keyboardVerticalOffset}
          keyboardShouldPersistTaps={true}
          style={{
            flexGrow: 1,
          }}>
          <View style={{flexDirection: 'row'}}>
            <View style={{flex: 1, marginRight: 50}}>
              <TextCp textType="bold" style={styles.h1}>
                {menu[step].name}
              </TextCp>
              <TextCp style={styles.p}>{menu[step].subtitle}</TextCp>
            </View>

            <TouchableOpacity
              onPress={() => {
                if (step === 0) {
                  props.navigation.goBack();
                }
              }}>
              <Icon name="x-circle" size={30} color="#333" />
            </TouchableOpacity>
          </View>

          <Image source={require('../images/login.jpg')} style={styles.image} />

          {step === 0 && (
            <View style={{marginTop: 20}}>
              <Input
                label={translate('firstname')}
                value={user_object.firstname}
                onChange={text =>
                  setUserObject(prev => ({...prev, firstname: text}))
                }
                showLabel
                error={error?.firstname && translate(error?.firstname)}
              />
              <Divider height={8} />
              <Input
                label={translate('lastname')}
                value={user_object.lastname}
                onChange={text =>
                  setUserObject(prev => ({...prev, lastname: text}))
                }
                showLabel
                error={error?.lastname && translate(error?.lastname)}
              />
              <Divider height={8} />
              <Input
                label={`${translate('email')} ${
                  userSelectedCountry?.preferred_notification_channel === 'sms'
                    ? `[ ${translate('Optional')} ]`
                    : ''
                }`}
                value={user_object.email}
                onChange={text => {
                  setUserObject(prev => ({...prev, email: text}));
                }}
                showLabel
                error={
                  (error?.email && translate(error?.email)) ||
                  (emailTaken && translate(emailTaken))
                }
              />
              {/* <Divider height={8} /> */}
              <TouchableOpacity
                style={styles.terms}
                onPress={() => setAcceptTerms(prev => !prev)}>
                <View
                  style={{
                    ...styles.box,
                    borderColor: acceptedTerms ? '#037375' : '#f1f1f1',
                  }}>
                  <View
                    style={{
                      ...styles.innerBox,
                      backgroundColor: acceptedTerms ? '#037375' : '#f1f1f1',
                    }}
                  />
                </View>
                <View style={styles.termsText}>
                  <TextCp>{translate('terms_and_conditions')}</TextCp>
                </View>
              </TouchableOpacity>

              <DanaButton
                theme="#282828"
                title={translate('next')}
                onPress={collectUserBioData}
                loading={loading}
              />
            </View>
          )}

          {step === 1 && (
            <View style={{marginTop: 20}}>
              <TextCp>{translate('job_type')}</TextCp>
              <SelectInput
                onValueChange={text =>
                  setUserObject(prev => ({...prev, activity_id: text}))
                }
                items={configuredActivities.map(val => ({
                  value: val.id,
                  label: translate(val.slug),
                }))}
                placeholder={{
                  label: translate('job'),
                  value: translate('job'),
                }}
                value={user_object.activity_id}
                error={error?.activity_id && translate(error?.activity_id)}
              />
              <Divider height={8} />
              {user_object.activity_id === 15 && (
                <Input
                  label={translate('other_job_type')}
                  value={user_object.job}
                  onChange={text =>
                    setUserObject(prev => ({...prev, job: text}))
                  }
                  showLabel
                  error={error?.job && translate(error?.job)}
                />
              )}

              <Divider height={8} />
              <TextCp>{translate('revenue')}</TextCp>
              <SelectInput
                onValueChange={text =>
                  setUserObject(prev => ({...prev, monthly_revenue: text}))
                }
                items={[
                  {label: '0 - 1400€', value: '0 - 1400€'},
                  {label: '1400€ - 2500€', value: '1400€ - 2500€'},
                  {label: '2500€ - 4000€', value: '2500€ - 4000€'},
                  {label: '> 4000€', value: '> 4000€'},
                ]}
                placeholder={{
                  label: translate('revenue'),
                  value: translate('revenue'),
                }}
                value={user_object.monthly_revenue}
                error={
                  error?.monthly_revenue && translate(error?.monthly_revenue)
                }
              />
              <Divider height={16} />

              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <View style={{flex: 1}}>
                  <DanaButton
                    theme="#f1f1f1"
                    textColor="#222"
                    loadingColor={'#037375'}
                    title={translate('return')}
                    onPress={() => setStep(prev => prev - 1)}
                  />
                </View>
                <View style={{width: 20}} />
                <View style={{flex: 1}}>
                  <DanaButton
                    theme="#282828"
                    title={translate('next')}
                    onPress={collectRevenueData}
                  />
                </View>
              </View>
            </View>
          )}

          {step === 2 && (
            <View style={{marginTop: 20}}>
              <TextCp>{translate('hear_about_us')}</TextCp>
              <SelectInput
                onValueChange={text => {
                  setHABValue(text);
                }}
                items={[
                  {label: 'Linkedin', value: 'Linkedin'},
                  {label: 'Facebook', value: 'Facebook'},
                  {label: 'Google', value: 'Google'},
                  {label: 'Instagram', value: 'Instagram'},
                  {label: 'TikTok', value: 'TikTok'},
                  {
                    label: translate('referred_by_someone'),
                    value: 'referred',
                  },
                  {
                    label: translate('event'),
                    value: 'event',
                  },
                  {label: translate('Other'), value: 'Other'},
                ]}
                placeholder={{
                  label: translate('hear_about_us'),
                  value: '',
                }}
                value={user_object.hear_about_us}
                error={error?.hear_about_us && translate(error?.hear_about_us)}
              />

              <Divider height={6} />

              {user_object.hear_about_us === 'Other' && (
                <Input
                  label={translate('other')}
                  value={user_object.other}
                  onChange={text =>
                    setUserObject(prev => ({...prev, other: text}))
                  }
                  error={error?.other && translate(error?.other)}
                />
              )}

              {user_object.hear_about_us === 'event' && (
                <Input
                  label={translate('event_name')}
                  value={user_object.event}
                  onChange={text =>
                    setUserObject(prev => ({...prev, event: text}))
                  }
                  error={error?.event && translate(error?.event)}
                />
              )}

              {user_object.hear_about_us === 'referred' && (
                <Input
                  label={translate('referralCode')}
                  value={user_object.referralCode}
                  onChange={text =>
                    setUserObject(prev => ({...prev, referralCode: text}))
                  }
                  error={error?.referralCode && translate(error?.referralCode)}
                />
              )}

              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <View style={{flex: 1}}>
                  <DanaButton
                    theme="#f1f1f1"
                    textColor="#222"
                    loadingColor={'#037375'}
                    title={translate('return')}
                    onPress={() => setStep(prev => prev - 1)}
                  />
                </View>
                <View style={{width: 20}} />
                <View style={{flex: 1}}>
                  <DanaButton
                    theme="#282828"
                    title={translate('validate')}
                    onPress={collectHearAboutUs}
                  />
                </View>
              </View>
            </View>
          )}

          {step === 3 && (
            <View>
              <Divider height={8} />
              <GooglePlaceSearch
                label={translate('search_your_location')}
                onChange={val => formatGoogleAddress(val)}
              />

              {/* <Input
                label={translate('address_line')}
                value={user_object.address_line}
                onChange={text =>
                  setUserObject(prev => ({...prev, address_line: text}))
                }
                showLabel
                error={error?.address_line && translate(error?.address_line)}
              /> */}

              <Input
                label={translate('city')}
                value={user_object.city}
                onChange={text =>
                  setUserObject(prev => ({...prev, city: text}))
                }
                showLabel
                error={error?.city && translate(error?.city)}
              />

              <Input
                label={
                  postal_code_required
                    ? translate('postal_code')
                    : translate('postal_code_optional')
                }
                value={user_object.postal_code}
                onChange={text =>
                  setUserObject(prev => ({...prev, postal_code: text}))
                }
                showLabel
                error={error?.postal_code && translate(error?.postal_code)}
              />

              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <View style={{flex: 1}}>
                  <DanaButton
                    theme="#f1f1f1"
                    textColor="#222"
                    loadingColor={'#037375'}
                    title={translate('return')}
                    onPress={() => setStep(prev => prev - 1)}
                  />
                </View>
                <View style={{width: 20}} />
                <View style={{flex: 1}}>
                  <DanaButton
                    theme="#282828"
                    title={translate('validate')}
                    onPress={collectAddress}
                  />
                </View>
              </View>
            </View>
          )}

          {step === 4 && (
            <View>
              <Divider height={16} />
              <TextCp>{translate('receiving_countries')}</TextCp>
              <Divider height={8} />

              <SelectInput
                onValueChange={text => {
                  setUserObject(prev => ({
                    ...prev,
                    receiving_countries_ids: text,
                  }));
                }}
                items={countries.map(val => ({
                  label: val.name,
                  value: val.id,
                }))}
                isMultiple={true}
                placeholder={''}
                value={user_object.receiving_countries_ids}
                error={error && translate(error?.receivingCountries)}
              />

              <Divider height={20} />
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <View style={{flex: 1}}>
                  <DanaButton
                    theme="#f1f1f1"
                    textColor="#222"
                    loadingColor={'#037375'}
                    title={translate('return')}
                    onPress={() => setStep(prev => prev - 1)}
                  />
                </View>
                <View style={{width: 20}} />
                <View style={{flex: 1}}>
                  <DanaButton
                    theme="#282828"
                    title={translate('validate')}
                    onPress={collectPreferences}
                  />
                </View>
              </View>
            </View>
          )}
        </KeyboardAvoidingView>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  terms: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    marginVertical: 10,
  },
  box: {
    height: 30,
    width: 30,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
  },
  innerBox: {
    height: 16,
    width: 16,
    borderRadius: 15,
  },
  termsText: {
    flex: 1,
    marginHorizontal: 16,
  },
  container: {flex: 1},
  top: {flex: 2, paddingVertical: 20},
  bottom: {
    flex: 1,
    paddingVertical: 20,
    justifyContent: 'center',
    alignContent: 'center',
  },
  modalContainer: {
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: 'red',
  },
  title: {
    fontSize: 16,
    marginBottom: 10,
    textAlign: 'center',
  },
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    padding: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
  },
  flag: {
    height: 100,
    width: 100,
  },
  countryBox: {
    marginHorizontal: 16,
    marginVertical: 25,
  },
  image: {
    height: 150,
    width: '100%',
    borderRadius: borderRadius,
    marginBottom: 0,
  },
  h1: {
    fontSize: titleFontSize,
    marginBottom: 7,
    textTransform: 'capitalize',
  },
  p: {
    fontSize: subtitleFontSize,
    marginBottom: 11,
  },

  subtitleCode: {
    textAlign: 'center',
    color: '#444',
  },
  titleCode: {
    fontSize: 26,
    color: '#010101',
    textAlign: 'center',
    textTransform: 'capitalize',
    marginBottom: 10,
  },
  input: {
    width: '100%',
    height: 50,
    backgroundColor: 'rgba(0, 0, 0, 0.07)',
    borderRadius: 5,
    marginBottom: 17,
    paddingHorizontal: 17,
  },
});
export default MiniProfile;
