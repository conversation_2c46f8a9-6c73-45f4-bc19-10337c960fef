import {
  View,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import React, {useEffect, useReducer} from 'react';
import Modal from 'react-native-modal';
import {
  getMMAuthenticate,
  getMMProvider,
  getNewStatus,
} from '../apis/Transfers';
import {translate, setI18nConfig} from '../utilities/Translate';
import TextCp from '../components/TextCp';
import DanaButton from '../components/DanaButton';
import {ActivityIndicator} from 'react-native';
import Input from '../components/Input';
import SelectInput from '../components/SelectInput';
import Icon from 'react-native-vector-icons/Feather';
import useDanaStore from '../app_state/store';
import {navigateWithNoHistory} from '../utilities/ForgetHistroty';
import {CommonActions} from '@react-navigation/native';
import Heap from '@heap/react-native-heap';

const {width, height} = Dimensions.get('screen');

const possible_success_states = [
  'transaction.payment_collected',
  'payment_intent.succeeded',
  'transfer.succeeded',
];

const possible_failure_states = [
  'payment_intent.payment_failed',
  'payment.failed',
  'transfer.failed',
];

const initialState = {
  status: null,
  isPolling: false,
  providers: [],
  otpCode: null,
  verifyingOTP: null,
  selectedProvider: null,
  pollResponse: null,
  mmResponse: null,
  optSent: false,
  sending_code: false,
  title: '',
  subTitle: '',
  isOpen: false,
  requestingForOtp: false,
};

const reducer = (state, action) => {
  switch (action.type) {
    case 'SET_SENDING_STATUS':
      return {...state, sending_code: action.payload};
    case 'TOGGLE_MODAL':
      return {...state, isOpen: action.payload};
    case 'SET_PROVIDERS':
      return {...state, providers: action.payload};
    case 'SET_STATUS':
      return {...state, status: action.payload};
    case 'SET_OTP_CODE':
      return {...state, otpCode: action.payload};
    case 'SET_OTP_SENT':
      return {...state, optSent: action.payload};
    case 'SET_TITLE':
      return {...state, title: action.payload};
    case 'SET_SUB_TITLE':
      return {...state, subTitle: action.payload};
    case 'SET_MM_RESPONSE':
      return {...state, mmResponse: action.payload};
    case 'SET_PROVIDER':
      return {...state, selectedProvider: action.payload};
    case 'SET_OTP_VERIFY_STATUS':
      return {...state, verifyingOTP: action.payload};
    case 'SET_OTP_REQUEST_STATUS':
      return {...state, requestingForOtp: action.payload};
    case 'SET_POLLING_STATUS':
      return {...state, isPolling: action.payload};
    default:
      return state;
  }
};

const MobileMoney = props => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const token = useDanaStore(state => state.token);
  const {mmResponse} = props.route.params;

  useEffect(() => {
    setI18nConfig();
    dispatch({type: 'SET_MM_RESPONSE', payload: mmResponse});
    const mmProviders =
      mmResponse?.select_providers || mmResponse.data.available_providers;
    if (mmProviders) {
      dispatch({type: 'SET_PROVIDERS', payload: mmProviders});
    }
  }, [props.route]);

  const polling = () => {
    const {details, payment} = state.mmResponse;
    const payment_id = details?.id || payment?.id;
    const first_call_time = new Date().getTime();
    return new Promise((resolve, reject) => {
      (async function requestStatus() {
        dispatch({type: 'SET_POLLING_STATUS', payload: true});

        const updated_time = new Date().getTime();
        const difference = updated_time - first_call_time;
        const minutes = Math.round(difference / 60000);

        if (minutes > 2) {
          dispatch({type: 'SET_STATUS', payload: 'failed'});
          dispatch({type: 'SET_POLLING_STATUS', payload: false});
          dispatch({
            type: 'SET_SUB_TITLE',
            payload: 'The request timed out, please try again later.',
          });
          return;
        }

        const result = await getNewStatus(payment_id, token);

        const length = result.length;
        const currentStatusObject = length > 0 ? result[length - 1] : null;

        if (
          ['action_required', 'payment_required'].includes(
            currentStatusObject?.status,
          )
        ) {
          resolve(currentStatusObject);

          dispatch({type: 'SET_POLLING_STATUS', payload: false});

          if (currentStatusObject?.nextAction?.type == 'otp') {
            //show otp state if action_required and next step is otp
            dispatch({type: 'SET_OTP_SENT', payload: true});
          } else if (currentStatusObject?.nextAction?.type == 'ussd') {
            dispatch({type: 'SET_STATUS', payload: 'ussd'});
          }

          dispatch({
            type: 'SET_SUB_TITLE',
            payload: currentStatusObject?.details?.code,
          });
        } else if (
          possible_success_states.includes(currentStatusObject?.type)
        ) {
          dispatch({type: 'SET_STATUS', payload: 'success'});
          dispatch({type: 'SET_POLLING_STATUS', payload: false});
        } else if (
          possible_failure_states.includes(currentStatusObject?.type)
        ) {
          dispatch({type: 'SET_STATUS', payload: 'failed'});
          dispatch({
            type: 'SET_SUB_TITLE',
            payload: currentStatusObject?.details?.code,
          });
          dispatch({type: 'SET_POLLING_STATUS', payload: false});
        } else {
          setTimeout(() => {
            requestStatus();
          }, 1000);
        }
      })();
    });
  };

  const sendOTP = async () => {
    const {details, payment} = state.mmResponse;
    const payment_id = details?.id || payment?.id;
    const payload = {
      payment_id,
      selected_provider: state.selectedProvider.id,
    };
    try {
      dispatch({type: 'SET_OTP_REQUEST_STATUS', payload: true});
      const result = await getMMProvider(payload, token);

      if (
        result.status === 'action_required' &&
        result?.nextAction?.type == 'otp'
      ) {
        // show otp input
        dispatch({
          type: 'SET_SUB_TITLE',
          payload: result?.nextAction?.message,
        });

        dispatch({
          type: 'SET_OTP_SENT',
          payload: true,
        });
      }

      if (result.status === 'payment_required') {
        // show message telling user to enter the ping on the phone
        dispatch({
          type: 'SET_SUB_TITLE',
          payload: result?.nextAction?.message,
        });

        if (result.lastPaymentFailure) {
          dispatch({
            type: 'SET_STATUS',
            payload: 'failed',
          });
        } else {
          await polling();
        }
      }

      if (result.status === 'successful') {
        // shw the success screen
        dispatch({type: 'SET_STATUS', payload: 'success'});
      }

      if (result.status === 'processing') {
        // perform a polling for the new status
        const poll_response = await polling();
        dispatch({
          type: 'SET_SUB_TITLE',
          payload: poll_response.next_action.message,
        });
        setNextAction(poll_response);
      }

      dispatch({type: 'SET_OTP_REQUEST_STATUS', payload: false});
    } catch (error) {
      dispatch({type: 'SET_OTP_REQUEST_STATUS', payload: false});
    }
  };

  const goBackHome = (type = '') => {
    const {details, payment} = state.mmResponse;

    if (type === 'user_cancelled') {
      // handle cancel logic
      Heap.track('Transfer canceled', {...details});
    } else {
      Heap.track('Transfer recipient details confirmed', {...details});
    }
    navigateWithNoHistory(props, CommonActions, 'Home');
  };

  const setNextAction = poll_response => {
    if (poll_response.status === 'action_required') {
      if (poll_response?.next_action?.type?.toLowerCase() === 'otp') {
        dispatch({type: 'SET_OTP_SENT', payload: true});
      }

      if (poll_response.next_action.type.toLowerCase() === 'ussd') {
        dispatch({
          type: 'SET_SUB_TITLE',
          payload: poll_response?.next_action?.message,
        });
        dispatch({type: 'SET_STATUS', payload: 'ussd'});
      }
    }

    if (poll_response.status.toLowerCase() === 'payment_required') {
      dispatch({
        type: 'SET_SUB_TITLE',
        payload: poll_response?.next_action?.message,
      });
    }

    if (poll_response.status.toLowerCase() === 'success') {
      dispatch({type: 'SET_STATUS', payload: 'success'});
    }
  };

  const verifyOTP = async () => {
    const {details, payment} = state.mmResponse;
    const payment_id = details?.id || payment?.id;
    const payload = {
      payment_id,
      otp: state.otpCode.trim(),
    };
    dispatch({type: 'SET_OTP_VERIFY_STATUS', payload: true});

    const results = await getMMAuthenticate(payload, token);
    if (
      (results.status && results.status === 'successful') ||
      possible_success_states.includes(results.type)
    ) {
      dispatch({type: 'SET_STATUS', payload: 'success'});
      dispatch({type: 'SET_OTP_VERIFY_STATUS', payload: false});

      dispatch({
        type: 'UPDATE_FAILED_MESSAGE',
        payload: results.details.message,
      });
    } else if ('lastPaymentFailure' in results) {
      dispatch({type: 'SET_STATUS', payload: 'failed'});
      dispatch({type: 'SET_OTP_VERIFY_STATUS', payload: false});
      dispatch({
        type: 'SET_SUB_TITLE',
        payload: results.lastPaymentFailure.code,
      });
    }
  };

  return (
    <SafeAreaView style={styles.page}>
      <View style={styles.toolbar}>
        <TextCp></TextCp>
        <TouchableOpacity onPress={() => goBackHome('user_cancelled')}>
          <Icon name="x-circle" color="#282828" size={32} />
        </TouchableOpacity>
      </View>
      <View style={styles.content}>
        {state.status === 'success' && (
          <View style={styles.success}>
            <Icon name="check-circle" color="#55793E" size={100} />
            <TextCp style={styles.title}>{translate('Successfully')} </TextCp>
            <TextCp opacity={0.8} align="center">
              {translate('Successfully_text')}
            </TextCp>
            <TextCp />
            <DanaButton
              title={translate('return')}
              onPress={goBackHome}
              theme="#55793E"
            />
          </View>
        )}

        {state.status === 'failed' && (
          <View style={styles.failure}>
            <Icon name="x-circle" color="#BC4749" size={60} />
            <TextCp textType={'bold'} align="center" style={styles.title}>
              {translate('payment_Failed')}
            </TextCp>
            <TextCp opacity={0.8} align="center">
              {translate(state.subTitle.toLowerCase())}
            </TextCp>
            <TextCp />
            <DanaButton
              title={translate('return')}
              onPress={goBackHome}
              theme="#BC4749"
            />
          </View>
        )}

        {state.status === 'ussd' && (
          <View style={styles.ussd}>
            <Icon name="x-circle" color="#BC4749" size={60} />
            <TextCp textType={'bold'} align="center" style={styles.title}>
              {translate('USSD_Verification')}
            </TextCp>
            <TextCp opacity={0.8} align="center">
              {translate(state.subTitle.toLowerCase())}
            </TextCp>
          </View>
        )}

        {state.status === null && (
          <View style={styles.form}>
            {state.optSent ? (
              <View style={styles.section}>
                <TextCp textType={'bold'} style={styles.title} align="center">
                  {translate('OTP_Verification')}
                </TextCp>
                <TextCp opacity={0.8} align="center">
                  {translate(state.subTitle.toLowerCase())}
                </TextCp>
                <TextCp />

                <Input
                  onChange={text => {
                    dispatch({type: 'SET_OTP_CODE', payload: text});
                  }}
                  label={translate('enter_otp')}
                  value={state.otp}
                  backgroundColor={'rgba(0, 0, 0, 0.07)'}
                />
                <DanaButton
                  title={translate('verify_otp')}
                  onPress={verifyOTP}
                  loading={state.verifyingOTP}
                />
              </View>
            ) : (
              <View style={styles.section}>
                <TextCp textType={'bold'} style={styles.title} align="center">
                  {translate('select_provider')}
                </TextCp>
                <TextCp opacity={0.8} align="center">
                  {translate('select_provider_text')}
                </TextCp>
                <TextCp />

                <SelectInput
                  onValueChange={value => {
                    const provider = state.providers.find(
                      val => val.id === value,
                    );
                    dispatch({type: 'SET_PROVIDER', payload: provider});
                  }}
                  items={state.providers.map(val => ({
                    label: val.name,
                    value: val.id,
                  }))}
                  placeholder={{
                    label: translate('select_provider'),
                    value: translate('select_provider'),
                  }}
                  value={state.selectedProvider?.id}
                />
                <DanaButton
                  title={translate('send_OTP')}
                  onPress={sendOTP}
                  loading={state.sending_code}
                />
              </View>
            )}
          </View>
        )}
      </View>
      <Modal isVisible={state.isPolling}>
        <View style={styles.pollingPopUp}>
          <ActivityIndicator size="large" color="#037375" />
          <TextCp align="center" style={{marginVertical: 14, fontSize: 16}}>
            {translate('polling_processing_text')}
          </TextCp>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  page: {
    flex: 1,
    backgroundColor: '#f1f1f1',
  },
  toolbar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginVertical: 8,
  },
  content: {
    paddingHorizontal: 16,
    justifyContent: 'center',
    // alignItems: 'center',
    flex: 1,
  },
  pollingPopUp: {
    width: width - 64,
    height: width - 64,
    backgroundColor: '#f1f1f1',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: width / 2,
    borderRadius: 8,
    left: 16,
  },
  success: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#E9F1E4',
    paddingVertical: 48,
    borderRadius: 8,
    paddingHorizontal: 16,
  },
  failure: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fbdbdd',
    paddingVertical: 48,
    borderRadius: 8,
    paddingHorizontal: 16,
  },
  ussd: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 48,
    paddingHorizontal: 16,
  },
  section: {
    justifyContent: 'center',
    // alignItems: 'center',
    paddingHorizontal: 16,
  },
  form: {
    marginVertical: 16,
  },
  title: {
    fontSize: 17,
    marginVertical: 8,
  },
  iconSize: {
    fontSize: 50,
  },
});

export default MobileMoney;
