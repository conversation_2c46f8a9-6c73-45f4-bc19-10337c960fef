import React, {useEffect} from 'react';
import {View, StatusBar} from 'react-native';
import {setI18nConfig, translate} from '../utilities/Translate';
import TextCp from '../components/TextCp';
import {gs} from '../theme';
import Icon from 'react-native-vector-icons/Feather';

const NotAllowed = () => {
  useEffect(() => {
    setI18nConfig();
  }, []);

  return (
    <View
      style={[
        gs.noScrollContainer,
        {
          backgroundColor: '#000',
          justifyContent: 'center',
          alignItems: 'center',
          padding: 20,
        },
      ]}>
      <StatusBar barStyle="light-content" backgroundColor={'#000'} />
      <Icon name="alert-circle" size={60} style={{color: '#fff'}} />
      <TextCp
        textType="bold"
        style={{fontSize: 30, color: '#fff', textTransform: 'capitalize'}}>
        {translate('not_allowed_title')}
      </TextCp>
      <TextCp style={{fontSize: 18, color: '#fff', textAlign: 'center'}}>
        {translate('not_allowed_text')}
      </TextCp>
      <TextCp
        style={{
          fontSize: 12,
          color: '#fff',
          textAlign: 'center',
          marginTop: 50,
        }}>
        {translate('not_allowed_footer')}
      </TextCp>
    </View>
  );
};

export default NotAllowed;
