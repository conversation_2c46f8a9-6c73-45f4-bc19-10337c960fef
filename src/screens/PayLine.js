import React, {useRef, useEffect, useState} from 'react';
import {StyleSheet, View, Platform, StatusBar} from 'react-native';
import TextCp from '../components/TextCp';
import DanaButton from '../components/DanaButton';
import {translate, setI18nConfig} from '../utilities/Translate';
import {navigateWithNoHistory} from '../utilities/ForgetHistroty';
import {CommonActions} from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Feather';
import {pagePadding, subtitleFontSize, titleFontSize} from '../theme';
import Toolbar from '../components/Toolbar';

const PayLine = (props) => {
  const webView = useRef();
  const [finished, setFinished] = useState(false);
  const {token, connect_url} = props.route.params;

  useEffect(() => {
    setI18nConfig();
  }, []);

  const navigatorChanged = (event) => {
    const dpUrl = `https://test.danapay.io/payment/status?token=${token}`;
    const dpUrlOption = `https://test.danapay.io/payment/status?token=${token}&paymentEndpoint=1`;
    if (event.url === dpUrl || event.url === dpUrlOption) {
      setFinished(true);
    }
  };

  const goHome = () => {
    navigateWithNoHistory(props, CommonActions, 'Home');
  };

  const goBack = () => {
    props.navigation.goBack();
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={'#fff'} />
      {!finished ? <Toolbar goBack={goBack} /> : <View style={{height: 56}} />}
      <View style={styles.success}>
        <TextCp textType="semiBold" style={styles.title}>
          {translate('payment_success_title')}
        </TextCp>
        {finished ? (
          <View
            style={{
              flex: 1,
            }}>
            <View
              style={{
                flex: 5,
                flexDirection: 'column',
                justifyContent: 'space-evenly',
                alignItems: 'center',
              }}>
              <View style={[styles.circle, {backgroundColor: '#0698fc'}]}>
                <Icon name="check" size={50} color="#fff" />
              </View>
              <TextCp textType="semiBold" style={styles.subTitle}>
                {translate('payment_success_message')}
              </TextCp>
            </View>

            <View
              style={{
                flex: 1,
                alignItems: 'center',
                flexDirection: 'column',
              }}>
              <DanaButton
                theme="#282828"
                title={translate('home')}
                onPress={() => goHome()}
              />
            </View>
          </View>
        ) : (
          <View />
          // <WebView
          //   ref={webView}
          //   style={styles.webview}
          //   source={{
          //     uri: `https://amazing-goldberg-60d727.netlify.app/dev.html?token=${token}`,
          //   }}
          //   onNavigationStateChange={(event) => navigatorChanged(event)}
          //   scalesPageToFit={true}
          //   startInLoadingState={true}
          // />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    paddingHorizontal: pagePadding,
  },
  webview: {
    flex: 1,
    width: '100%',
    backgroundColor: '#eee',
  },
  success: {
    flex: 1,
    justifyContent: 'center',
  },
  circle: {
    height: 120,
    width: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: titleFontSize,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  title1: {
    fontSize: 20,
  },
  subTitle: {
    fontSize: subtitleFontSize,
    textAlign: 'center',
  },
  header: {
    paddingHorizontal: pagePadding,
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginTop: Platform.OS === 'ios' ? 10 : 0,
  },
});

export default PayLine;
