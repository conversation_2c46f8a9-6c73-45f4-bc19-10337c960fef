import React, {useState, useEffect, useReducer, useMemo} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  KeyboardAvoidingView,
  StatusBar,
  TouchableWithoutFeedback,
  SafeAreaView,
  Keyboard,
  ActivityIndicator,
  Switch,
} from 'react-native';
import {CustomerIO} from 'customerio-reactnative';

import Icon from 'react-native-vector-icons/Feather';
import Modal from 'react-native-modal';
import {translate, setI18nConfig} from '../utilities/Translate';
import {titleFontSize, scaleRatio, colors} from '../theme';
import TextCp from '../components/TextCp';
import DanaButton from '../components/DanaButton';
import {ScrollView} from 'react-native-gesture-handler';
import Toolbar from '../components/Toolbar';
import {calculateFees} from '../apis/Transfers';
import useDanaStore from '../app_state/store';
import CashInput from '../components/CashInput';
import {showMessage} from 'react-native-flash-message';
import Input from '../components/Input';
import SelectInput from '../components/SelectInput';
import CashLayout from '../components/CashLayout';

const initial_state = {
  payMethod: '',
  euros: '0',
  euros_error: false,
  cfa: '0',
  cfa_error: false,
  payMethodError: false,
  cash_in_methods: [],
  reason: '',
  calculating: false,
};

const reducer = (state = initial_state, action) => {
  switch (action.type) {
    case 'SET_PAY_METHOD':
      return {...state, payMethod: action.payload};
    case 'SET_EURO':
      return {...state, euros: action.payload};
    case 'SET_CFA':
      return {...state, cfa: action.payload};
    case 'SET_REASON':
      return {...state, reason: action.payload};
    case 'SET_EURO_ERROR':
      return {...state, euros_error: action.payload};
    case 'SET_CFA_ERROR':
      return {...state, cfa_error: action.payload};
    case 'SET_PAY_METHOD_ERROR':
      return {...state, payMethodError: action.payload};
    case 'SET_CASH_IN_METHODS':
      return {...state, cash_in_methods: action.payload};
    case 'CALCULATING_FEES':
      return {...state, calculating: action.payload};
    default:
      break;
  }
};

const keyboardHeight = Keyboard.keyboardHeight;

const keyboardVerticalOffset = Platform.OS === 'ios' ? 40 : 0;

const PaymentInfo = props => {
  const transferTimer = useDanaStore(state => state.transferTimer);
  const updateTransferTimer = useDanaStore(state => state.updateTransferTimer);
  const transfer = useDanaStore(state => state.transfer);
  const loggedInUser = useDanaStore(state => state.user);
  const saveTransfer = useDanaStore(state => state.saveTransfer);
  const beneficiary = useDanaStore(state => state.beneficiary);
  const sendingCountry = useDanaStore(state => state.sendingCountry);
  const transferTries = useDanaStore(state => state.transferTries);
  const activeOperationPage = useDanaStore(state => state.activeOperationPage);
  const setActiveOperationPage = useDanaStore(
    state => state.setActiveOperationPage,
  );

  const fetchSelectedCountryProviders = useDanaStore(
    state => state.fetchSelectedCountryProviders,
  );
  const selected_country_providers = useDanaStore(
    state => state.selected_country_providers,
  );
  const selected_country_cash_out_Methods = useDanaStore(
    state => state.selected_country_cash_out_Methods,
  );

  // creating an array
  const items = selected_country_cash_out_Methods
    ? selected_country_cash_out_Methods instanceof Array
      ? selected_country_cash_out_Methods
      : [selected_country_cash_out_Methods]
    : [];

  const user = useDanaStore(state => state.user);
  const fees = useDanaStore(state => state.fees);
  const saveFees = useDanaStore(state => state.saveFees);
  const [state, dispatch] = useReducer(reducer, initial_state);
  const [feesError, setFeesError] = useState(null);
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  const [showModal, setShowModal] = useState(false);

  const setAllPaymentTypes = () => {
    const cash_in_methods = sendingCountry.cash_in_methods;
    dispatch({type: 'SET_CASH_IN_METHODS', payload: cash_in_methods});
  };

  const goBack = () => {
    props.navigation.goBack();
  };

  const setPaymentMethod = async () => {
    if (
      transfer?.amount_in_euro.length === 0 ||
      transfer?.amount_in_cfa.length === 0 ||
      transfer?.cash_in_method.length === 0 ||
      transfer?.reason.length === 0 ||
      (transfer.is_direct &&
        (transfer.w_provider === '' ||
          transfer.w_provider === '' ||
          transfer.w_phone === '' ||
          transfer.w_method === ''))
    ) {
      showMessage({
        type: 'danger',
        message: 'All fields are required',
      });
      return;
    }
    if (transfer?.cash_in_method_name.toLowerCase() === 'balance') {
      const balance = parseFloat(user?.client?.euro_balance);
      const sending = parseFloat(transfer?.amount_in_euro);
      // check if the balance is enough...
      if (balance < sending) {
        showMessage({
          message: "You don't have enough balance.",
          type: 'danger',
        });
        return;
      }
    }

    if (
      beneficiary.is_individual ||
      (!beneficiary.is_individual && beneficiary?.company?.activated_at)
    ) {
      props.navigation.navigate('WithdrawalOptions');
    } else {
      setShowModal(false);
      setActiveOperationPage('Transfer Summary Page');
      props.navigation.navigate('Summary');
    }

  };

  const calculate_fees = async () => {
    setFeesError(null);
    try {
      dispatch({type: 'CALCULATING_FEES', payload: true});
      const body = {
        euro_amount: transfer?.amount_in_euro,
        sending_country_id: sendingCountry.id,
        receiving_country_id: beneficiary?.country?.receiving_country?.id,
        cashin_method_id: transfer?.cash_in_method,
      };
      const result = await calculateFees(body, user?.id);
      const rateObject = result?.fee_calculation.find(val => val.exchange_rate);
      saveFees({...rateObject, fee: result.fee.toFixed(2)});
      dispatch({type: 'CALCULATING_FEES', payload: false});
      setFeesError(null);
    } catch (error) {
      setFeesError(null);
      dispatch({type: 'CALCULATING_FEES', payload: false});
      if (error?.response?.status !== 422) {
        setFeesError(error?.response?.data?.message);
      }
    }
  };

  const handleExchange = (cash, toCurrency) => {
    const exchange_rate = 655.957;

    if (cash.length === 0) return '';
    if (toCurrency.toLowerCase() === 'eur') {
      return fees?.exchange_rate
        ? (parseFloat(cash) / fees?.exchange_rate).toFixed(2).toString()
        : (parseFloat(cash) / exchange_rate).toFixed(2).toString();
    } else {
      return fees?.exchange_rate
        ? (parseFloat(cash) * fees?.exchange_rate).toFixed(2).toString()
        : (parseFloat(cash) * exchange_rate).toFixed(2).toString();
    }
  };

  const checkLimits = useMemo(() => {
    const selectedMethod = sendingCountry.cash_in_methods.find(
      val => val?.cash_in_method?.id === transfer?.cash_in_method,
    );

    if (transfer.amount_in_euro > selectedMethod?.cash_in_method?.max_amount) {
      return 'amountGrater';
    }

    if (transfer.amount_in_euro < selectedMethod?.cash_in_method?.min_amount) {
      return 'amountLower';
    }

    return null;
  }, [sendingCountry, transfer]);

  useEffect(() => {
    setI18nConfig();
    setAllPaymentTypes();
  }, []);

  useEffect(() => {
    calculate_fees();
  }, [
    transfer?.amount_in_cfa,
    transfer?.amount_in_euro,
    transfer?.cash_in_method,
  ]);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      },
    );

    return () => {
      setKeyboardVisible(false);
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  useEffect(() => {
    fetchSelectedCountryProviders(transfer?.w_country?.receiving_country?.code);
  }, []);

  useEffect(() => {
    let timer;
    timer = setInterval(() => {
      updateTransferTimer('increment');
      if (transferTimer === 60) {
        const currentDate = new Date();
        const dateString = currentDate.toLocaleString();
        CustomerIO.track('Transfer Unfinished In 10 Mins', {
          userType: 'individual',
          operationTYpe: 'transfer',
          channel: 'mobile',
          email: loggedInUser?.email,
          'transfer start date': dateString,
          'phone number': `${loggedInUser?.country_code}${loggedInUser?.phone_number}`,
          'first name': loggedInUser?.firstname,
          'last name': loggedInUser?.lastname,
          page: activeOperationPage,
          'recipient first name ': beneficiary?.firstName,
          'recipient last name': beneficiary?.lastName,
          'recipient phone ': beneficiary.phone_number,
          'recipient email ': beneficiary.email,
          'transfer method': transfer?.cash_in_method_name,
          tries: transferTries,
        });
        CustomerIO.setProfileAttributes({
          'Transfer Done': false,
        });
        updateTransferTimer('reset');
      }
    }, 10000);

    return () => {
      clearInterval(timer);
    };
  }, [transferTimer]);

  return (
    <SafeAreaView style={styles.page}>
      <StatusBar barStyle="dark-content" backgroundColor={'#f1f1f1'} />
      <ScrollView showsVerticalScrollIndicator={false}>
        <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
          <KeyboardAvoidingView
            behavior="position"
            keyboardVerticalOffset={keyboardVerticalOffset}
            style={{flex: 1}}>
            <View style={{flexGrow: 1}}>
              <View
                style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                <View>
                  <TextCp />
                  <TextCp textType="bold" style={{fontSize: 22}}>
                    {translate('pay_info')}
                  </TextCp>
                  <TextCp
                    style={{
                      color: state.payMethodError ? 'red' : '#282828',
                    }}>
                    {translate('choose_pay_method')}
                  </TextCp>
                </View>

                <Toolbar goBack={goBack} />
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginVertical: 16,
                }}>
                <TextCp
                  style={{
                    color: state.payMethodError ? 'red' : '#282828',
                  }}>
                  {translate('yob')}
                </TextCp>

                <CashLayout
                  value={user?.client?.euro_balance}
                  color="#037375"
                />
              </View>

              <Input
                onChange={text => {
                  saveTransfer({reason: text});
                }}
                label={translate('enter_reason')}
                value={
                  transfer?.reason.trim().length > 0 ? transfer.reason : ''
                }
                style={{
                  borderWidth: 1,
                  borderColor: state.reason ? '#BC4749' : 'transparent',
                  marginBottom: 10,
                }}
                backgroundColor={'rgba(0, 0, 0, 0.07)'}
                testID="paymentReasonInput"
              />

              <CashInput
                value={transfer?.amount_in_euro}
                onChange={amount => {
                  saveTransfer({
                    amount_in_euro: amount,
                    currency: 'EUR',
                    amount_in_cfa: handleExchange(amount, 'cfa'),
                  });
                }}
                placeholder={translate('calcMoneyInEUR')}
                style={{
                  borderWidth: 1,
                  borderColor: state.eurosError ? '#BC4749' : 'transparent',
                }}
                currency="EUR"
              />

              {feesError && (
                <View>
                  <TextCp color={colors.red} style={{fontSize: 12}}>
                    {translate(feesError.toLowerCase().split(' ').join('_'))}
                  </TextCp>
                </View>
              )}
              {!feesError && checkLimits && (
                <View>
                  <TextCp style={{fontSize: 12}} color={colors.red}>
                    {translate(checkLimits)}
                  </TextCp>
                </View>
              )}

              <View style={styles.calc}>
                <View style={styles.cash}>
                  <View style={styles.row}>
                    <Text style={styles.bullet}>{'\u2B24'}</Text>
                    <TextCp style={styles.name}>
                      {translate('calcExchangeRate')}
                    </TextCp>
                  </View>

                  <TextCp textType="bold" style={styles.conversion}>
                    {`1 € = ${fees.exchange_rate || 665.967} CFA`}
                  </TextCp>
                </View>

                <View style={[styles.cash]}>
                  <View style={styles.row}>
                    <Text style={styles.bullet}>{'\u2B24'}</Text>
                    <TextCp style={styles.name}>{translate('fees')}</TextCp>
                  </View>

                  {state.calculating ? (
                    <ActivityIndicator color="#4a4a4a" size={'small'} />
                  ) : (
                    <CashLayout value={fees?.fee} color="#666" fontSize={14} />
                  )}
                </View>

                <View style={[styles.cash]}>
                  <View style={styles.row}>
                    <Text style={styles.bullet}>{'\u2B24'}</Text>
                    <TextCp style={styles.name}>
                      {translate('cash_in_method')}
                    </TextCp>
                  </View>

                  <TextCp>
                    {translate(
                      transfer.cash_in_method_name
                        .toLowerCase()
                        .split(' ')
                        .join('_'),
                    )}
                  </TextCp>
                </View>
              </View>

              <CashInput
                value={transfer?.amount_in_cfa}
                onChange={amount => {
                  saveTransfer({
                    amount_in_cfa: amount,
                    currency: 'XOF',
                    amount_in_euro: handleExchange(amount, 'eur'),
                  });
                }}
                placeholder={translate('calcMoneyInCFA')}
                style={{
                  borderWidth: 1,
                  borderColor: state.cfa_error ? '#BC4749' : 'transparent',
                }}
                currency="CFA"
              />

              {items.length > 0 && items[0].name === 'mobile money' && (
                <View
                  style={[
                    styles.row,
                    {
                      marginVertical: 16,
                      borderWidth: 0.5,
                      borderColor: transfer.is_direct ? '#037375' : '#4a4a4a',
                      paddingHorizontal: 16,
                      paddingVertical: Platform.OS === 'ios' ? 32 : 16,
                      borderRadius: 8,
                    },
                  ]}>
                  <Switch
                    trackColor={{false: '#767577', true: '#03737566'}}
                    thumbColor={transfer.is_direct ? '#037375' : '#f4f3f4'}
                    ios_backgroundColor="#3e3e3e"
                    onValueChange={() => {
                      if (transfer.is_direct) {
                        saveTransfer({is_direct: false});
                        setShowModal(false);
                      } else {
                        saveTransfer({is_direct: true});
                        setShowModal(true);
                      }
                    }}
                    value={transfer.is_direct}
                  />
                  <View style={{flex: 1, paddingHorizontal: 16}}>
                    <TextCp
                      color={transfer.is_direct ? '#037375' : '#767577'}
                      textType={transfer.is_direct ? 'bold' : 'regular'}>
                      {translate('withdrawal_notice')}
                    </TextCp>
                  </View>
                </View>
              )}

              <View
                style={{
                  flexDirection: 'row',
                  paddingVertical: 6,
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                <TextCp style={{color: 'rgba(0,0,0,.5)'}} fontSize={17}>
                  {translate('totalToPay')}
                </TextCp>

                <CashLayout
                  value={+fees?.fee + +transfer?.amount_in_euro}
                  color={'#037375'}
                  fontSize={17}
                />
              </View>
            </View>
          </KeyboardAvoidingView>
        </TouchableWithoutFeedback>
      </ScrollView>
      <View style={styles.bottom}>
        <View style={{flex: 2, paddingRight: 10, paddingLeft: 1}}>
          <DanaButton
            title={translate('return')}
            onPress={goBack}
            theme="#fff"
            textColor="#282828"
          />
        </View>
        {/* <View style={{flex: 1, paddingRight: 16}} /> */}
        <View style={{flex: 2, paddingLeft: 10}}>
          <DanaButton
            title={translate('next')}
            onPress={setPaymentMethod}
            theme="#282828"
            testId="moveToSummaryScreenBtn"
          />
        </View>
      </View>
      <Modal
        isVisible={showModal}
        animationInTiming={200}
        animationOutTiming={200}
        animationOut="fadeOut"
        animationIn="fadeIn"
        statusBarTranslucent={true}
        style={{backgroundColor: 'rgba(154,155,159, .3)', margin: 0}}>
        <View
          style={{
            ...styles.modal,
            bottom: isKeyboardVisible ? keyboardHeight : 0,
          }}>
          <View style={styles.innerContainer}>
            <View style={{justifyContent: 'flex-end', alignItems: 'flex-end'}}>
              <TouchableOpacity
                onPress={() => {
                  saveTransfer({is_direct: !transfer.is_direct});
                  setShowModal(false);
                }}>
                <Icon
                  name="x-circle"
                  style={{fontSize: 35 * scaleRatio}}></Icon>
              </TouchableOpacity>
            </View>
            <View style={{justifyContent: 'center'}}>
              <TextCp
                textType="bold"
                style={{
                  fontSize: 22 * scaleRatio,
                  textAlign: 'center',
                }}>
                {translate('Select_a_withdraw_mode')}
              </TextCp>
            </View>
            <View
              style={{
                paddingVertical: 30,
              }}>
              <KeyboardAvoidingView behavior="position" enabled>
                <SelectInput
                  onValueChange={value => {
                    saveTransfer({w_method: value});
                  }}
                  items={items.map(val => ({
                    label: val.name,
                    value: val.name,
                  }))}
                  placeholder={{
                    label: translate('withdraw_method'),
                    value: translate('withdraw_method'),
                  }}
                  value={transfer.w_method}
                />

                <SelectInput
                  onValueChange={value => {
                    saveTransfer({w_provider: value});
                  }}
                  items={selected_country_providers?.map(val => ({
                    label: val.name,
                    value: val.name,
                  }))}
                  placeholder={{
                    label: translate('operator'),
                    value: translate('operator'),
                  }}
                  value={transfer.w_provider}
                />

                <View style={{flexDirection: 'row'}}>
                  <View style={{width: 70, marginRight: 8}}>
                    <Input
                      onChange={text => null}
                      placeholder={translate('c')}
                      value={`+${transfer.w_country?.receiving_country?.country_code}`}
                      backgroundColor={'rgba(0, 0, 0, 0.07)'}
                    />
                  </View>
                  <View style={{flex: 1}}>
                    <Input
                      onChange={text => saveTransfer({w_phone: text})}
                      placeholder={translate('PhoneNumber')}
                      value={transfer.w_phone}
                      backgroundColor={'rgba(0, 0, 0, 0.07)'}
                    />
                  </View>
                </View>

                <View style={styles.bottom}>
                  <View style={{flex: 2, paddingRight: 10, paddingLeft: 1}}>
                    <DanaButton
                      title={translate('cancel')}
                      onPress={() => {
                        saveTransfer({is_direct: !transfer.is_direct});
                        setShowModal(false);
                      }}
                      theme="#fff"
                      textColor="#282828"
                    />
                  </View>
                  {/* <View style={{flex: 1, paddingRight: 10}} /> */}
                  <View style={{flex: 2, paddingLeft: 10}}>
                    <DanaButton
                      title={translate('next')}
                      onPress={setPaymentMethod}
                      theme="#282828"
                    />
                  </View>
                </View>
                {isKeyboardVisible ? <View style={{height: 140}} /> : null}
              </KeyboardAvoidingView>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  page: {
    marginHorizontal: 16,
    backgroundColor: '#f1f1f1',
    flex: 1,
  },
  h1: {
    fontSize: titleFontSize,
    marginBottom: 17 * scaleRatio,
  },
  p: {
    fontSize: 15 * scaleRatio,
    marginBottom: 6 * scaleRatio,
  },
  input: {
    width: '100%',
    height: 56,
    backgroundColor: 'rgba(0, 0, 0, 0.07)',
    borderRadius: 8,
    marginVertical: 5.5,
    paddingHorizontal: 17 * scaleRatio,
  },
  bottom: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    paddingVertical: 16,
  },

  money: {
    fontSize: 25,
    color: '#000',
    marginBottom: 5,
  },

  payOptionsList: {
    marginBottom: 6,
  },
  amount: {
    color: 'rgba(0, 0, 0, 0.51)',
    fontSize: 12,
  },
  error: {
    fontSize: 12,
    color: 'red',
  },
  cash: {
    marginVertical: 16 * scaleRatio,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  bullet: {
    color: '#dadcdc',
    marginRight: 15,
  },
  name: {
    height: 19,
    color: 'rgba(0, 0, 0, 0.5)',
    fontSize: 16 * scaleRatio,
    marginRight: 10,
  },
  conversion: {
    color: 'rgba(0, 0, 0, 0.5)',
    fontSize: 16 * scaleRatio,
  },
  calc: {
    // marginLeft: 15,
    marginVertical: 16 * scaleRatio,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    paddingHorizontal: 26,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16 * scaleRatio,
  },
  innerContainerTra: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16,
  },
  modalTitle: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginVertical: 10,
    alignItems: 'center',
  },
  pay_img: {
    height: 40,
    width: 40,
    marginBottom: 8,
    position: 'absolute',
    right: 8,
    top: 8,
  },
});

export default PaymentInfo;
