import React, {useEffect, useState} from 'react';
import {
  View,
  StatusBar,
  TouchableOpacity,
  StyleSheet,
  Alert,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import Config from 'react-native-config';
import {scaleRatio} from '../theme';
import TextCp from '../components/TextCp';
import {translate, setI18nConfig} from '../utilities/Translate';
import Icon from 'react-native-vector-icons/Feather';
import DanaButton from '../components/DanaButton';
import Intercom from '@intercom/intercom-react-native';
import {navigateWithNoHistory} from '../utilities/ForgetHistroty';
import {CommonActions} from '@react-navigation/native';
import useDanaStore from '../app_state/store';
import Input from '../components/Input';
import SelectInput from '../components/SelectInput';
import {app_logout} from '../apis/auth';

var pjson = require('../../package.json');

const Profile = props => {
  const user = useDanaStore(state => state.user);
  const token = useDanaStore(state => state.token);
  const resetStore = useDanaStore(state => state.resetStore);
  const getUserCurrentState = useDanaStore(state => state.getUserCurrentState);
  const state_countries = useDanaStore(state => state.countries);

  const [userObject, setUserObject] = useState({
    first_name: user?.first_name,
    last_name: user?.last_name,
    email: user?.email,
    phone: user?.phone_number,
  });

  const [country, setCountry] = useState(
    state_countries.find(val => val.name === user?.country),
  );

  useEffect(() => {
    setI18nConfig();
    getUserCurrentState();
  }, []);

  const logout = () => {
    Alert.alert(
      '',
      translate('logoutText'),
      [
        {
          text: translate('no'),
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: translate('yes'),
          onPress: () => {
            app_logout(token)
              .then(res => {
                try {
                  Intercom.logout();
                } catch (error) {}
                resetStore();
                navigateWithNoHistory(props, CommonActions, 'Tutorial');
              })
              .catch(error => {});
          },
        },
      ],
      {cancelable: false},
    );
  };

  return (
    <SafeAreaView style={styles.page}>
      <StatusBar barStyle="dark-content" backgroundColor={'#fff'} />
      <View style={styles.toolbar}>
        <TextCp textType="bold" style={styles.title}>
          {translate('personal_information')}
        </TextCp>
        <TouchableOpacity
          style={styles.iconButton}
          onPress={() => props.navigation.goBack()}>
          <Icon name="x-circle" size={26} style={styles.icon} color="#222" />
        </TouchableOpacity>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.form}>
          <Input
            onChange={text =>
              setUserObject(prev => ({...prev, last_name: text}))
            }
            label={translate('lastname')}
            value={userObject.last_name}
            backgroundColor={'#fff'}
          />
          <View style={{height: 10}} />
          <Input
            onChange={text =>
              setUserObject(prev => ({...prev, first_name: text}))
            }
            label={translate('firstname')}
            value={userObject.first_name}
            backgroundColor={'#fff'}
          />

          <SelectInput
            onValueChange={value => {
              const selected_country = state_countries.find(
                val => val.id === value,
              );
              setCountry(selected_country);
            }}
            items={state_countries.map(val => {
              return {
                label: val.name,
                value: val.id,
              };
            })}
            placeholder={{
              label: translate('country'),
              value: translate('country'),
            }}
            value={country?.id}
          />

          <View>
            <Input
              label={translate('number')}
              value={userObject?.phone}
              onChange={text => setUserObject(prev => ({...prev, phone: text}))}
              backgroundColor={'#fff'}
            />
          </View>
          <View style={{height: 10}} />

          <View>
            <Input
              label={translate('email')}
              value={userObject?.email}
              onChange={text => setUserObject(prev => ({...prev, email: text}))}
              backgroundColor={'#fff'}
              keyboardType="email-address"
              inputMode="email"
            />
          </View>
        </View>
        <View
          style={{
            marginTop: 5 * scaleRatio,
            marginBottom: 2 * scaleRatio,
            paddingHorizontal: 16,
          }}>
          {/* <View style={styles.settingBox}>
            <TextCp textType="bold" style={styles.title}>
              {translate('manage_accounts')}
            </TextCp>
            <TouchableOpacity
              style={styles.item}
              onPress={() =>
                props.navigation.navigate('Accounts', {type: 'bank_accounts'})
              }>
              <Icon
                name="credit-card"
                size={26 * scaleRatio}
                style={{fontWeight: 'bold'}}
                color="#222"
              />
              <TextCp style={styles.textMenuItem}>
                {translate('bank_accounts')}
              </TextCp>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.item}
              onPress={() =>
                props.navigation.navigate('Accounts', {type: 'mobile_accounts'})
              }>
              <Icon
                name="smartphone"
                size={26 * scaleRatio}
                style={{fontWeight: 'bold'}}
                color="#222"
              />
              <TextCp style={styles.textMenuItem}>
                {translate('mobile_money_accounts')}
              </TextCp>
            </TouchableOpacity>
          </View> */}

          <View style={styles.settingBox}>
            <TextCp textType="bold" style={styles.title}>
              {translate('security')}
            </TextCp>
            <TouchableOpacity
              style={styles.item}
              onPress={() => props.navigation.navigate('ChangePin')}>
              <Icon
                name="lock"
                size={26 * scaleRatio}
                style={{fontWeight: 'bold'}}
                color="#222"
              />
              <TextCp style={styles.textMenuItem}>
                {translate('reset_pin')}
              </TextCp>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.item}
              onPress={() => props.navigation.navigate('ChangePhone')}>
              <Icon
                name="edit"
                size={26 * scaleRatio}
                style={{fontWeight: 'bold'}}
                color="#222"
              />
              <TextCp style={styles.textMenuItem}>
                {translate('change_phone')}
              </TextCp>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.item}
              onPress={() =>
                props.navigation.navigate('Sumsub', {toPage: 'Profile'})
              }>
              <Icon name="user-check" size={26 * scaleRatio} color="#222" />
              <TextCp style={styles.textMenuItem}>
                {translate('verify_your_identity')}
              </TextCp>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.item}
              onPress={() =>
                props.navigation.navigate('WebViewPage', {
                  title: 'FAQs',
                  url: 'https://intercom.help/cartezi-technology-5ce69f6b7243/fr/',
                })
              }>
              <Icon name="help-circle" size={26 * scaleRatio} color="#222" />
              <TextCp style={styles.textMenuItem}>{translate('FAQs')}</TextCp>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.item}
              onPress={() =>
                props.navigation.navigate('WebViewPage', {
                  title: 'DanaPay Conditions',
                  url: 'https://www.danapay.com/terms-of-service',
                })
              }>
              <Icon name="shield" size={26 * scaleRatio} color="#222" />
              <TextCp style={styles.textMenuItem} color="#4a4a4a">
                {translate('conditions')}
              </TextCp>
            </TouchableOpacity>
            <View style={styles.item}>
              <Icon name="pocket" size={26 * scaleRatio} color="#222" />
              <TextCp style={styles.textMenuItem} color="#4a4a4a">
                {translate('app_version')} {pjson.version}
                {Config.NODE_ENV !== 'prod' ? `  - ${Config.NODE_ENV}` : ''}
              </TextCp>
            </View>
          </View>
        </View>
      </ScrollView>
      <View style={{marginBottom: 8, paddingHorizontal: 16}}>
        <DanaButton
          theme="#000"
          title={translate('disconnection')}
          onPress={logout}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  page: {
    backgroundColor: '#fff',
    flex: 1,
  },
  toolbar: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  image: {
    height: 50 * scaleRatio,
    width: 50 * scaleRatio,
    borderRadius: 25 * scaleRatio,
  },
  toolbarInner: {
    paddingTop: 10 * scaleRatio,
    alignItems: 'center',
  },
  title: {
    fontSize: 30 * scaleRatio,
  },
  input: {
    width: '100%',
    height: 56,
    backgroundColor: '#fff',
    borderRadius: 8,
    marginVertical: 8 * scaleRatio,
    paddingHorizontal: 17,
    fontFamily: 'Inter-SemiBold',
  },
  item: {
    color: '#000',
    paddingVertical: 16 / 2,
    flexDirection: 'row',
    alignItems: 'center',
  },

  textMenuItem: {
    fontSize: 16 * scaleRatio,
    marginLeft: 18,
  },
  form: {
    marginTop: 2 * scaleRatio,
    paddingHorizontal: 16,
  },
  ImageBox: {
    height: 50 * scaleRatio,
    width: 50 * scaleRatio,
    borderRadius: 25 * scaleRatio,
    elevation: 4,
  },
  settingBox: {
    marginVertical: 16,
  },
  title: {
    fontSize: 18,
    marginBottom: 12,
  },
});

const pickerSelectStyles = StyleSheet.create({
  inputIOS: {
    fontSize: 16 * scaleRatio,
    paddingVertical: 12,
    paddingHorizontal: 17,
    color: '#000',
    paddingRight: 30, // to ensure the text is never behind the icon
    height: 56,
    backgroundColor: '#fff',
    borderRadius: 8,
    marginVertical: 8 * scaleRatio,
    fontFamily: 'Inter-SemiBold',
  },
  inputAndroid: {
    fontSize: 16 * scaleRatio,
    paddingHorizontal: 17,
    paddingVertical: 8,
    color: '#000',
    paddingRight: 30, // to ensure the text is never behind the icon
    height: 56,
    backgroundColor: '#fff',
    fontFamily: 'Inter-SemiBold',
    borderRadius: 8,
    marginVertical: 8 * scaleRatio,
  },
});

export default Profile;
