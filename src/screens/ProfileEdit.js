import {
  View,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  StatusBar,
} from 'react-native';
import React, {useCallback, useMemo, useState} from 'react';
import Heap from '@heap/react-native-heap';
import TextCp from '../components/TextCp';
import {translate} from '../utilities/Translate';
import useDanaStore from '../app_state/store';
import Input from '../components/Input';
import SelectInput from '../components/SelectInput';
import DanaButton from '../components/DanaButton';
import {CustomerIO} from 'customerio-reactnative';
import {addHearAbout, editProfile} from '../apis/auth';
import {navigateWithNoHistory} from '../utilities/ForgetHistroty';
import {CommonActions} from '@react-navigation/native';
import {showMessage} from 'react-native-flash-message';
import {extractYupErrors} from '../utilities';
import {
  registerWithEmailSchema,
  jobWithOtherSchema,
  jobSchema,
  locationSchema,
  CountriesSchema,
  hearWitOtherSchema,
  hearWitEventSchema,
  hearSchema,
  hearWitRefSchema,
  registerSchema,
} from '../validations';
import Divider from '../components/Divider';

const special_HA = ['event', 'Other', 'referred'];

const special_HAMapping = {
  event: 'event',
  Other: 'other',
  referred: 'referralCode',
};

const ProfileEdit = props => {
  const [page, setPage] = useState('Personal Information Setting Page');
  const token = useDanaStore(state => state.token);
  const user = useDanaStore(state => state.user);
  const temp_user = useDanaStore(state => state.temp_user);
  const [loading, setLoading] = useState(false);
  const countries = useDanaStore(state => state.countries);

  const menu = useMemo(
    () => [
      {
        name: translate('personal_info'),
        subtitle: translate('personal_info_desc'),
      },
      {
        name: translate('revenue_source'),
        subtitle: translate('revenue_source_desc'),
      },
      {
        name: translate('hear_about_us'),
        subtitle: translate('hear_about_us_desc'),
      },
      {
        name: translate('address_details'),
        subtitle: translate('address_details_desc'),
      },
      {
        name: translate('receiving_countries'),
        subtitle: translate('receiving_countries_desc'),
      },
    ],
    [],
  );

  const userCountry = useMemo(
    () => countries.find(val => val.country_code === temp_user?.country_code),
    [temp_user, countries],
  );

  const [user_object, setUserObject] = useState({
    firstname: user?.first_name,
    lastname: user?.last_name,
    email: user?.email,
    monthly_revenue: '',
    referralCode: '',
    job: '',
    hear_about_us: '',
    userType: 'individual',
    reason_for_modification: 'Profile set up from the mobile app',
    other: '',
    city: '',
    country: '',
    address_line: '',
    activity_id: '',
    receiving_countries_ids: [],
  });
  const [step, setStep] = useState(0);
  const [error, setError] = useState({});

  const collectUserBioData = useCallback(async () => {
    const results = {
      lastname: user_object.lastname,
      firstname: user_object.firstname,
    };
    try {
      if (userCountry.preferred_notification_channel === 'mail') {
        results['email'] = user_object.email;
        await registerWithEmailSchema.validate(results, {abortEarly: false});
      } else {
        await registerSchema.validate(results, {abortEarly: false});
      }
      setError(null);
      CustomerIO.setProfileAttributes({
        created_at: Math.floor(Date.now() / 1000),
        email: 'email' in results ? user_object.email : null,
        'user type': 'individual',
        mobile: true,
      });
      Heap.track('Identity Information Set');
      setPage('Revenue Setting Page');
      setStep(prev => prev + 1);
    } catch (error) {
      setError(extractYupErrors(error));
    }
  }, [
    user_object.email,
    user_object.firstname,
    user_object.lastname,
    userCountry,
  ]);

  const configuredActivities = useDanaStore(
    state => state.configuredActivities,
  );

  const collectRevenueData = useCallback(async () => {
    const results = {
      activity_id: user_object.activity_id,
      monthly_revenue: user_object.monthly_revenue,
    };
    try {
      if (user_object.activity_id === 15) {
        results['job'] = user_object.job;
        await jobWithOtherSchema.validate(results, {abortEarly: false});
      } else {
        await jobSchema.validate(results, {abortEarly: false});
      }
      setError(null);
      Heap.track('Revenue information set');
      setPage('Origin Setting Page');
      setStep(prev => prev + 1);
    } catch (error) {
      setError(extractYupErrors(error));
    }
  }, [user_object.activity_id, user_object.job, user_object.monthly_revenue]);

  const collectAddress = useCallback(async () => {
    const results = {
      address_line: user_object.address_line,
      city: user_object.city,
    };
    try {
      await locationSchema.validate(results, {abortEarly: false});
      setError(null);
      Heap.track('Location information set');
      setPage('Receiving Countries Setting Page');
      setStep(prev => prev + 1);
    } catch (error) {
      setError(extractYupErrors(error));
    }
  }, [user_object.address_line, user_object.city]);

  const collectHearAboutUs = useCallback(async () => {
    try {
      if (user_object.hear_about_us === 'Other') {
        await hearWitOtherSchema.validate(
          {
            hear_about_us: user_object.hear_about_us,
            other: user_object.other,
          },
          {abortEarly: false},
        );
      }

      if (user_object.hear_about_us === 'event') {
        await hearWitEventSchema.validate(
          {
            hear_about_us: user_object.hear_about_us,
            event: user_object.event,
          },
          {abortEarly: false},
        );
      }

      if (user_object.hear_about_us === 'referred') {
        await hearWitRefSchema.validate(
          {
            hear_about_us: user_object.hear_about_us,
            referralCode: user_object.referralCode,
          },
          {abortEarly: false},
        );
      }

      if (!['referred', 'event', 'Other'].includes(user_object.hear_about_us)) {
        await hearSchema.validate(
          {
            hear_about_us: user_object.hear_about_us,
          },
          {abortEarly: false},
        );
      }

      const selectedItem = special_HA.includes(user_object.hear_about_us)
        ? `${special_HAMapping[user_object.hear_about_us]} - ${
            user_object[special_HAMapping[user_object.hear_about_us]]
          }`
        : user_object.hear_about_us;

      setError(null);
      Heap.addEventProperties({'Origin Type': selectedItem});
      Heap.track('Origin Set');
      setPage('Address Setting Page');
      setStep(prev => prev + 1);
    } catch (error) {
      setError(extractYupErrors(error));
    }
  }, [user_object.hear_about_us, user_object.other, user_object.referralCode]);

  const collectPreferences = useCallback(async () => {
    const results = {
      receivingCountries: user_object.receiving_countries_ids,
    };

    try {
      await CountriesSchema.validate(results, {abortEarly: false});
      setError(null);
      Heap.track('Receiving countries set');
      saveChanges();
    } catch (error) {
      setError(extractYupErrors(error));
    }
  }, [user_object.receiving_countries_ids]);

  const setHABValue = useCallback(value => {
    setUserObject(prev => ({...prev, hear_about_us: value}));
  }, []);

  const saveChanges = async () => {
    const selectedItem =
      user_object.hear_about_us === 'referred'
        ? 'Referral'
        : user_object.hear_about_us;
    Heap.track(`${selectedItem} Origin Set`, {...user_object});
    try {
      const payload = {
        country_code: temp_user?.country_code,
        phone_number: temp_user?.phone_number,
        first_name: user_object.firstname,
        last_name: user_object.lastname,
        monthly_revenue: user_object.monthly_revenue,
        job: user_object.job,
        city: user_object.city,
        address_line: user_object.address_line,
        activity_id: user_object.activity_id,
        receiving_countries_ids: user_object.receiving_countries_ids,
        reason_for_modification:
          'Editing profile on mobile app after account verifications',
      };

      if (user_object.email) {
        payload['email'] = user_object.email;
      }

      if (user_object.referralCode) {
        payload['referralCode'] = user_object.referralCode;
      }

      setLoading(true);

      const selectedItem = special_HA.includes(user_object.hear_about_us)
        ? `${special_HAMapping[user_object.hear_about_us]} - ${
            user_object[special_HAMapping[user_object.hear_about_us]]
          }`
        : user_object.hear_about_us;

      const response = await editProfile(payload);
      const res = await addHearAbout({user_origin: selectedItem});

      setLoading(false);
      if (!user?.is_active || !user?.is_verified) {
        props.navigation.navigate('Sumsub', {toPage: 'Home'});
      } else {
        navigateWithNoHistory(props, CommonActions, 'Home');
      }
    } catch (error) {
      setLoading(false);
      showMessage({
        type: 'danger',
        message: error.response.data.message,
      });
    }
  };

  return (
    <SafeAreaView style={styles.page}>
      <StatusBar barStyle={'dark-content'} backgroundColor={'#f1f1f1'} />
      <View style={styles.toolbar}>
        <View>
          <TextCp style={{width: '80%', fontSize: 20}} textType={'bold'}>
            {menu[step].name}
          </TextCp>
          <TextCp style={{fontSize: 14}} textType={'regular'}>
            {menu[step].subtitle}
          </TextCp>
        </View>
      </View>
      <ScrollView
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}>
        {step === 0 && (
          <View style={{marginTop: 20}}>
            <Input
              label={translate('firstname')}
              value={user_object.firstname}
              onChange={text =>
                setUserObject(prev => ({...prev, firstname: text}))
              }
              showLabel
              error={error?.firstname && translate(error?.firstname)}
            />
            <Divider height={8} />
            <Input
              label={translate('lastname')}
              value={user_object.lastname}
              onChange={text =>
                setUserObject(prev => ({...prev, lastname: text}))
              }
              showLabel
              error={error?.lastname && translate(error?.lastname)}
            />
            <Divider height={8} />
            <Input
              label={`${translate('email')} ${
                userCountry?.preferred_notification_channel === 'sms'
                  ? `[ ${translate('Optional')} ]`
                  : ''
              }`}
              value={user_object.email}
              onChange={text => setUserObject(prev => ({...prev, email: text}))}
              showLabel
              error={error?.email && translate(error?.email)}
            />

            <DanaButton
              theme="#282828"
              title={translate('next')}
              onPress={collectUserBioData}
            />
          </View>
        )}

        {step === 1 && (
          <View style={{marginTop: 20}}>
            <TextCp>{translate('job_type')}</TextCp>
            <SelectInput
              onValueChange={text =>
                setUserObject(prev => ({...prev, activity_id: text}))
              }
              items={configuredActivities.map(val => ({
                value: val.id,
                label: translate(val.slug),
              }))}
              placeholder={{
                label: translate('job'),
                value: translate('job'),
              }}
              value={user_object.activity_id}
              error={error?.activity_id && translate(error?.activity_id)}
            />
            <Divider height={8} />
            {user_object.activity_id === 15 && (
              <Input
                label={translate('other_job_type')}
                value={user_object.job}
                onChange={text => setUserObject(prev => ({...prev, job: text}))}
                showLabel
                error={error?.job && translate(error?.job)}
              />
            )}

            <Divider height={8} />
            <TextCp>{translate('revenue')}</TextCp>
            <SelectInput
              onValueChange={text =>
                setUserObject(prev => ({...prev, monthly_revenue: text}))
              }
              items={[
                {label: '0 - 1400€', value: '0 - 1400€'},
                {label: '1400€ - 2500€', value: '1400€ - 2500€'},
                {label: '2500€ - 4000€', value: '2500€ - 4000€'},
                {label: '> 4000€', value: '> 4000€'},
              ]}
              placeholder={{
                label: translate('revenue'),
                value: translate('revenue'),
              }}
              value={user_object.monthly_revenue}
              error={
                error?.monthly_revenue && translate(error?.monthly_revenue)
              }
            />
            <Divider height={16} />

            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <View style={{flex: 1}}>
                <DanaButton
                  theme="#f1f1f1"
                  textColor="#222"
                  loadingColor={'#037375'}
                  title={translate('return')}
                  onPress={() => setStep(prev => prev - 1)}
                />
              </View>
              <View style={{width: 20}} />
              <View style={{flex: 1}}>
                <DanaButton
                  theme="#282828"
                  title={translate('next')}
                  onPress={collectRevenueData}
                />
              </View>
            </View>
          </View>
        )}

        {step === 2 && (
          <View style={{marginTop: 20}}>
            <TextCp>{translate('hear_about_us')}</TextCp>
            <SelectInput
              onValueChange={text => {
                setHABValue(text);
              }}
              items={[
                {label: 'Linkedin', value: 'Linkedin'},
                {label: 'Facebook', value: 'Facebook'},
                {label: 'Google', value: 'Google'},
                {label: 'Instagram', value: 'Instagram'},
                {label: 'TikTok', value: 'TikTok'},
                {
                  label: translate('referred_by_someone'),
                  value: 'referred',
                },
                {
                  label: translate('event'),
                  value: 'event',
                },
                {label: translate('Other'), value: 'Other'},
              ]}
              placeholder={{
                label: translate('hear_about_us'),
                value: '',
              }}
              value={user_object.hear_about_us}
              error={error?.hear_about_us && translate(error?.hear_about_us)}
            />

            <Divider height={6} />

            {user_object.hear_about_us === 'Other' && (
              <Input
                label={translate('other')}
                value={user_object.other}
                onChange={text =>
                  setUserObject(prev => ({...prev, other: text}))
                }
                error={error?.other && translate(error?.other)}
              />
            )}

            {user_object.hear_about_us === 'event' && (
              <Input
                label={translate('event_name')}
                value={user_object.event}
                onChange={text =>
                  setUserObject(prev => ({...prev, event: text}))
                }
                error={error?.event && translate(error?.event)}
              />
            )}

            {user_object.hear_about_us === 'referred' && (
              <Input
                label={translate('referralCode')}
                value={user_object.referralCode}
                onChange={text =>
                  setUserObject(prev => ({...prev, referralCode: text}))
                }
                error={error?.referralCode && translate(error?.referralCode)}
              />
            )}

            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <View style={{flex: 1}}>
                <DanaButton
                  theme="#f1f1f1"
                  textColor="#222"
                  loadingColor={'#037375'}
                  title={translate('return')}
                  onPress={() => setStep(prev => prev - 1)}
                />
              </View>
              <View style={{width: 20}} />
              <View style={{flex: 1}}>
                <DanaButton
                  theme="#282828"
                  title={translate('validate')}
                  onPress={collectHearAboutUs}
                />
              </View>
            </View>
          </View>
        )}

        {step === 3 && (
          <View>
            <Divider height={16} />
            <Input
              label={translate('address_line')}
              value={user_object.address_line}
              onChange={text =>
                setUserObject(prev => ({...prev, address_line: text}))
              }
              showLabel
              error={error && translate(error?.address_line)}
            />
            <Input
              label={translate('city')}
              value={user_object.city}
              onChange={text => setUserObject(prev => ({...prev, city: text}))}
              showLabel
              error={error && translate(error?.city)}
            />
            <Divider height={4} />
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <View style={{flex: 1}}>
                <DanaButton
                  theme="#f1f1f1"
                  textColor="#222"
                  loadingColor={'#037375'}
                  title={translate('return')}
                  onPress={() => setStep(prev => prev - 1)}
                />
              </View>
              <View style={{width: 20}} />
              <View style={{flex: 1}}>
                <DanaButton
                  theme="#282828"
                  title={translate('validate')}
                  onPress={collectAddress}
                />
              </View>
            </View>
          </View>
        )}

        {step === 4 && (
          <View>
            <Divider height={16} />
            <TextCp>{translate('receiving_countries')}</TextCp>
            <Divider height={8} />

            <SelectInput
              onValueChange={text => {
                setUserObject(prev => ({
                  ...prev,
                  receiving_countries_ids: text,
                }));
              }}
              items={countries.map(val => ({
                label: val.name,
                value: val.id,
              }))}
              isMultiple={true}
              placeholder={''}
              value={user_object.receiving_countries_ids}
              error={error && translate(error?.receivingCountries)}
            />

            <Divider height={20} />
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <View style={{flex: 1}}>
                <DanaButton
                  theme="#f1f1f1"
                  textColor="#222"
                  loadingColor={'#037375'}
                  title={translate('return')}
                  onPress={() => setStep(prev => prev - 1)}
                />
              </View>
              <View style={{width: 20}} />
              <View style={{flex: 1}}>
                <DanaButton
                  theme="#282828"
                  title={translate('validate')}
                  loading={loading}
                  onPress={collectPreferences}
                />
              </View>
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default ProfileEdit;

const styles = StyleSheet.create({
  page: {
    flex: 1,
    backgroundColor: '#eee',
  },
  toolbar: {
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  content: {
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
});
