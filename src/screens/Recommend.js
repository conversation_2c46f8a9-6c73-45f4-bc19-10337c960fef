import React, {useState, useEffect, useContext} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Platform,
  StatusBar,
} from 'react-native';
import {translate, setI18nConfig} from '../utilities/Translate';
import {pagePadding, titleFontSize, aspectRatio, scaleRatio} from '../theme';
import TextCp from '../components/TextCp';
import {ScrollView} from 'react-native-gesture-handler';
import Toolbar from '../components/Toolbar';
import {GlobalContext} from '../Context/Index';
import DanaButton from '../components/DanaButton';
import _ from 'lodash';
import RecommendItem from '../components/RecommendItem';

const keyboardVerticalOffset = Platform.OS === 'ios' ? 40 : 0;
const Recommend = (props) => {
  const [payMethod, setPayMethod] = useState('');
  const [payMethodError, setPayMethodError] = useState(false);
  const context = useContext(GlobalContext);
  const recommenders = [
    {name: '<PERSON><PERSON> <PERSON>', created_at: '12 Dec 2020'},
    {name: '<PERSON><PERSON> <PERSON>', created_at: '12 Dec 2020'},
  ];
  const paymentTypes = [
    {
      name: '1',
      value: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do',
    },
    {
      name: '2',
      value: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do',
    },
    {
      name: '3',
      value: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do',
    },
  ];

  const getUserCash = () => {};

  const selectPaymentOption = (method) => {
    setPayMethodError(false);
    setPayMethod(method.name);
  };

  useEffect(() => {
    setI18nConfig();
    getUserCash();
  }, []);

  const goBack = () => {
    props.navigation.goBack();
  };

  return (
    <View style={styles.page}>
      <StatusBar barStyle="dark-content" backgroundColor={'#fff'} />
      <View style={{flexGrow: 1}}>
        <Toolbar goBack={goBack} />
        <TextCp textType="bold" style={styles.h1}>
          {translate('recommend_title')}
        </TextCp>
        <View style={styles.payOptionsList}>
          <ScrollView horizontal={true} showsHorizontalScrollIndicator={false}>
            {paymentTypes.map((option, index) => {
              return (
                <TouchableOpacity
                  disabled={option.name == '1' ? true : false}
                  key={index}
                  onPress={() => selectPaymentOption(option)}
                  style={{
                    width: 120 * aspectRatio,
                    height: 100 * aspectRatio,
                    backgroundColor:
                      payMethod &&
                      payMethod.toLowerCase() == option.name.toLowerCase()
                        ? '#000'
                        : '#D1D1D1',
                    borderRadius: 10,
                    marginRight: 10,
                    marginVertical: 16,
                  }}>
                  <View
                    style={{
                      paddingVertical: 10,
                      paddingHorizontal: 20,
                      justifyContent: 'center',
                      height: '100%',
                    }}>
                    <TextCp
                      textType="bold"
                      style={{
                        fontSize: 24,
                        color:
                          payMethod.toLowerCase() == option.name.toLowerCase()
                            ? '#fff'
                            : '#555',
                        textTransform: 'capitalize',
                      }}>
                      {option.value}
                    </TextCp>
                  </View>
                </TouchableOpacity>
              );
            })}
          </ScrollView>
        </View>
        <TextCp style={{fontSize: 15, color: 'rgba(0, 0, 0, 0.5)'}}>
          {translate('recommend_title1')}
        </TextCp>
        <TextCp textType="bold" style={styles.h1}>
          50 €
        </TextCp>

        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginVertical: 20,
          }}>
          <View>
            <TextCp style={{fontSize: 15, color: 'rgba(0, 0, 0, 0.5)'}}>
              {translate('recommend_title2')}
            </TextCp>
          </View>
          <TouchableOpacity
            onPress={() => props.navigation.navigate('RecommendationList')}>
            <TextCp style={{fontSize: 15, color: '#037375'}}>
              {translate('recommend_title3')}
            </TextCp>
          </TouchableOpacity>
        </View>

        {recommenders.map((value) => {
          return <RecommendItem value={value} />;
        })}
      </View>

      <View style={styles.bottom}>
        <DanaButton
          title={translate('recommend_btn')}
          onPress={() => props.navigation.navigate('ToRecommend')}
          theme="#282828"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  page: {
    paddingHorizontal: pagePadding,
    backgroundColor: '#fff',
    paddingTop: Platform.OS === 'android' ? 10 : 0,
    flex: 1,
  },
  h1: {
    fontSize: titleFontSize,
    marginBottom: 17 * scaleRatio,
  },
  p: {
    fontSize: 15 * scaleRatio,
    marginBottom: 6 * scaleRatio,
  },
  input: {
    width: '100%',
    height: 45,
    backgroundColor: 'rgba(0, 0, 0, 0.07)',
    borderRadius: 5,
    marginVertical: 5.5,
    paddingHorizontal: 17 * scaleRatio,
  },
  bottom: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
  },

  money: {
    fontSize: 25,
    color: '#000',
    marginBottom: 12,
  },

  payOptionsList: {
    marginBottom: 6,
  },
  amount: {
    color: 'rgba(0, 0, 0, 0.51)',
    fontSize: 12,
  },
  error: {
    fontSize: 12,
    color: 'red',
  },
  cash: {marginBottom: 16 * scaleRatio, flexDirection: 'row'},
  bullet: {
    color: '#dadcdc',
    marginRight: 15,
  },
  name: {
    height: 19,
    color: 'rgba(0, 0, 0, 0.5)',
    fontSize: 16 * scaleRatio,
    marginRight: 10,
  },
  conversion: {
    color: 'rgba(0, 0, 0, 0.5)',
    fontSize: 16 * scaleRatio,
  },
  calc: {
    marginLeft: 15,
    marginTop: 11 * scaleRatio,
  },
});

export default Recommend;
