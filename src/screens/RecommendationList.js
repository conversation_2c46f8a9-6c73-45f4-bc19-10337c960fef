import React, {useEffect, useState, useContext} from 'react';
import {
  View,
  Platform,
  StyleSheet,
  TextInput,
  ActivityIndicator,
  ScrollView,
  StatusBar,
} from 'react-native';
import Toolbar from '../components/Toolbar';
import TextCp from '../components/TextCp';
import {gs, pagePadding, scaleRatio, titleFontSize} from '../theme';
import {translate} from '../utilities/Translate';
import {GlobalContext} from '../Context/Index';
import RecommendItem from '../components/RecommendItem';

const RecommendationList = (props) => {
  const [transactionClone, setTransactionClone] = useState([]);
  const [clickedTransaction, setClickedTransaction] = useState({
    status: 'l',
    message: 'Fetching Transaction details',
    data: null,
  });
  const [searchTerm, setSearchTerm] = useState(null);
  const context = useContext(GlobalContext);
  const [transactions, setTransactions] = useState([]);
  const [fetching, setFetching] = useState(false);
  const recommenders = [
    {name: '<PERSON><PERSON>', created_at: '12 Dec 2020'},
    {name: '<PERSON><PERSON>', created_at: '12 Dec 2020'},
    {name: '<PERSON>nde <PERSON>', created_at: '12 <PERSON> 2020'},
    {name: '<PERSON>nde <PERSON>', created_at: '12 <PERSON> 2020'},
    {name: '<PERSON>nde <PERSON>', created_at: '12 Dec 2020'},
    {name: '<PERSON>nde <PERSON>', created_at: '12 Dec 2020'},
    {name: '<PERSON>nde <PERSON>', created_at: '12 Dec 2020'},
    {name: '<PERSON>nde <PERSON>', created_at: '12 Dec 2020'},
    {name: 'Katende Ivan', created_at: '12 Dec 2020'},
    {name: 'Katende Ivan', created_at: '12 Dec 2020'},
  ];

  useEffect(() => {}, []);

  const goBack = () => {
    props.navigation.goBack();
  };

  const search = (searchText) => {};

  return (
    <View style={styles.page}>
      <StatusBar barStyle="dark-content" backgroundColor={'#fff'} />
      <View style={{paddingHorizontal: 8}}>
        <Toolbar goBack={goBack} />
        <TextCp textType="bold" style={styles.title}>
          {translate('invitations')}
        </TextCp>
        <TextInput
          style={styles.input}
          onChangeText={(text) => search(text)}
          placeholder={translate('search')}
          value={searchTerm}
        />
      </View>
      {fetching ? (
        <View
          style={{
            height: 400,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <ActivityIndicator size="large" color="#666" />
        </View>
      ) : (
        <ScrollView style={styles.list}>
          {recommenders.map((value) => {
            return <RecommendItem value={value} />;
          })}
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  phones: {
    fontSize: 14 * scaleRatio,
    color: 'rgba(0,0,0,.5)',
  },
  contact_details: {
    fontSize: 17 * scaleRatio,
  },
  contact: {
    flexDirection: 'row',
    marginBottom: 15 * scaleRatio,
  },
  bullet: {
    fontSize: 30 * scaleRatio,
    marginRight: 10,
    color: '#eee',
  },
  input: {
    width: '100%',
    height: 45,
    backgroundColor: 'rgba(0, 0, 0, 0.07)',
    borderRadius: 5,
    marginVertical: 5.5 * scaleRatio,
    paddingHorizontal: 17,
  },
  page: {
    paddingHorizontal: pagePadding - 8,
    backgroundColor: '#fff',
    paddingTop: Platform.OS === 'android' ? 1 : 0,
    flex: 1,
  },
  title: {
    fontSize: titleFontSize,
    marginBottom: 3 * scaleRatio,
  },
  list: {
    height: '60%',
    marginVertical: 5 * scaleRatio,
    paddingHorizontal: 2,
  },
  subTitle: {
    marginBottom: 23 * scaleRatio,
  },
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    paddingHorizontal: 26,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16 * scaleRatio,
  },
  innerContainerTra: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16,
  },
  modalTitle: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginVertical: 10,
    alignItems: 'center',
  },
  downloadBtn: {
    backgroundColor: '#000',
    height: 50 * scaleRatio,
    width: 50 * scaleRatio,
    borderRadius: 25 * scaleRatio,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 25,
  },
  downloadIcon: {
    fontSize: 30 * scaleRatio,
    color: '#fff',
  },
  modalMoney: {
    color: '#000000',
    fontSize: 30 * scaleRatio,
  },
  modalheader: {
    color: 'rgba(0, 0, 0, 0.51)',
    fontSize: 14 * scaleRatio,
    marginBottom: 5,
  },
  modalConversion: {
    color: 'rgba(0, 0, 0, 0.5)',
    fontSize: 14 * scaleRatio,
  },
  statusItem: {
    marginBottom: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  scrollLocations: {
    flexDirection: 'row',
    marginLeft: 10,
  },
  leftScroll: {
    flex: 1,
    height: 130 * scaleRatio,
  },
  rightScroll: {
    flex: 1,
    height: 130 * scaleRatio,
  },
  RecommendationListBox: {
    paddingVertical: 5 * scaleRatio,
    paddingHorizontal: 10 * scaleRatio,
    ...gs.boxShadow,
    backgroundColor: '#fff',
    borderRadius: 14,
    marginTop: 10,
    marginBottom: 10,
  },
  status: {
    paddingHorizontal: 14 * scaleRatio,
    backgroundColor: '#fff',
    borderRadius: 10,
    paddingVertical: 8,
    ...gs.boxShadow,
  },
  modalUser: {
    marginBottom: 15,
    paddingHorizontal: 14,
    backgroundColor: '#fff',
    borderRadius: 10,
    paddingVertical: 8,
    ...gs.boxShadow,
  },
  modalTransaction: {
    flexDirection: 'row',
    marginBottom: 15,
    paddingHorizontal: 14,
    backgroundColor: '#fff',
    borderRadius: 10,
    paddingVertical: 8,
    ...gs.boxShadow,
  },
});

export default RecommendationList;
