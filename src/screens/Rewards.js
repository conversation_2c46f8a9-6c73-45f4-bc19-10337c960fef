import {
  View,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Image,
  Clipboard,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import useDanaStore from '../app_state/store';
import {getReferralListing, getUserReferralData} from '../apis/auth';
import TextCp from '../components/TextCp';
import Icon from 'react-native-vector-icons/Feather';
import {translate} from '../utilities/Translate';
import Share from 'react-native-share';

const statuses = {
  created: {
    textColor: '#C2410C', // Orange-700
    bgColor: '#FFEDD5', // Orange-100
  },
  initiated: {
    textColor: '#C2410C', // Orange-700
    bgColor: '#FFEDD5', // Orange-100
  },
  pending: {
    textColor: '#C2410C', // Orange-700
    bgColor: '#FFEDD5', // Orange-100
  },
  completed: {
    textColor: '#065F46', // Green-800
    bgColor: '#D1FAE5', // Green-100
  },
};

const Rewards = ({navigation}) => {
  const [rewards, setRewards] = useState([]);
  const user = useDanaStore(state => state.user);
  const [rewardData, setRewardData] = useState(null);
  const [loading, setLoading] = useState(true);

  const getRewardData = async () => {
    try {
      const response = await getUserReferralData(user?.id);
      setRewardData(response);
    } catch (error) {
      console.error('Error fetching reward data:', error);
    }
  };

  const getRewards = async () => {
    try {
      setLoading(true);
      const response = await getReferralListing(user?.id);
      // Ensure rewards is always an array
      setRewards(response.referrals);
    } catch (error) {
      console.error('Error fetching rewards:', error);
      setRewards([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getRewards();
    getRewardData();
  }, []);

  const copyToClipboard = () => {
    const code =
      rewardData?.defaultReferralCode?.code || rewardData?.referral_code;
    if (code) {
      Clipboard.setString(code);
    }
  };

  const shareViaWhatsapp = () => {
    const code =
      rewardData?.defaultReferralCode?.code || rewardData?.referral_code;
    const message = `${translate('reward_share_message')} ${
      code || 'ML_762736'
    }`;
    const options = {
      title: translate('reward_share_title'),
      message: message,
      social: Share.Social.WHATSAPP,
      whatsAppNumber: '', // leave empty to use the installed WhatsApp
      filename: 'danapay_referral', // only for base64 file in Android
    };

    Share.shareSingle(options)
      .then(res => {
        console.log('Share success:', res);
      })
      .catch(err => {
        console.log('Share error:', err);
      });
  };



  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            marginRight: 24,
            height: 48,
            width: 48,
            justifyContent: 'center',
          }}>
          <Icon name="arrow-left" size={30} color="#333" />
        </TouchableOpacity>
        <TextCp textType="bold" style={styles.headerTitle}>
          {translate('rewards')}
        </TextCp>
      </View>

      <FlatList
        data={rewards}
        renderItem={({item}) => (
          <View style={styles.rewardItem}>
            <View>
              <TextCp textType="bold">
                {item.referred_fullname}
              </TextCp>
              <TextCp style={styles.date}>
                {item.referred_phone_number}
              </TextCp>
            </View>
            <View style={styles.rewardRight}>
              <TextCp textType="bold" style={styles.amount}>
                {item?.amount_in_euros}€
              </TextCp>
              <View
                style={{
                  ...styles.statusBadge,
                  backgroundColor:
                    statuses[item.status?.toLowerCase() || 'completed']
                      ?.bgColor || '#D1FAE5',
                }}>
                <TextCp
                  style={styles.statusText}
                  color={
                    statuses[item.status?.toLowerCase() || 'completed']
                      ?.textColor || '#065F46'
                  }>
                  {item.status || 'Reçu'}
                </TextCp>
              </View>
            </View>
          </View>
        )}
        keyExtractor={(item, index) => item?.id || String(index)}
        ListHeaderComponent={
          <View>
            <View style={styles.referralCard}>
              <View style={styles.referralContent}>
                <TextCp
                  textType="bold"
                  style={styles.referralTitle}
                  color="#fff">
                  {translate('reward_title')}
                  {rewardData?.campaign?.referrer_compensation_value || '15'}€
                </TextCp>
                <TextCp style={styles.referralDesc} color="#fff">
                  {translate('reward_description')}{' '}
                </TextCp>

                <TextCp style={styles.codeLabel} color="#fff" textType="bold">
                  {translate('reward_code')}
                </TextCp>
                <View style={styles.codeContainer}>
                  <TextCp
                    textType="bold"
                    style={styles.referralCode}
                    color="#FF6B35">
                    {rewardData?.defaultReferralCode?.code ||
                      '_ _ _ _ _ _ _ _ _'}
                  </TextCp>
                  <TouchableOpacity onPress={copyToClipboard}>
                    <Icon name="copy" size={20} color="#fff" />
                  </TouchableOpacity>
                </View>

                <TouchableOpacity
                  style={styles.whatsappButton}
                  onPress={shareViaWhatsapp}>
                  <TextCp style={styles.whatsappText} color="#fff">
                    {translate('reward_share')}
                  </TextCp>
                </TouchableOpacity>
              </View>
            </View>

            <TextCp style={styles.sectionTitle}>
              {translate('reward_list')}
            </TextCp>
          </View>
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <TextCp>{translate('no_rewards')} </TextCp>
          </View>
        }
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        ListFooterComponent={<View style={{height: 50}} />}
        extraData={rewards}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
  },
  profileImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  listContent: {
    padding: 16,
  },
  referralCard: {
    backgroundColor: '#037375',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 24,
  },
  referralContent: {
    padding: 16,
  },
  referralTitle: {
    fontSize: 24,
    color: '#fff',
    marginBottom: 8,
  },
  referralDesc: {
    color: '#fff',
    marginBottom: 16,
    fontSize: 14,
  },
  codeLabel: {
    color: '#fff',
    marginBottom: 8,
  },
  codeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  referralCode: {
    fontSize: 24,
    color: '#FF6B35',
  },
  whatsappButton: {
    backgroundColor: '#000',
    padding: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  whatsappText: {
    color: '#fff',
  },
  sectionTitle: {
    fontSize: 16,
    color: '#888',
    marginBottom: 16,
  },
  rewardItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  date: {
    fontSize: 12,
    color: '#888',
    marginTop: 4,
  },
  rewardRight: {
    alignItems: 'flex-end',
  },
  amount: {
    fontSize: 16,
  },
  statusBadge: {
    backgroundColor: '#E8F5E9',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    marginTop: 4,
  },
  statusText: {
    color: '#4CAF50',
    fontSize: 12,
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 20,
  },
});

export default Rewards;
