import {
  View,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Image,
  Clipboard,
  Linking,
  StatusBar,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import useDanaStore from '../app_state/store';
import {getReferralListing, getUserReferralData} from '../apis/auth';
import TextCp from '../components/TextCp';
import Icon from 'react-native-vector-icons/Feather';
import {translate} from '../utilities/Translate';
import Share from 'react-native-share';
import useGetLang from '../Hooks/useGetLang';
import {convertToUserTime} from '../utilities';

const statuses = {
  created: {
    textColor: '#C2410C', // Orange-700
    bgColor: '#FFEDD5', // Orange-100
  },
  initiated: {
    textColor: '#C2410C', // Orange-700
    bgColor: '#FFEDD5', // Orange-100
  },
  pending: {
    textColor: '#C2410C', // Orange-700
    bgColor: '#FFEDD5', // Orange-100
  },
  completed: {
    textColor: '#065F46', // Green-800
    bgColor: '#D1FAE5', // Green-100
  },
};

const Rewards = ({navigation}) => {
  const lang = useGetLang();
  const [rewards, setRewards] = useState([]);
  const user = useDanaStore(state => state.user);
  const [rewardData, setRewardData] = useState(null);
  const [loading, setLoading] = useState(true);

  const getRewardData = async () => {
    try {
      const response = await getUserReferralData(user?.id);
      setRewardData(response);
    } catch (error) {
      console.error('Error fetching reward data:', error);
    }
  };

  const getRewards = async () => {
    try {
      setLoading(true);
      const response = await getReferralListing(user?.id);
      setRewards(response.referrals);
    } catch (error) {
      console.error('Error fetching rewards:', error);
      setRewards([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getRewards();
    getRewardData();
  }, []);

  const copyToClipboard = () => {
    const code = rewardData?.defaultReferralCode?.code;
    if (code) {
      Clipboard.setString(code);
    }
  };

  const shareViaWhatsapp = () => {
    if (!rewardData?.defaultReferralCode?.code) {
      return;
    }
    const code = rewardData?.defaultReferralCode?.code;

    const phoneNumber = 'PHONE_NUMBER';

    const baseUrl = 'https://api.whatsapp.com/send';
    const message_fr = `Bonjour, Je t'invite à utiliser l'application Danapay. En utilisant le code de parrainage ${code} tu pourras envoyer de l'argent gratuitement à vie vers les comptes mobile money et bancaire en Afrique. Télécharge l'application via ce lien https://onelink.to/cxkgdb`;
    const message_en = `Hello, I invite you to use the Danapay application. By using the referral code ${code}, you will be able to send money for free for life to mobile money and bank accounts in Africa. Download the app using this link: https://onelink.to/cxkgdb`;

    const encodedMessage = encodeURIComponent(
      lang === 'fr' ? message_fr : message_en,
    );

    const fullUrl = `${baseUrl}?phone=${phoneNumber}&text=${encodedMessage}`;

    Linking.openURL(fullUrl)
      .then(supported => {
        if (!supported) {
          console.log('WhatsApp is not installed');
        }
      })
      .catch(error => {
        console.error('Error opening WhatsApp:', error);
      });
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={'#fff'} barStyle="dark-content" />
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            marginRight: 24,
            height: 48,
            width: 48,
            justifyContent: 'center',
          }}>
          <Icon name="arrow-left" size={30} color="#333" />
        </TouchableOpacity>
        <TextCp textType="bold" style={styles.headerTitle}>
          {translate('rewards')}
        </TextCp>
      </View>

      <FlatList
        data={rewards}
        renderItem={({item}) => (
          <View style={styles.rewardItem}>
            <View>
              <TextCp textType="medium" style={{fontSize: 18, marginBottom: 4}}>
                {item.referred_fullname}
              </TextCp>
              <TextCp style={styles.date}>
                {convertToUserTime(item.statuses[0]?.datetime)}
              </TextCp>
            </View>
            <View style={styles.rewardRight}>
              <TextCp textType="bold" style={styles.amount}>
                {item?.amount_in_euros}€
              </TextCp>
              <View
                style={{
                  ...styles.statusBadge,
                  backgroundColor:
                    statuses[item.status?.toLowerCase()]?.bgColor || '#D1FAE5',
                }}>
                <TextCp
                  style={styles.statusText}
                  color={
                    statuses[item.statuses[0].message_en?.toLowerCase()]
                      ?.textColor || '#065F46'
                  }>
                  {lang === 'fr'
                    ? item.statuses[0].message_fr
                    : item.statuses[0].message_en}
                </TextCp>
              </View>
            </View>
          </View>
        )}
        keyExtractor={(item, index) => item?.id || String(index)}
        ListHeaderComponent={
          <View>
            <View style={styles.referralCard}>
              <View style={styles.referralContent}>
                <TextCp
                  textType="merriweather"
                  style={styles.referralTitle}
                  color="#fff">
                  {translate('reward_title')}
                  {rewardData?.campaign?.referrer_compensation_value}€
                </TextCp>
                <TextCp style={styles.referralDesc} color="#fff">
                  {translate('reward_description')}{' '}
                </TextCp>

                <TextCp style={styles.codeLabel} color="#fff" textType="bold">
                  {translate('reward_code')}
                </TextCp>
                <View style={styles.codeContainer}>
                  <TextCp
                    textType="merriweather"
                    style={styles.referralCode}
                    color="#FF6B35">
                    {rewardData?.defaultReferralCode?.code ||
                      '_ _ _ _ _ _ _ _ _'}
                  </TextCp>
                  <TouchableOpacity onPress={copyToClipboard}>
                    <Icon name="copy" size={20} color="#fff" />
                  </TouchableOpacity>
                </View>

                <TouchableOpacity
                  style={styles.whatsappButton}
                  onPress={shareViaWhatsapp}>
                  <TextCp
                    style={styles.whatsappText}
                    color="#fff"
                    textType="bold">
                    {translate('reward_share')}
                  </TextCp>
                </TouchableOpacity>
              </View>
            </View>

            {rewards.length > 0 && (
              <TextCp style={styles.sectionTitle}>
                {translate('reward_list')}
              </TextCp>
            )}
          </View>
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <TextCp>{translate('no_rewards')} </TextCp>
          </View>
        }
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        ListFooterComponent={<View style={{height: 50}} />}
        extraData={rewards}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
  },
  profileImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  listContent: {
    padding: 16,
  },
  referralCard: {
    backgroundColor: '#023428',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 24,
  },
  referralContent: {
    padding: 16,
  },
  referralTitle: {
    fontSize: 32,
    color: '#fff',
    marginBottom: 8,
  },
  referralDesc: {
    color: '#fff',
    marginBottom: 16,
    fontSize: 14,
  },
  codeLabel: {
    color: '#fff',
    marginBottom: 8,
  },
  codeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  referralCode: {
    fontSize: 24,
    color: '#FF6B35',
  },
  whatsappButton: {
    backgroundColor: '#000',
    padding: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  whatsappText: {
    color: '#fff',
  },
  sectionTitle: {
    fontSize: 16,
    color: '#888',
    marginBottom: 16,
  },
  rewardItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  date: {
    fontSize: 12,
    color: '#888',
    marginTop: 4,
  },
  rewardRight: {
    alignItems: 'flex-end',
  },
  amount: {
    fontSize: 18,
    marginBottom: 4,
  },
  statusBadge: {
    backgroundColor: '#E8F5E9',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    marginTop: 4,
  },
  statusText: {
    color: '#4CAF50',
    fontSize: 12,
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 20,
    height: 200,
    justifyContent: 'center',
  },
});

export default Rewards;
