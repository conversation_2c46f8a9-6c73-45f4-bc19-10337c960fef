import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  StatusBar,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import Intercom, {Visibility} from '@intercom/intercom-react-native';
import {pagePadding} from '../theme';
import {translate, setI18nConfig} from '../utilities/Translate';
import {showMessage} from 'react-native-flash-message';
import Toolbar from '../components/Toolbar';
import PinCode from '../components/PinCode';
import {app_logout, getLastRegisterationStep, setPin} from '../apis/auth';
import TextCp from '../components/TextCp';
import {navigateWithNoHistory} from '../utilities/ForgetHistroty';
import {CommonActions} from '@react-navigation/native';
import useDanaStore from '../app_state/store';
import Heap from '@heap/react-native-heap';

const Setpin = props => {
  const saveToken = useDanaStore(state => state.saveToken);
  const saveUser = useDanaStore(state => state.saveUser);
  const temp_user = useDanaStore(state => state.temp_user);
  const setTemp = useDanaStore(state => state.setTemp);
  const resetStore = useDanaStore(state => state.resetStore);
  const setMissingData = useDanaStore(state => state.setMissingData);

  const [pins, setPins] = useState({
    pin: null,
    status: 'enter',
  });

  const [loading, setLoading] = useState(false);

  const handleOnPinFinished = confirmPin => {
    const {pin} = pins;

    if (pin.join() !== confirmPin.join()) {
      showMessage({
        message: translate('pin_didnt_match'),
        type: 'danger',
      });
      return;
    }

    setLoading(true);

    const payload = {
      pincode: pin.join(''),
    };

    setPin(payload)
      .then(response => {
        setLoading(false);

        const registeredUserObj = {
          ...response.user,
        };

        showMessage({
          message: response.message,
          type: 'success',
        });

        intercomInit(response.user);
        saveUser(registeredUserObj);
        setTemp({...temp_user, has_pin: true});

        // Check if the user is company
        if (!response?.user?.is_individual) {
          setRejectReason('individual');
          navigateWithNoHistory(props, CommonActions, 'Rejected');
          return;
        }

        // Check if the user is rejected
        if (response?.user?.is_rejected) {
          setRejectReason('rejected');
          navigateWithNoHistory(props, CommonActions, 'Rejected');
          return;
        }

        getLastRegisterationStep()
          .then(registratioProgress => {
            setMissingData(registratioProgress);
            if (
              !registratioProgress?.completed &&
              response?.user?.client?.type === 'temporary-customer'
            ) {
              navigateWithNoHistory(props, CommonActions, 'Register');
            } else {
              if (!response?.user?.kyc_submitted_at) {
                navigateWithNoHistory(props, CommonActions, 'Sumsub');
              } else {
                setTimeout(() => {
                  setLoading(false);
                  navigateWithNoHistory(props, CommonActions, 'Home');
                }, 200);
              }
            }
          })
          .catch(error => null);
      })
      .catch(error => {
        showMessage({
          message: error.message,
          type: 'danger',
        });
        setLoading(false);
      });
  };

  const goBack = () => {
    // props.navigation.goBack();
    Alert.alert(
      '',
      translate('logoutText'),
      [
        {
          text: translate('no'),
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: translate('yes'),
          onPress: () => {
            app_logout()
              .then(res => {
                try {
                  Intercom.logout();
                } catch (error) {}
                resetStore();
                navigateWithNoHistory(props, CommonActions, 'Tutorial');
              })
              .catch(error => {});
          },
        },
      ],
      {cancelable: false},
    );
  };

  const resetPin = () => {
    setPins({status: 'enter', pin: null});
    setLoading(false);
  };

  const intercomInit = userObject => {
    try {
      Intercom.loginUserWithUserAttributes({
        email: userObject?.email
          ? userObject?.email
          : userObject.full_phone_number,
        userId: userObject?.id,
      });
      Intercom.setLauncherVisibility(Visibility.VISIBLE);
      Intercom.setBottomPadding(Platform.OS === 'ios' ? 0 : 23);
      Heap.identify(userObject?.full_phone_number, 'phone');
    } catch (error) {}
  };

  useEffect(() => {
    setI18nConfig();
  }, []);

  return (
    <SafeAreaView
      style={{
        backgroundColor: '#fff',
        flex: 1,
        paddingHorizontal: pagePadding,
      }}>
      <StatusBar barStyle="dark-content" backgroundColor={'#fff'} />
      <View style={{paddingHorizontal: 16}}>
        <Toolbar goBack={goBack} />
      </View>
      {pins.status === 'enter' ? (
        <PinCode
          titleText={translate('create_pin_title')}
          subTitleText={translate('create_pin_desc')}
          pinFinished={result =>
            setPins(prev => ({pin: result, status: 'confirm'}))
          }
        />
      ) : (
        <View>
          <PinCode
            titleText={translate('confirm_pin_title')}
            subTitleText={translate('confirm_pin_desc')}
            loadingText={translate('creating_pin')}
            pinFinished={handleOnPinFinished}
            loading={loading}
          />
          <TouchableOpacity onPress={resetPin} style={styles.resetBtn}>
            <TextCp style={styles.resetBtnText} textType={'bold'}>
              {translate('reset_pin')}
            </TextCp>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  spinnerTextStyle: {
    color: '#FFF',
  },
  resetBtn: {
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  resetBtnText: {
    color: '#027375',
    fontSize: 13,
  },
});

export default Setpin;
