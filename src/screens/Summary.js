import React, {useEffect, useState} from 'react';
import {
  View,
  StatusBar,
  StyleSheet,
  ScrollView,
  Image,
  Platform,
  SafeAreaView,
} from 'react-native';
import {
  borderRadius,
  titleFontSize,
  subtitleFontSize,
  scaleRatio,
} from '../theme';
import {CustomerIO} from 'customerio-reactnative';

import {setI18nConfig, translate} from '../utilities/Translate';
import TextCp from '../components/TextCp';
import DanaButton from '../components/DanaButton';
import Toolbar from '../components/Toolbar';
import Icon from 'react-native-vector-icons/Feather';
import {makeDirectTransfer, makeTransfer} from '../apis/Transfers';
import {cleanError} from '../utilities/cleanerror';
import {navigateWithNoHistory} from '../utilities/ForgetHistroty';
import {CommonActions} from '@react-navigation/native';
import crashlytics from '@react-native-firebase/crashlytics';
import useDanaStore from '../app_state/store';
import ExternalPaymentsModal from '../components/ExternalPaymentsModal';
import {showMessage} from 'react-native-flash-message';
import CashLayout from '../components/CashLayout';
import Heap from '@heap/react-native-heap';

const Summary = props => {

  const transferTimer = useDanaStore(state => state.transferTimer);
  const updateTransferTimer = useDanaStore(state => state.updateTransferTimer);

  const location = useDanaStore(state => state.location);
  const beneficiary = useDanaStore(state => state.beneficiary);
  const transfer = useDanaStore(state => state.transfer);
  const token = useDanaStore(state => state.token);
  const user = useDanaStore(state => state.user);
  const fees = useDanaStore(state => state.fees);
  const trackingId = useDanaStore(state => state.trackingId);
  const storeOperationTrackingId = useDanaStore(
    state => state.storeOperationTrackingId,
  );
  const sendingCountry = useDanaStore(state => state.sendingCountry);
  const [loading, setLoading] = useState(false);
  const [show, setShow] = useState(false);
  const restoreTransfer = useDanaStore(state => state.restoreTransfer);
  const saveTransferResponse = useDanaStore(
    state => state.saveTransferResponse,
  );

  const transfer_response = useDanaStore(state => state.transfer_response);
  const transferTries = useDanaStore(state => state.transferTries);
  const activeOperationPage = useDanaStore(state => state.activeOperationPage);
  const setActiveOperationPage = useDanaStore(
    state => state.setActiveOperationPage,
  );

  useEffect(() => {
    setI18nConfig();
  }, []);

  const proceedWithTransaction = async () => {
    try {
      const payload = {
        cashin_method_id: transfer.cash_in_method,
        amount_without_fees_in_euro: transfer?.amount_in_euro,
        payment_delivery: false,
        is_escrowed: false,
        sending_country_id: sendingCountry.id,
        receiving_country_id: beneficiary.country?.receiving_country?.id,
        phone_number: beneficiary.phone_number,
        country_code: beneficiary.country?.receiving_country?.country_code,
        reason: transfer.reason,
        is_payment_on_demand: false,
        currency: transfer.currency,
        heapio_track_id: trackingId,
      };

      setLoading(true);
      let res = null;

      if (transfer.is_direct) {
        res = await makeDirectTransfer({
          payload: {
            ...payload,
            verify: false,
            operator: transfer.w_provider,
            amount_without_fees_in_euro: transfer?.amount_in_euro,
          },
          token,
        });
      } else {
        res = await makeTransfer(payload);
      }
      storeOperationTrackingId(null);
      handleResult(res);
    } catch (error) {
      const err = cleanError(error);
      crashlytics().recordError(error);
      showMessage({
        type: 'danger',
        message: `${err.main_message} ${err.all_errors}`,
      });
      setLoading(false);
    }
  };

  const handleResult = res => {
    setActiveOperationPage('');
    const method = transfer.cash_in_method_name.toLowerCase();
    Heap.track('Transfer Payment Details Confirmed');

    if (method === 'bank transfer') {
      saveTransferResponse({body: res, nextPage: 'BankerTransfer'});
      props.navigation.navigate('BankerTransfer');
    } else if (method === 'bank card') {
      saveTransferResponse({body: res, nextPage: 'TransferSuccess'});
      setShow(true);
    } else if (method === 'instant sepa') {
      saveTransferResponse({body: res, nextPage: 'TransferSuccess'});
      setShow(true);
    } else if (method === 'balance') {
      saveTransferResponse({body: res, nextPage: 'TransferSuccess'});
      props.navigation.navigate('TransferSuccess', {status: 'success'});
    } else if (method === 'mobile money') {
      props.navigation.navigate('MobileMoney', {mmResponse: res});
    }
  };

  const goBack = () => {
    restoreTransfer();
    navigateWithNoHistory(props, CommonActions, 'Home');
  };

  useEffect(() => {
    const timer = setInterval(() => {
      const counter = transferTimer + 1;
      updateTransferTimer('increment');
      if (counter === 60) {
        updateTransferTimer('reset');
        const currentDate = new Date();
        const dateString = currentDate.toLocaleString();
        CustomerIO.track('Transfer Unfinished In 10 Mins', {
          userType: 'individual',
          operationTYpe: 'Transfer',
          channel: 'mobile',
          email: user?.email,
          'transfer start date': dateString,
          'phone number': `${user?.country_code}${user?.phone_number}`,
          'first name': user?.firstname,
          'last name': user?.lastname,
          page: activeOperationPage,
          tries: transferTries,
        });
      }
    }, 10000);

    return () => {
      clearInterval(timer);
    };
  }, [transferTimer]);

  return (
    <SafeAreaView
      style={{
        flex: 1,
        marginHorizontal: 16,
        backgroundColor: '#f1f1f1',
      }}>
      <StatusBar barStyle="dark-content" backgroundColor="#f1f1f1" />
      <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
        <TextCp textType="bold" style={styles.h1}>
          {translate('summary')}
        </TextCp>
        <Toolbar goBack={() => goBack()} />
      </View>
      <ScrollView style={{flexGrow: 1}} showsVerticalScrollIndicator={false}>
        <TextCp style={styles.p}>{translate('summarySubTitle')}</TextCp>
        <Image source={require('../images/summary.jpg')} style={styles.image} />

        <View style={styles.userInfo}>
          <TextCp style={{fontSize: 12}} opacity={0.51}>
            {translate('Beneficiary')}
          </TextCp>
          <View style={{height: 8}} />
          <TextCp
            textType="bold"
            opacity={0.8}
            color="#282828"
            style={{fontSize: 20 * scaleRatio}}>
            {beneficiary && `${beneficiary.firstName} ${beneficiary.lastName}`}
          </TextCp>
          <TextCp opacity={0.51}>
            <Icon name="phone" />{' '}
            {beneficiary &&
              `+${beneficiary.country?.receiving_country?.country_code} ${beneficiary.phone_number}`}
          </TextCp>
          <TextCp opacity={0.51} style={{textTransform: 'capitalize'}}>
            <Icon name="map" /> {beneficiary && beneficiary?.country_name}
          </TextCp>
        </View>

        <View style={styles.cash}>
          <View style={styles.left}>
            <TextCp style={{fontSize: 12}} opacity={0.51}>
              {translate('funds')}
            </TextCp>
            <View style={{height: 8}} />

            <CashLayout
              value={transfer && transfer?.amount_in_euro}
              fontSize={22 * scaleRatio}
              color="#282828"
            />

            <TextCp
              textType="lighter"
              opacity={0.51}
              style={{fontSize: 14 * scaleRatio}}>
              {fees?.fee_calculation &&
                fees?.fee_calculation.length > 2 &&
                fees?.fee_calculation[1]?.exchange_rate}{' '}
              CFA
            </TextCp>
            <TextCp
              textType="lighter"
              opacity={0.51}
              style={{fontSize: 14 * scaleRatio}}>
              655.957.000K Danacoins
            </TextCp>
          </View>
          <View style={styles.right}>
            <TextCp style={{fontSize: 12}} opacity={0.51}>
              {translate('fees')}
            </TextCp>
            <View style={{height: 8}} />

            {location && location.isPaid ? (
              <CashLayout
                value={fees?.fee + 1.5}
                fontSize={22 * scaleRatio}
                color="#282828"
              />
            ) : (
              <CashLayout
                value={fees?.fee}
                fontSize={22 * scaleRatio}
                color="#282828"
              />
            )}

            <TextCp textType="lighter" opacity={0.51}>
              {translate('delivery')} :{translate('no')}
            </TextCp>
          </View>
        </View>

        <View style={styles.total}>
          <View style={styles.left}>
            <TextCp style={{fontSize: 12}} opacity={0.51}>
              {translate('totalToPay')}
            </TextCp>
            <View style={{height: 8}} />

            {location && location?.isPaid ? (
              <CashLayout
                value={
                  parseInt(transfer?.amount_in_euro) +
                  parseFloat(fees?.fee) +
                  1.5
                }
                fontSize={22 * scaleRatio}
                color="#037375"
              />
            ) : (
              <CashLayout
                value={
                  parseInt(transfer?.amount_in_euro) + parseFloat(fees?.fee)
                }
                fontSize={22 * scaleRatio}
                color="#037375"
              />
            )}
          </View>

          <View style={styles.right}>
            <TextCp style={{fontSize: 12}} opacity={0.5}>
              {translate('means_of_pay')}
            </TextCp>
            <View style={{height: 8}} />

            <TextCp opacity={0.5} textType={'bold'} style={{fontSize: 16}}>
              {translate(
                transfer?.cash_in_method_name
                  .toLowerCase()
                  .split(' ')
                  .join('_'),
              )}
            </TextCp>
          </View>
        </View>
      </ScrollView>

      <View style={styles.bottom}>
        <View style={{flex: 2, paddingRight: 10}}>
          <DanaButton
            title={translate('return')}
            onPress={() => props.navigation.goBack()}
            theme="#fff"
            textColor="#282828"
          />
        </View>
        {/* <View style={{flex: 1}} /> */}
        <View style={{flex: 2, paddingLeft: 10}}>
          <DanaButton
            title={translate('next')}
            onPress={proceedWithTransaction}
            theme="#282828"
            loading={loading}
            testID="moveToSummaryBtn"
          />
        </View>
      </View>
      <ExternalPaymentsModal
        show={show}
        navigation={props.navigation}
        connect_url={transfer_response?.body?.connect_url}
        translate={translate}
        setShow={setShow}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {flex: 1},
  top: {flex: 2, paddingVertical: 20 * scaleRatio},
  bottom: {
    flexDirection: 'row',
    paddingVertical: 10 * scaleRatio,
    paddingHorizontal: 3,
    marginBottom: Platform.OS === 'ios' ? 20 : 20,
  },
  cash: {
    flexDirection: 'row',
    marginVertical: 16 * scaleRatio,
  },
  total: {
    marginVertical: 16 * scaleRatio,
    flexDirection: 'row',
  },
  image: {
    height: 200 * scaleRatio,
    width: '100%',
    borderRadius: borderRadius,
    marginBottom: 0,
  },
  h1: {
    fontSize: titleFontSize,
    textTransform: 'capitalize',
  },
  p: {
    fontSize: subtitleFontSize,
    marginBottom: 16,
    color: '#000',
  },
  userInfo: {
    backgroundColor: '#f1f1f1',
    marginBottom: 16,
    marginTop: 19 * scaleRatio,
  },
  smallText: {
    fontSize: 14 * scaleRatio,
    color: 'rgba(0, 0, 0, 0.75)',
  },
  money: {
    fontSize: 22 * scaleRatio,
    color: '#000',
  },
  left: {
    flex: 3,
  },
  right: {
    flex: 2,
  },
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    paddingHorizontal: 26,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16 * scaleRatio,
  },
  innerContainerTra: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16,
  },
  modalTitle: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginVertical: 10,
    alignItems: 'center',
  },
  webview: {
    height: 700,
  },
  icon: {
    fontSize: 35 * scaleRatio,
  },
});
export default Summary;
