import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  StatusBar,
  Image,
  SafeAreaView,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
  useColorScheme,
} from 'react-native';
import SNSMobileSDK from '@sumsub/react-native-mobilesdk-module';
import {getKYCSumSubToken} from '../apis/auth';
import analytics from '@react-native-firebase/analytics';
import {
  titleFontSize,
  scaleRatio,
  subtitleFontSize,
  borderRadius,
} from '../theme';
import DanaButton from '../components/DanaButton';
import TextCp from '../components/TextCp';
import {setI18nConfig, translate} from '../utilities/Translate';
import Icon from 'react-native-vector-icons/Feather';
import useDanaStore from '../app_state/store';
import {CustomerIO} from 'customerio-reactnative';
import useGetLang from '../Hooks/useGetLang';
import {showMessage} from 'react-native-flash-message';
import FastImage from 'react-native-fast-image';
import {tiktokLogEvents} from '../utilities/tiktok';
import {useTheme} from '@react-navigation/native';

const lightThemeConfig = {
  universal: {
    metrics: {
      buttonBorderWidth: 0,
      fieldCornerRadius: 10,
      buttonCornerRadius: 10,
    },
    colors: {
      backgroundCommon: '#F5F5F5',
      backgroundNeutral: '#FFFFFF',
      primaryButtonBackground: '#023428',
      primaryButtonBackgroundHighlighted: '#035C41', // slightly lighter for pressed state
      primaryButtonBackgroundDisabled: '#BBBBBB',
      secondaryButtonBackground: '#023428',
      secondaryButtonContent: '#FFFFFF',
      fieldBackground: '#FFFFFF',
      contentStrong: '#000000C2',
      contentNeutral: '#000000',
      fieldContent: '#023428',
    },
  },
};

const darkThemeConfig = {
  universal: {
    metrics: {
      buttonBorderWidth: 0,
      fieldCornerRadius: 10,
      buttonCornerRadius: 10,
    },
    colors: {
      backgroundCommon: '#121212',
      backgroundNeutral: '#1E1E1E',
      primaryButtonBackground: '#035C41',
      primaryButtonBackgroundHighlighted: '#048F65', // brighter for highlight
      primaryButtonBackgroundDisabled: '#555555',
      secondaryButtonBackground: '#035C41',
      secondaryButtonContent: '#FFFFFF',
      fieldBackground: '#1E1E1E',
      contentStrong: '#FFFFFFC2',
      contentNeutral: '#FFFFFF',
      fieldContent: '#048F65',
    },
  },
};

const Sumsub = props => {
  const activeTheme = useColorScheme();
  const lang = useGetLang();
  const [page, setPage] = useState('Selfie and Document Setting Page');
  const [accessToken, setAccessToken] = useState('');
  const [processing, setProcessing] = useState(false);
  const onboardingTimer = useDanaStore(state => state.onboardingTimer);
  const updateOnboardingTimer = useDanaStore(
    state => state.updateOnboardingTimer,
  );
  const toPage = props?.route?.params?.toPage;
  const user = useDanaStore(state => state.user);
  const getUserCurrentState = useDanaStore(state => state.getUserCurrentState);
  const updateHNTVP = useDanaStore(state => state.updateHNTVP);

  const getSumSubToken = () => {
    setProcessing(true);
    getKYCSumSubToken({level_name: user?.residency})
      .then(res => {
        setAccessToken(res.token);
        launchSNSMobileSDK();
      })
      .catch(error => {
        setProcessing(false);
        if (error.response.status === 404) {
          showMessage({type: 'danger', message: translate('sumsubError')});
        }
      });
  };

  useEffect(() => {
    setI18nConfig();
  }, []);

  let launchSNSMobileSDK = () => {
    let snsMobileSDK = SNSMobileSDK.init(accessToken, () =>
      getKYCSumSubToken({level_name: user?.residency}).then(resp => resp.token),
    )
      .withHandlers({
        onStatusChanged: event => {
          if (event?.newStatus?.toLowerCase() === 'pending') {
            props.navigation.navigate('Home');
          }
        },
        onLog: event => {
          // console.log('onLog', event);
        },
      })
      .withApplicantConf({
        email: user?.email,
        phone: user?.full_phone_number,
      })
      .withDebug(true)
      .withTheme(activeTheme === 'dark' ? darkThemeConfig : lightThemeConfig)
      .withLocale(lang)
      .build();

    snsMobileSDK
      .launch()
      .then(async result => {
        getUserCurrentState();
        await analytics().logEvent('complete_registration'); //when the user has submitted documents
        await tiktokLogEvents('CompleteRegistration', user, {}, Platform.OS);
        props.navigation.navigate('Home');
      })
      .catch(error => {
        props.navigation.navigate('Home');
      });
  };

  const goBack = () => {
    updateHNTVP(true);
    setProcessing(false);
    setTimeout(() => {
      if (toPage) {
        props.navigation.navigate(toPage);
      } else {
        props.navigation.navigate('Home');
      }
    }, 10);
  };

  useEffect(() => {
    const timer = setInterval(() => {
      updateOnboardingTimer('increment');
      if (onboardingTimer === 60) {
        updateOnboardingTimer('reset');
        CustomerIO.track('Registration Unfinished In 20 Mins', {
          userType: 'individual',
          channel: 'mobile',
          email: user?.email,
          'onboarding start date': new Date(),
          'phone number': `${user?.country_code}${user?.phone_number}`,
          'first name': user?.firstname,
          'last name': user?.lastname,
          page,
        });
      }
    }, 10000);

    return () => {
      clearInterval(timer);
    };
  }, [onboardingTimer]);

  useEffect(() => {
    if (user?.is_verified && user?.is_active) {
      props.navigation.navigate('Home');
    }
  }, [user?.is_verified, user?.is_active]);

  return (
    <SafeAreaView style={styles.page}>
      <StatusBar barStyle="dark-content" backgroundColor={'#f1f1f1'} />
      <View style={styles.toolbar}>
        <View>
          <TextCp textType="bold" style={styles.h1}>
            {translate('verifyTitle')}
          </TextCp>
        </View>

        <TouchableOpacity onPress={() => goBack()}>
          <Icon
            name="x-circle"
            size={25}
            style={styles.icon}
            color="#444"></Icon>
        </TouchableOpacity>
      </View>

      {processing ? (
        <View
          style={{height: 500, justifyContent: 'center', alignItems: 'center'}}>
          <ActivityIndicator size="large" color="#282828" />
        </View>
      ) : (
        <View style={styles.content}>
          <View style={{flex: 1}}>
            <TextCp style={styles.p}>{translate('verifySubTitle')}</TextCp>
            <FastImage
              source={require('../images/verify1.jpg')}
              style={styles.image}
            />
            <View style={styles.footer}>
              <DanaButton
                theme="#282828"
                title={translate('start_verify')}
                onPress={() => getSumSubToken()}
                loading={!!processing}
              />
            </View>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  page: {
    backgroundColor: '#f1f1f1',
    flex: 1,
  },
  toolbar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  closeBtn: {
    height: 45 * scaleRatio,
    width: 45 * scaleRatio,
    justifyContent: 'center',
    alignItems: 'center',
  },
  h1: {
    fontSize: titleFontSize,
    marginBottom: 7 * scaleRatio,
    textTransform: 'capitalize',
  },
  p: {
    fontSize: subtitleFontSize,
    marginBottom: 18 * scaleRatio,
  },
  image: {
    flex: 1,
    width: '100%',
    borderRadius: borderRadius,
  },
  circle: {
    height: 90,
    width: 90,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    backgroundColor: '#037375',
  },
  content: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: 28 * scaleRatio,
    textAlign: 'center',
    marginBottom: 10,
  },
  footer: {
    paddingBottom: 16,
    // justifyContent: 'center',
    // position: 'absolute',
    // bottom: 20,
    // left: 0,
    // right: 0,
    // marginTop: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  content_loading: {
    height: 500,
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuItem: {
    // paddingVertical: 20,
    paddingHorizontal: 16,
  },
  header: {
    minHeight: 60,
    justifyContent: 'center',
    paddingHorizontal: 16,
    backgroundColor: '#eee',
    paddingVertical: 16,
  },
  heading: {
    fontSize: 17,
  },
  body: {
    fontSize: 13,
  },
  contentInner: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  menuItemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#ccc',
    paddingHorizontal: 10,
    borderRadius: 6,
    minHeight: 70,
    // backgroundColor: 'red',
  },
  menuItemRowBtn: {
    height: 25,
    justifyContent: 'center',
    alignItems: 'center',
    width: 25,
    marginHorizontal: 10,
    borderRadius: 4,
  },
  menuItemRowDetails: {
    marginVertical: 10,
  },
});

export default Sumsub;
