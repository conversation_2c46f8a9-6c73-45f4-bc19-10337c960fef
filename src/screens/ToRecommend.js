import React, {useEffect, useState, useContext} from 'react';
import {
  View,
  Platform,
  StyleSheet,
  FlatList,
  PermissionsAndroid,
  TextInput,
  ActivityIndicator,
  StatusBar,
  TouchableOpacity,
  KeyboardAvoidingView,
  Keyboard,
} from 'react-native';
import Contacts from 'react-native-contacts';
import Toolbar from '../components/Toolbar';
import TextCp from '../components/TextCp';
import ContactItem from '../components/ContactItem';
import {pagePadding, scaleRatio} from '../theme';
import DanaButton from '../components/DanaButton';
import {setI18nConfig, translate} from '../utilities/Translate';
import {GlobalContext} from '../Context/Index';
import Modal from 'react-native-modal';
import Icon from 'react-native-vector-icons/Feather';
import RNPickerSelect from 'react-native-picker-select';
const keyboardHeight = Keyboard.keyboardHeight;
import crashlytics from '@react-native-firebase/crashlytics';
import {getData} from '../utilities/DataStore';
import {showMessage} from 'react-native-flash-message';

const ToRecommend = props => {
  const context = useContext(GlobalContext);
  const [contacts, setContacts] = useState([]);
  const [contactsClone, setContactsClone] = useState([]);
  const [searchTerm, setSearchTerm] = useState(null);
  const [favorites, setFavorite] = useState([]);
  const [addingFavorites, setAddingFavorites] = useState(false);
  const [noPermission, setNoPermission] = useState(false);
  const [show, setShow] = useState(false);
  const [firstName, setFirstName] = useState('');
  const [firstNameError, setFirstNameError] = useState(false);
  const [lastName, setLastName] = useState('');
  const [lastNameError, setLastNameError] = useState(false);
  const [country, setCountry] = useState('');
  const [countryError, setCountryError] = useState(false);
  const [phone, setPhone] = useState('');
  const [phoneError, setPhoneError] = useState(false);
  const countries = [
    {
      country_code: 'ML',
      label: 'Mali',
      value: '+223',
    },
  ];

  const [isKeyboardVisible, setKeyboardVisible] = useState(false);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      },
    );

    return () => {
      setKeyboardVisible(false);
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  useEffect(() => {
    fetchContacts();
    setI18nConfig();
  }, [favorites]);

  const fetchContacts = () => {
    if (Platform.OS === 'ios') {
      Contacts.getAll((err, res) => {
        if (err) {
          throw err;
        }
        setContacts(res);
        setContactsClone(res);
      });
    } else {
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.READ_CONTACTS, {
        title: translate('contacts'),
        message: translate('contact_describe'),
      })
        .then(() => {
          Contacts.getAll((err, res) => {
            if (err === 'denied') {
              setNoPermission(true);
            } else {
              setContacts(res);
              setContactsClone(res);
            }
          });
        })
        .catch(error => crashlytics().recordError(error));
    }
  };

  const contactClicked = contact => {
    const sectionPhone = contact.phoneNumbers[0].number.split(' ');
    let phoneNumber = '';
    if (sectionPhone[0].includes('+')) {
      phoneNumber = contact.phoneNumbers[0].number
        .replace(sectionPhone[0], 0)
        .replace(/\s/g, '');
    } else {
      phoneNumber = contact.phoneNumbers[0].number.replace(/\s/g, '');
    }

    setShow(true);
    setFirstName(contact.familyName);
    setLastName(contact.givenName);
    setPhone(contact.phoneNumbers[0].number);
  };

  const add_contact = async () => {
    const data = await getData();
    if (data !== null) {
      setAddingFavorites(true);
      const contact = {
        phone_number: phone,
        country_code: +country,
        first_name: firstName,
        last_name: lastName,
      };
      someFunction();
    } else {
      showMessage({
        message: translate('create_account'),
        type: 'danger',
      });
    }
  };

  const someFunction = () => {
    setAddingFavorites(false);

  };

  const goBack = () => {
    props.navigation.goBack();
  };

  const search = searchText => {
    setSearchTerm(searchText);
    if (searchText && searchText.trim() !== '') {
      const contactsFound = contactsClone.filter(contact => {
        return (
          contact.givenName
            .toLowerCase()
            .indexOf(searchText.trim().toLowerCase()) > -1
        );
      });
      setContactsClone(contactsFound);
    } else {
      setContactsClone(contacts);
    }
  };

  return (
    <View style={styles.page}>
      <View>
        <StatusBar barStyle="dark-content" backgroundColor={'#fff'} />
        <Toolbar goBack={goBack} />
        <TextCp textType="bold" style={styles.title}>
          {translate('list_page')}
        </TextCp>
        <TextInput
          style={styles.input}
          onChangeText={text => search(text)}
          placeholder={translate('search')}
          value={searchTerm}
        />
      </View>
      <FlatList
        data={contactsClone}
        style={{flex: 1}}
        renderItem={({item}) => (
          <ContactItem contactClicked={contactClicked} item={item} />
        )}
        numColumns={1}
        keyExtractor={(item, index) => index}
      />
      <Modal
        isVisible={show}
        animationInTiming={200}
        animationOutTiming={200}
        animationOut="fadeOut"
        animationIn="fadeIn"
        statusBarTranslucent={true}
        style={{backgroundColor: 'rgba(154,155,159, .3)', margin: 0}}>
        <View
          style={{
            ...styles.modal,
            bottom: isKeyboardVisible ? keyboardHeight : 0,
          }}>
          <View style={styles.innerContainer}>
            <View style={{justifyContent: 'flex-end', alignItems: 'flex-end'}}>
              <TouchableOpacity onPress={() => setShow(false)}>
                <Icon
                  name="x-circle"
                  style={{fontSize: 35 * scaleRatio}}></Icon>
              </TouchableOpacity>
            </View>
            <View style={{justifyContent: 'center', paddingHorizontal: '10%'}}>
              <TextCp
                textType="bold"
                style={{
                  fontSize: 30 * scaleRatio,
                  textAlign: 'center',
                }}>
                {translate('confirm_add')}
              </TextCp>
            </View>
            <View
              style={{
                paddingVertical: 30,
              }}>
              <KeyboardAvoidingView behavior="position" enabled>
                <TextInput
                  style={[
                    styles.input,
                    {
                      borderWidth: 1,
                      borderColor: firstNameError ? '#BC4749' : 'transparent',
                    },
                  ]}
                  onChangeText={text => {
                    setFirstName(text);
                    setFirstNameError(false);
                  }}
                  placeholder={translate('name_on_id')}
                  value={firstName}
                  returnKeyType="next"
                />
                <TextInput
                  style={[
                    styles.input,
                    {
                      borderWidth: 1,
                      borderColor: lastNameError ? '#BC4749' : 'transparent',
                    },
                  ]}
                  onChangeText={text => {
                    setLastName(text);
                    setLastNameError(false);
                  }}
                  placeholder={translate('first_name_on_id')}
                  value={lastName}
                  returnKeyType="next"
                />

                <RNPickerSelect
                  placeholder={{
                    label: translate('country'),
                    value: translate('country'),
                  }}
                  Icon={() => {
                    return (
                      <Icon
                        name="chevron-down"
                        style={{fontSize: 20}}
                        color="#aaa"
                      />
                    );
                  }}
                  onValueChange={value => {
                    setCountryError(false);
                    setCountry(value);
                  }}
                  items={countries}
                  style={{
                    inputIOS: {
                      fontSize: 16,
                      paddingVertical: 12,
                      paddingHorizontal: 17,
                      color: '#000',
                      paddingRight: 30,
                      height: 45,
                      backgroundColor: 'rgba(0, 0, 0, 0.07)',
                      borderWidth: 1,
                      borderColor: phoneError ? '#BC4749' : 'transparent',
                      borderRadius: 5,
                      // marginBottom: 17,
                    },
                    inputAndroid: {
                      fontSize: 16,
                      paddingHorizontal: 17,
                      paddingVertical: 8,
                      color: '#000',
                      paddingRight: 30,
                      height: 45,
                      backgroundColor: 'rgba(0, 0, 0, 0.07)',
                      borderWidth: 1,
                      borderColor: phoneError ? '#BC4749' : 'transparent',
                      borderRadius: 5,
                      // marginBottom: 17,
                    },
                    iconContainer: {
                      top: 10,
                      right: 15,
                    },
                    placeholder: {
                      color: 'rgba(0,0,0,.31)',
                      fontSize: 15,
                    },
                  }}
                  useNativeAndroidPickerStyle={false}
                  value={country}
                />

                <View style={{flexDirection: 'row'}}>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        borderWidth: 1,
                        borderColor: phoneError ? '#BC4749' : 'transparent',
                        flex: 1,
                      },
                    ]}
                    value={'(+223)'}
                    keyboardType="number-pad"
                    returnKeyType="done"
                  />
                  <TextInput
                    style={[
                      styles.input,
                      {
                        borderWidth: 1,
                        borderColor: phoneError ? '#BC4749' : 'transparent',
                        flex: 6,
                      },
                    ]}
                    onChangeText={text => {
                      setPhoneError(false);
                      setPhone(text);
                    }}
                    placeholder={translate('PhoneNumber')}
                    value={phone}
                    keyboardType="number-pad"
                    returnKeyType="done"
                  />
                </View>

                {!addingFavorites ? (
                  <DanaButton
                    title={`${translate('add')}`}
                    theme="#282828"
                    onPress={() => add_contact()}
                  />
                ) : (
                  <ActivityIndicator size="small" color="#000" />
                )}
                {isKeyboardVisible ? <View style={{height: 140}} /> : null}
              </KeyboardAvoidingView>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  contact: {
    flexDirection: 'row',
    marginBottom: 15 * scaleRatio,
  },

  input: {
    width: '100%',
    height: 45,
    backgroundColor: 'rgba(0, 0, 0, 0.07)',
    borderRadius: 5,
    marginVertical: 5.5 * scaleRatio,
    paddingHorizontal: 17,
  },
  page: {
    paddingHorizontal: pagePadding,
    backgroundColor: '#fff',
    // paddingTop: Platform.OS === 'android' ? 1 : 0,
    flex: 1,
  },
  title: {
    fontSize: 30 * scaleRatio,
    marginBottom: 3 * scaleRatio,
  },
  list: {
    marginVertical: 5 * scaleRatio,
  },
  subTitle: {
    marginBottom: 6 * scaleRatio,
  },
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    paddingHorizontal: 26,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16 * scaleRatio,
  },
  innerContainerTra: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16,
  },
  modalTitle: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginVertical: 10,
    alignItems: 'center',
  },
});

export default ToRecommend;
