import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  Platform,
  StatusBar,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import Heap from '@heap/react-native-heap';
import TextCp from '../components/TextCp';
import {setI18nConfig, translate} from '../utilities/Translate';
import Icon from 'react-native-vector-icons/Feather';
import {pagePadding} from '../theme';
import DanaButton from '../components/DanaButton';
import useDanaStore from '../app_state/store';
import {CustomerIO} from 'customerio-reactnative';
import FastImage from 'react-native-fast-image';
import LottieView from 'lottie-react-native';
import {
  INSTANT_SEPA,
  BANK_TRANSFER,
  BANK_PAYOUT_INTERNAL,
  MOBILE_MONEY_BY_HUB2,
  DELIVERY,
  MFI,
} from './constants';
import CashInMethod from './transfer/CashInMethod';
import CashInMethodComp from './transfer/components/CashInMethodComp';
import {makeDirectTransfer, makeTransfer} from '../apis/Transfers';
import ExternalPaymentsModal from '../components/ExternalPaymentsModal';
import {showMessage} from 'react-native-flash-message';
import useGetLang from '../Hooks/useGetLang';
const customOrder = [INSTANT_SEPA, BANK_TRANSFER];

const TransferSuccess = props => {
  const sendingCountry = useDanaStore(state => state.sendingCountry);
  const {status, message} = props.route.params;
  const lang = useGetLang();
  const updateTransferTimer = useDanaStore(state => state.updateTransferTimer);
  const updateDepositTimer = useDanaStore(state => state.updateDepositTimer);
  const increaseTries = useDanaStore(state => state.increaseTries);
  const activeOperation = useDanaStore(state => state.activeOperation);
  const beneficiary = useDanaStore(state => state.beneficiary);
  const bankTransferType = useDanaStore(state => state.bankTransferType);

  const transfer = useDanaStore(state => state.transfer);
  const [loading, setLoading] = useState(false);
  const [show, setShow] = useState(false);
  const transfer_response = useDanaStore(state => state.transfer_response);
  const saveTransfer = useDanaStore(state => state.saveTransfer);
  const saveTransferResponse = useDanaStore(
    state => state.saveTransferResponse,
  );
  const restoreTransfer = useDanaStore(state => state.restoreTransfer);

  const cashOutMethod = transfer?.cashoutMethod;
  const {navigation} = props;

  const customIban = transfer.selectedIban
    ? transfer?.selectedIban.iban
    : transfer.enteredIban;

  const phone_number = transfer.MMSelectedPhone
    ? transfer?.MMSelectedPhone.phone_number
    : transfer.MMEnteredPhone;

  const isInstantSepa =
    transfer?.cashInMethod?.cash_in_method?.name?.toLowerCase() ===
    'instant sepa';

  const isBankSelected = ['bank_transfer', 'manual_bank_transfer'].includes(
    cashOutMethod?.cashout_method?.payment_type?.name,
  );

  const isNeeroWallet =
    cashOutMethod?.cashout_method?.name?.toLowerCase() === 'wallet by neero';

  const isMobileSelected =
    cashOutMethod?.cashout_method?.payment_type?.name === 'mobile_money';

  const isDelivery =
    cashOutMethod?.cashout_method?.name?.trim().toLowerCase() === DELIVERY;

  const isMicroFinance =
    cashOutMethod?.cashout_method?.name?.trim().toLowerCase() === MFI;

  useEffect(() => {
    setI18nConfig();
  }, []);

  const presentError = errorObject => {
    const response = errorObject?.response?.data;
    if (errorObject?.response?.status === 422) {
      let readableErrors = Object.entries(response.errors)
        .map(([field, messages]) =>
          messages
            .map(message =>
              translate(
                `${message}_${field}`.replace(/\./g, '_').toLowerCase(),
              ),
            )
            .join(', '),
        )
        .join(', ');

      showMessage({
        type: 'danger',
        message: readableErrors,
      });
    } else {
      showMessage({
        type: 'danger',
        message: response.message,
      });
    }
  };

  const makePayment = () => {
    const payload = {
      amount_without_fees_in_euro: transfer.amount_in_euro,
      beneficiary_id: beneficiary.id,
      cashin_method_id: transfer?.cashInMethod?.cash_in_method.id,
      country_code: beneficiary.country.country_code,
      is_escrowed: false,
      is_payment_on_demand: false,
      payment_delivery: isDelivery,
      reason: transfer.reason,
      verify: false,
      institution_id: cashOutMethod?.cash_out_method.payment_provider.id,
      phone_number: beneficiary.phone_number,
    };

    if (
      transfer?.cashInMethod?.cash_in_method?.name.toLowerCase() ===
      'bank transfer'
    ) {
      if (!bankTransferType) {
        showMessage({
          message: translate('bank_transfer_option'),
          type: 'danger',
        });
        return;
      }
    }
    setLoading(true);


    if (cashOutMethod) {
      if (isBankSelected) {
        payload[
          'account_holder'
        ] = `${beneficiary?.first_name} ${beneficiary?.last_name}`;
        payload['bank_name'] = transfer?.bankName?.name;
        payload['iban'] = customIban;
        payload['institution_id'] = transfer?.bankName?.id;
        payload['account_holder'] = beneficiary?.full_name;
        payload['operator'] = transfer?.bankName?.name;
      } else if (isMobileSelected) {
        payload['operator'] = transfer?.MMOperator?.name;
        payload['phone_number'] = phone_number;
        payload['institution_id'] = transfer?.MMOperator?.id;
      } else if (isDelivery) {
        payload['cashout_method_id'] = cashOutMethod?.cashout_method?.id;
        payload['receiver_fullname'] = beneficiary?.full_name;
        payload['receiver_country'] = beneficiary?.country.name;
        payload['receiver_phonenumber'] = beneficiary?.phone_number;
        payload['country_id'] = beneficiary?.country.id;
        payload['local_amount'] = '1000';
        payload['local_currency'] = 'EUR';
      } else if (isMicroFinance) {
        payload['operator'] = 'danapay';
      } else if (isNeeroWallet) {
        payload['cashout_account_id'] = transfer.neeroWallet.account_id;
        payload['cashout_method_id'] = cashOutMethod?.cashout_method?.id;
        payload['operator'] = 'Neero';
      }

      makeDirectTransfer(payload)
        .then(response => {
          if (response) {
            handleResponse(response);
          }
        })
        .catch(error => {
          presentError(error);
          setLoading(false);
        });
    } else {
      payload['sending_country_id'] = sendingCountry.id;
      payload['receiving_country_id'] = beneficiary.country.id;
      makeTransfer(payload)
        .then(response => {
          if (response) {
            handleResponse(response);
          }
        })
        .catch(error => {
          presentError(error);
          setLoading(false);
        });
    }
  };

  const handleResponse = res => {
    setLoading(false);
    const method = transfer?.cashInMethod?.cash_in_method?.name.toLowerCase();
    if (method === 'bank transfer') {
      saveTransferResponse({body: res, nextPage: 'BankerTransfer'});
      navigation.navigate('BankerTransfer');
    } else if (method === 'bank card') {
      saveTransferResponse({body: res, nextPage: 'TransferSuccess'});
      setShow(true);
    } else if (method === 'instant sepa') {
      saveTransferResponse({body: res, nextPage: 'TransferSuccess'});
      setShow(true);
    } else if (method === 'balance') {
      saveTransferResponse({body: res, nextPage: 'TransferSuccess'});
      navigation.navigate('TransferSuccess', {status: 'success'});
    } else if (method === 'mobile money') {
      navigation.navigate('MobileMoney', {mmResponse: res});
    }
  };

  const goBackToHome = () => {
    saveTransferResponse({
      body: null,
      nextPage: '',
    });
    restoreTransfer();
    if (activeOperation === 'transfer') {
      updateTransferTimer('reset');
      increaseTries({type: 'reset', operations: 'transfer'});
      Heap.track('Transfer payment successfully');
      CustomerIO.setProfileAttributes({
        'Transfer Done': true,
      });
      CustomerIO.track('Transfer Submitted');
    } else if (activeOperation === 'transfer') {
      updateDepositTimer('reset');
      increaseTries({type: 'reset', operations: 'deposit'});
      CustomerIO.setProfileAttributes({
        'Deposit Done': true,
      });
      CustomerIO.track('Deposit Submitted');
    }
    props.navigation.navigate('Home');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={'#fff'} />
      {status === 'success' ? (
        <>
          <View
            style={{
              ...styles.content,
              justifyContent: 'center',
              alignItems: 'center',
              paddingHorizontal: 16,
            }}>
            <LottieView
              source={require('../lottie/success.json')}
              autoPlay
              style={{
                alignSelf: 'center',
                width: 80,
                height: 80,
                marginVertical: 20,
              }}
              loop={false}
            />
            <TextCp textType="bold" style={styles.title} align="center">
              {translate('transfer_success')}
            </TextCp>

            {isInstantSepa ? (
              <>
                {lang == 'fr' ? (
                  <TextCp align="center">
                    Votre transfert sera exécuté dans un délai de{' '}
                    <TextCp textType="bold">5mn à 24h</TextCp>
                  </TextCp>
                ) : (
                  <TextCp  align="center">
                    Your transfer will be executed within a timeframe of{' '}
                    <TextCp textType="bold">5 minutes to 24 hours.</TextCp>
                  </TextCp>
                )}
              </>
            ) : (
              <>
                {lang == 'en' ? (
                  <TextCp>
                    Your transfer will be executed{' '}
                    <TextCp textType="bold">immediately</TextCp>.
                  </TextCp>
                ) : (
                  <TextCp>
                    Votre transfert sera exécuté{' '}
                    <TextCp textType="bold">immédiatement</TextCp>
                  </TextCp>
                )}
              </>
            )}
          </View>

          <View
            style={{
              paddingHorizontal: pagePadding,
            }}>
            <DanaButton
              theme="#282828"
              title={translate('home')}
              onPress={goBackToHome}
            />
          </View>
          <View style={{height: 20}} />
        </>
      ) : (
        <>
          <ScrollView
            style={styles.content}
            contentContainerStyle={{paddingHorizontal: 16}}>
            <LottieView
              source={require('../lottie/failure.json')}
              autoPlay
              style={{
                alignSelf: 'center',
                width: 80,
                height: 80,
                marginVertical: 20,
              }}
              loop={false}
            />
            <TextCp align="center" textType="bold" style={styles.title}>
              {translate('payment_failed_title')}
            </TextCp>
            <TextCp style={styles.subTitle} align="center">
              {translate('payment_failed_description')}
            </TextCp>

            <View style={styles.alert}>
              <Icon name="info" color="rgba(51,119,255,.5)" size={25} />
              <TextCp
                color="rgba(1,41,56,.75)"
                style={{flex: 1, marginLeft: 10}}>
                {translate('other_methods')}
              </TextCp>
            </View>

            {sendingCountry.cash_in_methods
              .filter(val =>
                customOrder.includes(
                  val.cash_in_method?.name?.trim().toLowerCase(),
                ),
              )
              .map(cashInMethod => (
                <CashInMethodComp
                  cashInMethod={cashInMethod}
                  key={cashInMethod.id}
                />
              ))}
          </ScrollView>
          <View style={styles.row}>
            <View style={styles.rowCol}>
              <DanaButton
                theme="#fff"
                textColor="#282828"
                title={translate('home')}
                onPress={goBackToHome}
              />
            </View>
            <View style={{width: 50}}></View>
            <View style={styles.rowCol}>
              <DanaButton
                theme="#282828"
                title={translate('pay')}
                onPress={makePayment}
                loading={loading}
              />
            </View>
          </View>
        </>
      )}
      <ExternalPaymentsModal
        show={show}
        navigation={navigation}
        connect_url={transfer_response?.body?.connect_url}
        translate={translate}
        setShow={setShow}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: Platform.OS === 'android' ? 1 : 56,
  },
  title: {
    fontSize: 22,
    textAlign: 'center',
  },
  subTitle: {
    fontSize: 15,
    textAlign: 'center',
  },
  circle: {
    height: 80,
    width: 80,
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    marginVertical: 18,
  },
  toolbar: {
    height: 45,
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
  content: {
    flex: 1,
    paddingVertical: 16,
  },
  alert: {
    backgroundColor: '#DEEEFA',
    padding: 10,
    marginVertical: 16,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingHorizontal: pagePadding,
  },
  rowCol: {
    flex: 1,
  },
});

export default TransferSuccess;
