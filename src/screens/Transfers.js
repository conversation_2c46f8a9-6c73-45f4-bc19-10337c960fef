import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  ActivityIndicator,
  StatusBar,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  Platform,
} from 'react-native';
import TextCp from '../components/TextCp';
import {colors, gs, pagePadding, scaleRatio} from '../theme';
import {setI18nConfig, translate} from '../utilities/Translate';
import TransactionItem from '../components/TransactionItem';
import {getAllUserTransactions, getOperationsDetails} from '../apis/Transfers';
import Icon from 'react-native-vector-icons/Feather';
import {ScrollView} from 'react-native-gesture-handler';
import OperationDetailComp from './transfer/components/OperationDetailComp';
import useDanaStore from '../app_state/store';

const types_map = {
  withdraws: 'cashout',
  deposits: 'cashin',
  'bulk transfers': 'bulk_transfer',
  'instant transfers': 'transfer',
  all: 'all',
};

const operation_types = [
  'all',
  'instant transfers',
  // 'bulk transfers',
  // 'deposits',
  // 'withdraws',
];

const Transfers = props => {
  const [selectedType, setSelectedType] = useState('all');
  const store_all_transfers = useDanaStore(state => state.store_all_transfers);
  const store_fetch_all_transfers = useDanaStore(
    state => state.store_fetch_all_transfers,
  );

  const [transfers_clone, setTransfersClone] = useState([
    ...store_all_transfers,
  ]);
  const [fetching, setFetching] = useState(false);
  const [loading, setLoading] = useState(false);
  const [fetchingMore, setFetchingMore] = useState(false);
  const [endReached, setEndReached] = useState(false);

  const operationModalRef = useRef(null);

  const openOperationModal = () => {
    operationModalRef.current?.open();
  };

  const closeOperationModal = () => {
    operationModalRef.current?.close();
  };

  const pageNumber = useRef(2);

  useEffect(() => {
    setI18nConfig();
  }, []);

  const onEndReachedHandler = async () => {
    if (transfers_clone.length > 0) {
      setEndReached(false);
      setFetchingMore(true);
      try {
        const res = await getAllUserTransactions(pageNumber.current, 8, {
          transferType:
            selectedType === 'all' ? null : types_map[item.toLowerCase()],
        });

        const details = await Promise.all(
          res?.data?.map(async transfer => {
            const _details = await getOperationsDetails(transfer.payment_id);
            return {...transfer, _details};
          }),
        );

        const __details = details?.filter(val => {
          if (
            val._details?.operation_direction?.toLowerCase() === 'receiving' &&
            ['in progress', 'failed', 'aborted', 'action required']?.includes(
              val?._details?.operation_status_en?.toLowerCase(),
            )
          ) {
            return false;
          } else {
            return true;
          }
        });

        if (__details.length === 0) {
          setEndReached(true);
        }

        setTransfersClone(prev => [...prev, ...__details]);
        setFetchingMore(false);
        pageNumber.current = pageNumber?.current + 1;
      } catch (error) {
        setFetchingMore(false);
      }
    }
  };

  const goBack = () => {
    props.navigation.goBack();
  };


  const onRefresh = React.useCallback(async () => {
    store_fetch_all_transfers();
  }, []);

  return (
    <SafeAreaView style={styles.page}>
      <StatusBar barStyle="dark-content" backgroundColor={'#f1f1f1'} />
      <View style={{paddingHorizontal: 16, paddingTop: 16}}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: 16,
          }}>
          <View>
            <TextCp textType="bold" style={styles.title}>
              {translate('transactions')}
            </TextCp>
          </View>

          <TouchableOpacity style={styles.iconButton} onPress={() => goBack()}>
            <Icon name="x-circle" size={24} style={styles.icon} color="#222" />
          </TouchableOpacity>
        </View>
      </View>
      <FlatList
        showsVerticalScrollIndicator={false}
        onRefresh={onRefresh}
        refreshing={fetching}
        data={transfers_clone}
        renderItem={({item}) => (
          <TransactionItem
            value={item}
            openOperationModal={openOperationModal}
          />
        )}
        ListEmptyComponent={
          <>
            {loading ? (
              <View
                style={{
                  height: 400,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <ActivityIndicator size="large" color="#666" />
              </View>
            ) : (
              <View
                style={{
                  height: 400,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <TextCp
                  textType={'bold'}
                  opacity={0.8}
                  style={{fontSize: 18, marginBottom: 4}}>
                  {translate('all_transfer_title')}
                </TextCp>
                <TextCp opacity={0.6}>{translate('all_transfer')}</TextCp>
              </View>
            )}
          </>
        }
        extraData={transfers_clone}
        keyExtractor={(item, index) => item.id || index.toString()}
        onEndReached={onEndReachedHandler}
        onEndReachedThreshold={0}
        ListFooterComponent={
          <>
            {fetching && (
              <View style={styles.footer}>
                <ActivityIndicator color="#4a4a4a" size="large" />
              </View>
            )}
          </>
        }
        style={{
          marginHorizontal: pagePadding,
        }}
      />
      {fetchingMore && (
        <View style={{paddingVertical: 10}}>
          <ActivityIndicator color={colors.primary} size={'small'} />
        </View>
      )}
      {endReached && (
        <View style={{padding: 20}}>
          <TextCp align="center">{translate('end_reached')}</TextCp>
        </View>
      )}
      <OperationDetailComp
        ref={operationModalRef}
        closeOperationModal={() => {
          closeOperationModal();
        }}
        navigation={props.navigation}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  phones: {
    fontSize: 14 * scaleRatio,
    color: 'rgba(0,0,0,.5)',
  },
  contact_details: {
    fontSize: 17 * scaleRatio,
  },
  contact: {
    flexDirection: 'row',
    marginBottom: 15 * scaleRatio,
  },
  bullet: {
    fontSize: 30 * scaleRatio,
    marginRight: 10,
    color: '#eee',
  },
  input: {
    height: 48,
    borderRadius: 5,
    // paddingHorizontal: ,
    flex: 1,
  },
  page: {
    backgroundColor: '#f1f1f1',
    paddingTop: Platform.OS === 'android' ? 1 : 0,
    flex: 1,
  },
  title: {
    fontSize: 24 * scaleRatio,
  },
  list: {
    height: '60%',
    marginVertical: 5 * scaleRatio,
    paddingHorizontal: 2,
  },
  subTitle: {
    marginBottom: 23 * scaleRatio,
  },
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    paddingHorizontal: 26,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16 * scaleRatio,
  },
  innerContainerTra: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16,
  },
  modalTitle: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginVertical: 10,
    alignItems: 'center',
  },
  downloadBtn: {
    backgroundColor: '#000',
    height: 50 * scaleRatio,
    width: 50 * scaleRatio,
    borderRadius: 25 * scaleRatio,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 25,
  },
  downloadIcon: {
    fontSize: 30 * scaleRatio,
    color: '#fff',
  },
  modalMoney: {
    color: '#000000',
    fontSize: 30 * scaleRatio,
  },
  modalheader: {
    color: 'rgba(0, 0, 0, 0.51)',
    fontSize: 14 * scaleRatio,
    marginBottom: 5,
  },
  modalConversion: {
    color: 'rgba(0, 0, 0, 0.5)',
    fontSize: 14 * scaleRatio,
  },
  statusItem: {
    marginBottom: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  scrollLocations: {
    flexDirection: 'row',
    marginLeft: 10,
  },
  leftScroll: {
    flex: 1,
    height: 130 * scaleRatio,
  },
  rightScroll: {
    flex: 1,
    height: 130 * scaleRatio,
  },
  transfersBox: {
    paddingVertical: 5 * scaleRatio,
    paddingHorizontal: 10 * scaleRatio,
    ...gs.boxShadow,
    backgroundColor: '#fff',
    borderRadius: 14,
    marginTop: 10,
    marginBottom: 10,
  },
  status: {
    paddingHorizontal: 14 * scaleRatio,
    backgroundColor: '#fff',
    borderRadius: 10,
    paddingVertical: 8,
    ...gs.boxShadow,
  },
  modalUser: {
    marginBottom: 15,
    paddingHorizontal: 14,
    backgroundColor: '#fff',
    borderRadius: 10,
    paddingVertical: 8,
    ...gs.boxShadow,
  },
  modalTransaction: {
    flexDirection: 'row',
    marginBottom: 15,
    paddingHorizontal: 14,
    backgroundColor: '#fff',
    borderRadius: 10,
    paddingVertical: 8,
    ...gs.boxShadow,
  },
  scrollItem: {
    marginRight: 10,
    paddingHorizontal: 16,
    borderRadius: 100,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#282828',
    marginBottom: 16,
  },
  footer: {
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default Transfers;
