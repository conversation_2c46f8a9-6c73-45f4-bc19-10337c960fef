import React, {useState, useEffect, useMemo} from 'react';
import {View, Text, StyleSheet, StatusBar, Platform} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import TextCp from '../components/TextCp';
import {buttonTextFontSize, blueTextBtnColor, borderRadius} from '../theme';
import {TouchableOpacity} from 'react-native-gesture-handler';
import {setI18nConfig, translate} from '../utilities/Translate';
import {navigateWithNoHistory} from '../utilities/ForgetHistroty';
import {CommonActions} from '@react-navigation/native';
import Offline from '../components/Offline';
import BackgroundImageComponent from '../components/BackgroundImageComponent';
import Config from 'react-native-config';

var pjson = require('../../package.json');

const Tutorial = props => {
  const [activePage, setActivePage] = useState(0);

  const pages = useMemo(
    () => [
      {
        image: require('../images/1.jpg'),
        subtitle: 'tutorial_one',
        title: 'title_one',
      },
      {
        image: require('../images/2.jpg'),
        subtitle: 'tutorial_two',
        title: 'title_two',
      },
      {
        image: require('../images/3.jpg'),
        subtitle: 'tutorial_three',
        title: 'title_three',
      },
    ],
    [],
  );

  const nextButtonClicked = () => {
    const nextPage = activePage + 1;
    if (nextPage === 3) {
      navigateWithNoHistory(props, CommonActions, 'PhoneNumberCheck');
    } else {
      setActivePage(nextPage);
    }
  };

  useEffect(() => {
    setI18nConfig();
  }, []);

  return (
    <>
      <Offline />
      <View style={styles.page}>
        <StatusBar
          translucent={true}
          backgroundColor={'transparent'}
          barStyle={'dark-content'}
        />
        <BackgroundImageComponent imagePath={pages[activePage].image}>
          <LinearGradient
            colors={[
              'rgba(0,0,0,0)',
              'rgba(0,0,0,.75)',
              'rgba(0,0,0,.5)',
              '#000',
            ]}
            style={{padding: 16}}>
            <TextCp
              textType="bold"
              color="#fff"
              style={styles.title}
              align="center">
              {translate(pages[activePage].title)}
            </TextCp>

            <TextCp color="#fff" style={styles.subtitle} align="center">
              {translate(pages[activePage].subtitle)}
            </TextCp>

            <View style={styles.bullets}>
              <Text
                style={[
                  styles.bullet,
                  {color: activePage === 0 ? blueTextBtnColor : '#fff'},
                ]}>
                {'\u2B24'}
              </Text>
              <Text
                style={[
                  styles.bullet,
                  {color: activePage === 1 ? blueTextBtnColor : '#fff'},
                ]}>
                {'\u2B24'}
              </Text>
              <Text
                style={[
                  styles.bullet,
                  {color: activePage === 2 ? blueTextBtnColor : '#fff'},
                ]}>
                {'\u2B24'}
              </Text>
            </View>

            <TouchableOpacity onPress={nextButtonClicked}>
              <View style={styles.button}>
                <TextCp textType="bold" color="#fff">
                  {activePage === 2
                    ? translate('get_started')
                    : translate('next')}
                </TextCp>
              </View>
            </TouchableOpacity>

            <TextCp
              style={{fontSize: 10, marginTop: 10}}
              align="center"
              color="#eee">
              {translate('app_version')} {pjson.version}
              {Config.NODE_ENV !== 'prod' ? `  - ${Config.NODE_ENV}` : ''}
            </TextCp>

            {Platform.OS === 'ios' && <View style={{height: 20}} />}
          </LinearGradient>
        </BackgroundImageComponent>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  page: {
    flex: 1,
    backgroundColor: '#000',
  },
  title: {
    fontSize: 24,
    // marginVertical: 5,
  },
  subtitle: {
    fontSize: 16,
    marginVertical: 5,
    width: '78%',
    alignSelf: 'center',
  },
  bullets: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: 16,
  },
  bullet: {
    fontSize: 10,
    marginHorizontal: 5,
  },
  button: {
    backgroundColor: blueTextBtnColor,
    borderRadius: borderRadius,
    padding: 16,
    textAlign: 'center',
    fontSize: buttonTextFontSize,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  textMenuItem: {},
});

export default Tutorial;
