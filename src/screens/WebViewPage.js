import React from 'react';
import {
  View,
  StatusBar,
  TouchableOpacity,
  Image,
  StyleSheet,
  SafeAreaView,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import TextCp from '../components/TextCp';
import Icon from 'react-native-vector-icons/Feather';
import WebView from 'react-native-webview';

const height = Dimensions.get('screen').height;

const WebViewPage = ({route, navigation}) => {
  const {url: uri} = route.params;
  return (
    <SafeAreaView style={styles.page}>
      <StatusBar barStyle={'dark-content'} backgroundColor={'#fff'} />
      <View style={styles.toolbar}>
        <TextCp></TextCp>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="x-circle" size={30} color={'#282828'} />
        </TouchableOpacity>
      </View>
      <WebView
        source={{uri}}
        renderLoading={() => (
          <View
            style={{
              height: height,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <ActivityIndicator size={'large'} color="#444" />
          </View>
        )}
        startInLoadingState={true}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  page: {
    flex: 1,
    backgroundColor: '#fff',
    height: height,
  },
  toolbar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 45,
    paddingHorizontal: 13,
  },
});

export default WebViewPage;
