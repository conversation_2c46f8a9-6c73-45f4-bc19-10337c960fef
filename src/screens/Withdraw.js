import {
  View,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Dimensions,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import React, {useEffect, useState, useRef} from 'react';
import Icon from 'react-native-vector-icons/Feather';
import Modal from 'react-native-modal';
import {setI18nConfig, translate} from '../utilities/Translate';
import {titleFontSize} from '../theme';
import TextCp from '../components/TextCp';
import DanaButton from '../components/DanaButton';
import useDanaStore from '../app_state/store';
import Input from '../components/Input';
import AccountCard from '../components/AccountCard';
import {
  getAppliedFeesForWithdraws,
  initiateCashOut,
  sendResendCode,
  confirmCashOut,
} from '../apis/Transfers';
import FlashMessage, {showMessage} from 'react-native-flash-message';
import CashLayout from '../components/CashLayout';
import AddAccountModal from '../components/AddAccountModal';
import Heap from '@heap/react-native-heap';
import Offline from '../components/Offline';

const RATE = 655.957;
const {height, width} = Dimensions.get('window');
const keyboardHeight = Keyboard.keyboardHeight;

const Withdraw = ({navigation}) => {
  const sendingCountry = useDanaStore(state => state.sendingCountry);
  const bankAccounts = useDanaStore(state => state.bankAccounts);
  const mmAccounts = useDanaStore(state => state.mmAccounts);
  const getBankAccounts = useDanaStore(state => state.getBankAccounts);
  const getMMAccounts = useDanaStore(state => state.getMMAccounts);
  const user = useDanaStore(state => state.user);
  const token = useDanaStore(state => state.token);
  const currency = useDanaStore(state => state.currency);

  const setWithdrawalPayload = useDanaStore(
    state => state.setWithdrawalPayload,
  );
  const withdrawal_payload = useDanaStore(state => state.withdrawal_payload);

  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState(null);
  const [showOtp, setShowOtp] = useState(false);
  const [otp, setOtp] = useState('');
  const [isKBShowing, setIsKBShowing] = useState(false);
  const [showMethod, setShowMethod] = useState(false);
  const [rate, setRate] = useState(RATE);
  const [showModal, setShowModal] = useState(false);
  const [channel, setChannel] = useState('sms');
  const [sendingCode, setSendingCode] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [show, setShow] = useState(false);
  const [addingAccounting, setAddingAccounting] = useState(false);

  const modalFlash = useRef();

  const startWithDraw = () => {
    try {
      setLoading(true);
      const payload_data = {
        amount_without_fees_in_euro:
          currency.toLowerCase() === 'xof'
            ? +withdrawal_payload?.amount / rate
            : withdrawal_payload?.amount,
        cashout_method_id: withdrawal_payload?.cashout_method?.cashout_method?.id,
        bank_account_id:
          withdrawal_payload?.type.toLowerCase() === 'bank payout'
            ? withdrawal_payload?.account.id
            : null,
        mobile_money_account_id:
          withdrawal_payload?.type?.toLowerCase() !== 'bank payout'
            ? withdrawal_payload?.account?.id
            : null,
        local_amount: withdrawal_payload?.amount * RATE,
        local_currency: currency,
        country_id: sendingCountry?.id,
      };
      initiateCashOut(payload_data, token)
        .then(res => {
          Heap.track('Withdraw successfully initiated', {...payload_data});
          setResponse(res);
          setShowMethod(false);
          setShowOtp(true);
          setLoading(false);
        })
        .catch(error => {
          setLoading(false);
          showMessage({
            message: error.response.data.message || error.message,
            type: 'danger',
          });
        });
    } catch (error) {
      setLoading(false);
    }
  };

  const calculateFees = () => {
    getAppliedFeesForWithdraws(
      {
        euro_amount:
          currency.toLowerCase() === 'xof'
            ? +withdrawal_payload?.amount / rate
            : withdrawal_payload?.amount,
        country_id: sendingCountry?.id,
        cashout_method_id: sendingCountry.cashout_methods[0].id,
      },
      user?.id,
      token,
    )
      .then(response => {
        setWithdrawalPayload({fees: response.fee});
      })
      .catch(error => {});
  };

  const sendCode = () => {
    setSendingCode(true);
    sendResendCode({channel}, response?.cashout.id, token)
      .then(res => {
        Heap.track('Withdraw code sent successfully', {...response});
        setSendingCode(false);
        modalFlash.current.showMessage({
          message: translate('code_sent'),
          type: 'success',
        });
      })
      .catch(error => {
        setSendingCode(false);
        modalFlash.current.showMessage({
          message: error.response.data.message || error.message,
          type: 'danger',
        });
      });
  };

  const checkBalance = () => {
    const {amount, fees} = withdrawal_payload || {};
    const toWithdraw =
      currency.toLowerCase() === 'xof'
        ? +withdrawal_payload?.amount / rate
        : withdrawal_payload?.amount;
    const {euro_balance} = user?.client;
    if (amount.length === 0) {
      showMessage({
        message: translate('pl_enter_amount'),
        type: 'danger',
      });
      return;
    }

    if (parseFloat(euro_balance) < parseFloat(toWithdraw) + fees) {
      showMessage({
        message: translate('little_balance'),
        type: 'danger',
      });
      return;
    }
    setShowModal(true);
    setShowMethod(true);
  };

  const verifyCode = () => {
    const otpData = {
      key_code: otp,
      id: response?.cashout.id,
    };
    setVerifying(true);
    confirmCashOut(otpData, token)
      .then(res => {
        setShowModal(false);
        setVerifying(false);
        Heap.track('Withdraw successfully confirmed', {
          ...(withdrawal_payload || {}),
        });
        navigation.navigate('TransferSuccess', {
          status: 'success',
          message: translate('Withdraw_success_text'),
        });
      })
      .catch(error => {
        setVerifying(false);
        setLoading(false);
        modalFlash.current.showMessage({
          message: error?.response?.data?.message,
          type: 'danger',
        });
      });
  };

  useEffect(() => {
    calculateFees();
  }, [withdrawal_payload?.amount]);

  useEffect(() => {
    setI18nConfig();
    getMMAccounts();
    getBankAccounts();

    const method = sendingCountry?.cashout_methods?.find(
      val => val?.cashout_method?.name?.toLowerCase() === 'bank payout',
    );
    setWithdrawalPayload({cashout_method: method, type: 'bank payout'});
  }, []);

  useEffect(() => {
    const showSubscription = Keyboard.addListener('keyboardDidShow', () => {
      setIsKBShowing(true);
    });
    const hideSubscription = Keyboard.addListener('keyboardDidHide', () => {
      setIsKBShowing(false);
    });

    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: '#f1f1f1'}}>
      <Offline />
      <StatusBar backgroundColor={'#f1f1f1'} barStyle="dark-content" />
      <View style={styles.toolbar}>
        <TextCp style={styles.h1} textType="bold">
          {translate('make_withdraw')}
        </TextCp>
        <TouchableOpacity
          onPress={() => {
            Heap.track('Withdraw canceled', {
              message: 'Clicked the close button on the Withdraw page',
            });
            navigation.goBack();
          }}>
          <Icon name="x-circle" color="#333" size={32} />
        </TouchableOpacity>
      </View>
      <ScrollView
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}>
        <View style={styles.balanceBox}>
          <TextCp style={{marginBottom: 4}}>{translate('yob')}</TextCp>
          <CashLayout
            value={user?.client?.euro_balance}
            color="#037375"
            fontSize={20}
          />
        </View>
        <View style={styles.form}>
          <TextCp>{translate('enter_withdraw_amount')}</TextCp>
          <TextCp>{translate('')}</TextCp>
          <View style={styles.inputBox}>
            <View style={{flex: 1}}>
              {currency.toLowerCase() === 'xof' ? (
                <Input
                  value={withdrawal_payload?.amount}
                  onChange={text => {
                    setWithdrawalPayload({amount: text.replace(/\s/g, '')});
                  }}
                  label={translate('calcMoneyInCFA')}
                  backgroundColor="#eee"
                  keyboardType="number-pad"
                />
              ) : (
                <Input
                  value={withdrawal_payload?.amount}
                  onChange={text => {
                    setWithdrawalPayload({amount: text.replace(/\s/g, '')});
                  }}
                  label={translate('calcMoneyInEUR')}
                  backgroundColor="#eee"
                  keyboardType="number-pad"
                />
              )}
            </View>
            <TouchableOpacity
              style={[
                styles.inputBtn,
                {backgroundColor: '#037375', padding: 13},
              ]}
              onPress={() =>
                setWithdrawalPayload({amount: user?.client?.euro_balance})
              }>
              <TextCp
                style={styles.inputBtnText}
                textType={'bold'}
                color="#fff">
                {translate('withdraw_all')}
              </TextCp>
            </TouchableOpacity>
          </View>
          <View style={styles.row}>
            <TextCp>{translate('applied_fees')}</TextCp>
            <CashLayout
              value={withdrawal_payload?.fees||0}
              color="#444"
              fontSize={13}
            />
          </View>
          <View style={styles.row}>
            <TextCp textType={'bold'}>
              {translate('total_withdraw_amount')}
            </TextCp>
            {withdrawal_payload?.amount === '' ? (
              <CashLayout value={0} color="#037375" fontSize={15} />
            ) : (
              <CashLayout
                value={
                  currency.toLowerCase() === 'xof'
                    ? +withdrawal_payload?.amount / rate
                    : +withdrawal_payload?.amount + withdrawal_payload?.fees||0
                }
                color="#037375"
                fontSize={15}
              />
            )}
          </View>
        </View>
        <DanaButton
          title={translate('next')}
          onPress={checkBalance}
          theme="#282828"
          loading={loading}
        />
      </ScrollView>
      <View></View>
      <Modal
        isVisible={showModal}
        animationIn="slideInUp"
        animationOut="slideOutDown"
        statusBarTranslucent={true}
        onModalHide={() => {
          if (addingAccounting) {
            setShow(true);
          }
        }}
        useNativeDriver={true}
        style={{
          backgroundColor: '#ffffff33',
          margin: 0,
        }}>
        <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
          <>
            {showMethod && (
              <SafeAreaView style={styles.modal}>
                <View style={styles.modalHeader}>
                  <View style={styles.h}>
                    <TextCp style={styles.modalHeaderText} textType={'bold'}>
                      {translate('select_withdraw_method')}
                    </TextCp>
                  </View>

                  <TouchableOpacity
                    onPress={() => {
                      setShowMethod(false);
                      setShowModal(false);
                      setLoading(false);
                    }}>
                    <Icon name="x-circle" color="#333" size={25} />
                  </TouchableOpacity>
                </View>
                <View style={styles.tabs}>
                  {sendingCountry?.cashout_methods.map(val => (
                    <TouchableOpacity
                      key={val.id}
                      style={[
                        styles.tab,
                        {
                          borderBottomColor:
                            withdrawal_payload?.type.toLocaleLowerCase() ===
                            val.cashout_method.name.toLowerCase()
                              ? '#037375'
                              : '#eee',
                          borderBottomWidth: 2,
                        },
                      ]}
                      onPress={() => {
                        setWithdrawalPayload({
                          type: val.cashout_method.name.toLowerCase(),
                          cashout_method: val,
                        });
                      }}>
                      <TextCp>
                        {translate(
                          val.cashout_method.name
                            .split(' ')
                            .join('_')
                            .toLowerCase(),
                        )}
                      </TextCp>
                    </TouchableOpacity>
                  ))}
                </View>
                {withdrawal_payload?.type &&
                  withdrawal_payload.cashout_method && (
                    <View style={{paddingHorizontal: 0}}>
                      <ScrollView
                        showsHorizontalScrollIndicator={false}
                        horizontal
                        contentContainerStyle={styles.scrollView}>
                        <View style={{width: 2}} />
                        {withdrawal_payload?.type.toLowerCase() ===
                          'bank payout' ||
                        withdrawal_payload?.type.toLowerCase() ===
                          'bank payout internal' ? (
                          <>
                            {bankAccounts.map((acc, index) => (
                              <AccountCard
                                key={index}
                                details={acc}
                                type={'bank_accounts'}
                                style={{width: width - 80, marginRight: 20}}
                                refreshList={selected => {
                                  setWithdrawalPayload({
                                    account: selected,
                                  });
                                }}
                                is_active={
                                  withdrawal_payload?.account?.id === acc.id
                                }
                                translate={translate}
                              />
                            ))}
                          </>
                        ) : (
                          <>
                            {mmAccounts.map((acc, index) => (
                              <AccountCard
                                key={index}
                                details={acc}
                                type={'mobile_accounts'}
                                style={{width: width - 80, marginRight: 20}}
                                refreshList={selected => {
                                  setWithdrawalPayload({
                                    account: selected,
                                  });
                                }}
                                is_active={
                                  withdrawal_payload?.account?.id === acc.id
                                }
                                translate={translate}
                              />
                            ))}
                          </>
                        )}
                        <View
                          style={{
                            width: width - 80,
                            marginRight: 20,
                            backgroundColor: '#fff',
                            marginVertical: 8,
                            borderRadius: 8,
                          }}>
                          {withdrawal_payload?.type.toLowerCase() ===
                            'bank payout' ||
                          withdrawal_payload?.type.toLowerCase() ===
                            'bank payout internal' ? (
                            <View
                              style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                paddingHorizontal: 16,
                                marginVertical: 40,
                              }}>
                              <TextCp
                                align="center"
                                textType={'bold'}
                                style={{marginBottom: 4}}>
                                {translate('no_banks')}
                              </TextCp>
                              <TextCp
                                align="center"
                                opacity={0.6}
                                style={{fontSize: 12}}>
                                {translate('no_banks_text')}{' '}
                              </TextCp>
                              <DanaButton
                                onPress={() => {
                                  setShowModal(false);
                                  setAddingAccounting(true);

                                  setWithdrawalPayload({
                                    type: 'bank payout',
                                  });
                                }}
                                title={translate('add_account')}
                                textColor="#f1f1f1"
                              />
                            </View>
                          ) : (
                            <View
                              style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                paddingHorizontal: 16,
                                marginVertical: 40,
                              }}>
                              <TextCp
                                align="center"
                                textType={'bold'}
                                style={{marginBottom: 4}}>
                                {translate('no_mm_accounts')}
                              </TextCp>
                              <TextCp
                                align="center"
                                opacity={0.6}
                                style={{fontSize: 12}}>
                                {translate('no_mm_accounts_text')}{' '}
                              </TextCp>

                              <DanaButton
                                onPress={() => {
                                  setAddingAccounting(true);
                                  setShowModal(false);

                                  setWithdrawalPayload({
                                    type: 'mobile money',
                                  });
                                }}
                                title={translate('add_account')}
                                textColor="#f1f1f1"
                              />
                            </View>
                          )}
                        </View>
                      </ScrollView>
                      <View style={{paddingHorizontal: 16}}>
                        {withdrawal_payload?.account && (
                          <DanaButton
                            title={translate('next')}
                            onPress={
                              withdrawal_payload?.account && startWithDraw
                            }
                            theme="#282828"
                            loading={loading}
                          />
                        )}
                      </View>
                    </View>
                  )}
                <View style={{height: 30}}></View>
              </SafeAreaView>
            )}

            {showOtp && (
              <SafeAreaView
                style={[
                  styles.modal,
                  {
                    backgroundColor: '#fff',
                    bottom: showOtp && isKBShowing ? keyboardHeight : 0,
                  },
                ]}>
                <View style={styles.modalHeader}>
                  <TextCp style={styles.modalHeaderText} textType={'bold'}>
                    {translate('Verify_CashOut')}
                  </TextCp>
                  <TouchableOpacity
                    onPress={() => {
                      setShowOtp(false);
                      setShowModal(false);
                    }}>
                    <Icon name="x-circle" style={styles.icon} size={25}></Icon>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    padding: 15,
                  }}>
                  <TextCp textType={'bold'}>
                    {translate('otp_code_form')}
                  </TextCp>
                  <TouchableOpacity
                    style={[styles.rowSimple]}
                    onPress={() => setChannel('mail')}>
                    <View
                      style={[
                        styles.rowSimpleCheck,
                        {
                          backgroundColor:
                            channel === 'mail' ? '#037375' : '#eee',
                        },
                      ]}></View>
                    <TextCp>{translate('by_email')}</TextCp>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.rowSimple]}
                    onPress={() => setChannel('sms')}>
                    <View
                      style={[
                        styles.rowSimpleCheck,
                        {
                          backgroundColor:
                            channel === 'sms' ? '#037375' : '#eee',
                        },
                      ]}></View>
                    <TextCp>{translate('by_sms')}</TextCp>
                  </TouchableOpacity>
                  <View style={{height: 16}} />
                  <TextCp textType={'bold'}>
                    {translate('enter_otp_code')}
                  </TextCp>
                  <View style={styles.inputBox}>
                    <View style={{flex: 1}}>
                      <Input
                        value={otp}
                        onChange={text => setOtp(text)}
                        label={translate('enter_otp')}
                        backgroundColor="#eee"
                        keyboardType="number-pad"
                      />
                    </View>
                    {sendingCode ? (
                      <TouchableOpacity
                        style={[
                          styles.inputBtn,
                          {backgroundColor: '#037375', padding: 13},
                        ]}>
                        <ActivityIndicator color="#fff" size="small" />
                      </TouchableOpacity>
                    ) : (
                      <TouchableOpacity
                        style={[
                          styles.inputBtn,
                          {backgroundColor: '#037375', padding: 13},
                        ]}
                        onPress={sendCode}>
                        <TextCp
                          style={styles.inputBtnText}
                          color="#fff"
                          textType={'bold'}>
                          {translate('send_code')}
                        </TextCp>
                      </TouchableOpacity>
                    )}
                  </View>

                  <DanaButton
                    title={translate('verify_withdraw')}
                    onPress={verifyCode}
                    theme="#282828"
                    loading={verifying}
                  />
                  <View style={{height: 50}}></View>
                </View>
              </SafeAreaView>
            )}
          </>
        </TouchableWithoutFeedback>
        <FlashMessage ref={modalFlash} position="top" />
      </Modal>
      <AddAccountModal
        show={show}
        setShow={() => {
          setShow(false);
          getMMAccounts();
          getBankAccounts();
        }}
        onModalHide={() => {
          setShowModal(true);
          setAddingAccounting(false);
        }}
        translate={translate}
        type={
          withdrawal_payload?.type?.toLowerCase() === 'mobile money'
            ? 'mobile_accounts'
            : 'bank_accounts'
        }
      />
    </SafeAreaView>
  );
};
//

const styles = StyleSheet.create({
  page: {
    flex: 1,
    backgroundColor: '#f1f1f1',
  },
  toolbar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    height: 56,
    marginVertical: 8,
  },
  h1: {
    fontSize: titleFontSize,
    color: '#222',
  },
  content: {
    padding: 16,
  },
  form: {
    backgroundColor: '#fff',
    elevation: 1,
    paddingVertical: 26,
    paddingHorizontal: 16,
    borderRadius: 10,
    marginVertical: 20,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 10,
  },
  inputBox: {
    backgroundColor: '#eee',
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 10,
    marginVertical: 4,
    borderRadius: 8,
  },
  inputBtn: {
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  inputBtnText: {
    color: '#fff',
    fontSize: 12,
  },
  balanceBox: {
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  balanceBoxAmount: {
    fontSize: 20,
    color: '#4a4a4a',
    marginVertical: 10,
  },
  total: {
    color: '#037375',
  },
  modal: {
    paddingHorizontal: 16,
    backgroundColor: '#f1f1f1',
    bottom: 0,
    left: 0,
    right: 0,
    position: 'absolute',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    minHeight: height / 2,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    marginTop: 10,
  },
  modalHeaderText: {
    fontSize: 19,
    flex: 1,
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    borderBottomColor: '#fff',
    borderBottomWidth: 1,
  },
  tab: {
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  scrollView: {
    marginVertical: 20,
  },
  rowSimple: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rowSimpleCheck: {
    height: 20,
    width: 20,
    borderWidth: 3,
    borderColor: '#fff',
    marginRight: 10,
    marginVertical: 13,
    backgroundColor: '#aaa',
    borderRadius: 10,
  },
  h: {
    width: width * 0.7,
  },
});

export default Withdraw;
