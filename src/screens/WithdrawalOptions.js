import {
  View,
  Text,
  SafeAreaView,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import React, {useMemo} from 'react';
import useDanaStore from '../app_state/store';
import TextCp from '../components/TextCp';
import {translate} from '../utilities/Translate';

const WithdrawalOptions = () => {
  const setWithdrawalOptionsPayload = useDanaStore(
    state => state.setWithdrawalOptionsPayload,
  );
  const widthdrawal_options = useDanaStore(state => state.widthdrawal_options);
  const transfer = useDanaStore(state => state.transfer);
  const countries = useDanaStore(state => state.countries);
  const beneficiary = useDanaStore(state => state.beneficiary);

  const withdrawal_methods = useMemo(() => {
    const beneficiaryCountry = countries.find(
      val => val.country_code === beneficiary.country_code,
    );
    return beneficiaryCountry.cashout_methods;
  }, [countries, beneficiary]);

  return (
    <SafeAreaView>
      <Text>WithdrawalOptions {withdrawal_methods.toString()}</Text>
      <ScrollView>
        <View style={styles.header}>
          <TextCp>{translate('set_withdrawal_method_title')}</TextCp>
          <TextCp>{translate('set_withdrawal_method_body')}</TextCp>
        </View>

        {withdrawal_methods.map(val => {
          <TouchableOpacity onPress={()=>setWithdrawalOptionsPayload({cahs})}>
            <View>
              <TextCp>{val.cashout_method.name}</TextCp>
            </View>
          </TouchableOpacity>;
        })}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    padding: 16,
    justifyContent: '',
  },
});

export default WithdrawalOptions;
