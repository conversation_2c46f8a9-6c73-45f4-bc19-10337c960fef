import React, {useEffect, useState} from 'react';
import {
  StyleSheet,
  StatusBar,
  Keyboard,
  SafeAreaView,
  Platform,
} from 'react-native';
import {CustomerIO} from 'customerio-reactnative';
import Intercom, {Visibility} from '@intercom/intercom-react-native';
import Heap from '@heap/react-native-heap';
import {setI18nConfig, translate} from '../../utilities/Translate';
import {pagePadding} from '../../theme';
import Toolbar from '../../components/Toolbar';
import PinCode from '../../components/PinCode';
import {register} from '../../apis/auth';
import {navigateWithNoHistory} from '../../utilities/ForgetHistroty';
import {CommonActions} from '@react-navigation/native';
import {cleanError} from '../../utilities/cleanerror';
import crashlytics from '@react-native-firebase/crashlytics';
import useDanaStore from '../../app_state/store';
import {showMessage} from 'react-native-flash-message';
import {extractDigits} from '../../utilities';

const CreatePin = props => {
  const heapIOUserId = useDanaStore(state => state.heapIOUserId);
  const setTemp = useDanaStore(state => state.setTemp);
  const temp_user = useDanaStore(state => state.temp_user);
  const saveUser = useDanaStore(state => state.saveUser);
  const saveToken = useDanaStore(state => state.saveToken);
  const increaseTries = useDanaStore(state => state.increaseTries);
  const [loading, setLoading] = useState(false);
  const [pincode, setPinCode] = useState(null);
  const [step, setStep] = useState('login');

  useEffect(() => {
    Keyboard.dismiss();
    setI18nConfig();
  }, []);

  const signUp = confirm_pin => {
    if (confirm_pin !== pincode) {
      showMessage({
        type: 'danger',
        message: translate('pin_didnt_match'),
      });
      return;
    }

    increaseTries({operations: 'onboarding', type: 'reset'});

    const userObj = {
      pincode: confirm_pin,
      heapio_user_id: heapIOUserId,
    };

    setLoading(true);
    register(userObj)
      .then(res => {
        if ('access_token' in res) {
          const registeredUserObj = {
            ...res.customer,
            token: res.access_token,
          };
          intercomInit(res.customer);
          saveUser(registeredUserObj);
          saveToken(res.access_token);
          setTemp({...temp_user, has_pin: true});
          setTimeout(() => {
            setLoading(false);
            CustomerIO.identify(registeredUserObj.external_user_id);
            CustomerIO.setProfileAttributes({
              'Deposit Done': false,
              'Transfer Done': false,
              'Onboarding Done': true,
            });
            setLoading(false);
            navigateWithNoHistory(props, CommonActions, 'Register');
          }, 200);
        }
      })
      .catch(error => {
        setLoading(false);
        crashlytics().recordError(error);
        const err = cleanError(error);
        showMessage({type: 'danger', message: err.all_errors});
        setTimeout(() => {
          props.navigation.goBack();
        }, 100);
      });
  };

  const goBack = () => {
    props.navigation.goBack();
  };

  const intercomInit = userObject => {
    try {
      Intercom.loginUserWithUserAttributes({
        email: userObject?.email
          ? userObject?.email
          : userObject?.full_phone_number,
        userId: userObject?.id,
      });
      Intercom.setLauncherVisibility(Visibility.VISIBLE);
      Intercom.setBottomPadding(Platform.OS === 'ios' ? 0 : 23);
      Heap.identify(userObject?.full_phone_number, 'phone');
    } catch (error) {}
  };

  return (
    <SafeAreaView style={styles.page}>
      <StatusBar barStyle="dark-content" backgroundColor={'#f1f1f1'} />
      <Toolbar goBack={goBack} />
      {step === 'login' && (
        <PinCode
          titleText={
            temp_user?.exists
              ? translate('enter_pin_title')
              : translate('create_pin_title')
          }
          subTitleText={
            temp_user?.exists
              ? translate('enter_pin_desc')
              : translate('create_pin_desc')
          }
          loadingText={translate('logging_in')}
          pinFinished={result => {
            const pin_code = result.join('');
            setPinCode(pin_code);
            setStep('confirm');
          }}
          loading={loading}
        />
      )}
      {step === 'confirm' && (
        <PinCode
          titleText={translate('confirm_pin_title')}
          subTitleText={translate('confirm_pin_desc')}
          loadingText={translate('creating_pin')}
          pinFinished={result => signUp(result.join(''))}
          loading={loading}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  spinnerTextStyle: {
    color: '#FFF',
  },
  page: {
    backgroundColor: '#f1f1f1',
    flex: 1,
    marginHorizontal: pagePadding,
  },
});

export default CreatePin;
