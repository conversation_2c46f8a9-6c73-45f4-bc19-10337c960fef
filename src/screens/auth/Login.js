import React, {useEffect, useState} from 'react';
import {
  StyleSheet,
  StatusBar,
  Keyboard,
  SafeAreaView,
  Alert,
  Platform,
} from 'react-native';
import * as Sentry from '@sentry/react-native';
import {CustomerIO} from 'customerio-reactnative';
import Intercom, {Visibility} from '@intercom/intercom-react-native';
import {setI18nConfig, translate} from '../../utilities/Translate';
import {pagePadding} from '../../theme';
import Toolbar from '../../components/Toolbar';
import PinCode from '../../components/PinCode';
import {loginUser, getLastRegisterationStep} from '../../apis/auth';
import {navigateWithNoHistory} from '../../utilities/ForgetHistroty';
import {CommonActions} from '@react-navigation/native';
import {cleanError} from '../../utilities/cleanerror';
import crashlytics from '@react-native-firebase/crashlytics';
import useDanaStore from '../../app_state/store';
import {showMessage} from 'react-native-flash-message';
import {extractDigits} from '../../utilities';
import Heap from '@heap/react-native-heap';

const Login = props => {
  const temp_user = useDanaStore(state => state.temp_user);
  const heapIOUserId = useDanaStore(state => state.heapIOUserId);
  const saveUser = useDanaStore(state => state.saveUser);
  const saveToken = useDanaStore(state => state.saveToken);
  const setRejectReason = useDanaStore(state => state.setRejectReason);
  const toSummary = useDanaStore(state => state.toSummary);
  const setMissingData = useDanaStore(state => state.setMissingData);
  const updateOnboardingTimer = useDanaStore(
    state => state.updateOnboardingTimer,
  );

  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState('login');
  const [pincode, setPinCode] = useState(null);

  useEffect(() => {
    Keyboard.dismiss();
    setI18nConfig();
  }, []);

  const login = pin => {
    const obj = {
      country_code: temp_user?.country_code,
      phone_number: extractDigits(temp_user?.phone_number),
      pincode: pin,
      heapio_user_id: heapIOUserId,
    };

    setLoading(true);
    loginUser(obj)
      .then(res => {
        if ('access_token' in res) {
          updateOnboardingTimer('reset');
          saveToken(res.access_token);
          const registeredUserObj = {
            ...res.user,
            token: res.access_token,
          };
          intercomInit(res.user);
          saveUser(registeredUserObj);
          // CustomerIO.identify(registeredUserObj?.id);

          Sentry.setUser({
            id: res?.user?.id,
            email: res?.user?.email,
            full_name: res?.user?.first_name + ' ' + res?.user?.last_name,
          });

          // Check if the user is company
          if (!res?.user?.is_individual) {
            setRejectReason('individual');
            navigateWithNoHistory(props, CommonActions, 'Rejected');
            return;
          }

          // Check if the user is rejected
          if (res?.user?.is_rejected) {
            setRejectReason('rejected');
            navigateWithNoHistory(props, CommonActions, 'Rejected');
            return;
          }

          getLastRegisterationStep()
            .then(async registratioProgress => {
              setMissingData(registratioProgress);
              if (
                !registratioProgress?.completed &&
                res?.user?.client?.type === 'temporary-customer'
              ) {
                navigateWithNoHistory(props, CommonActions, 'Register');
              } else {
                if (!res.user?.kyc_submitted_at) {
                  navigateWithNoHistory(props, CommonActions, 'Sumsub');
                  return;
                } else {
                  if (toSummary) {
                    setLoading(false);
                    props.navigation.navigate('Summary');
                  } else {
                    setTimeout(() => {
                      setLoading(false);
                      navigateWithNoHistory(props, CommonActions, 'Home');
                    }, 200);
                  }
                }
              }
            })
            .catch(error => console.log('----', error));
        }
      })
      .catch(error => {
        console.log('...', error);
        setLoading(false);
        crashlytics().recordError(error);
        const err = cleanError(error);
        showMessage({
          type: 'danger',
          message: `${err.all_errors || err.main_message}`,
        });
      });
  };

  const goBack = () => {
    props.navigation.goBack();
  };

  const intercomInit = userObject => {
    try {
      Intercom.loginUserWithUserAttributes({
        email: userObject?.email
          ? userObject?.email
          : userObject?.full_phone_number,
        userId: userObject?.id,
      });
      Intercom.setLauncherVisibility(Visibility.VISIBLE);
      Intercom.setBottomPadding(Platform.OS === 'ios' ? 0 : 23);
      Heap.identify(userObject?.full_phone_number, 'phone');
    } catch (error) {}
  };

  return (
    <SafeAreaView style={styles.page}>
      <StatusBar barStyle="dark-content" backgroundColor={'#f1f1f1'} />
      <Toolbar goBack={goBack} />
      <PinCode
        titleText={
          temp_user?.exists
            ? translate('enter_pin_title')
            : translate('create_pin_title')
        }
        subTitleText={
          temp_user?.exists
            ? translate('enter_pin_desc')
            : translate('create_pin_desc')
        }
        loadingText={translate('logging_in')}
        pinFinished={result => {
          const pin_code = result.join('');
          if (temp_user?.exists) {
            login(pin_code);
          } else {
            setPinCode(pin_code);
            setStep('confirm');
          }
        }}
        loading={loading}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  spinnerTextStyle: {
    color: '#FFF',
  },
  page: {
    backgroundColor: '#f1f1f1',
    flex: 1,
    marginHorizontal: pagePadding,
  },
});

export default Login;
