import React, {useEffect, useState, useRef, useCallback} from 'react';
import {
  View,
  StatusBar,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Image,
  SafeAreaView,
  Keyboard,
  Alert,
  TouchableWithoutFeedback,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import Heap from '@heap/react-native-heap';
import {CustomerIO} from 'customerio-reactnative';
import analytics from '@react-native-firebase/analytics';
import {
  pagePadding,
  borderRadius,
  titleFontSize,
  subtitleFontSize,
  scaleRatio,
} from '../../theme';
import {setI18nConfig, translate} from '../../utilities/Translate';
import TextCp from '../../components/TextCp';
import PhoneInput from '../../components/PhoneInput';
import DanaButton from '../../components/DanaButton';
import {
  checkUser,
  sendCode,
  sendCodeToEmail,
  verifyCode,
} from '../../apis/auth';
import {cleanError} from '../../utilities/cleanerror';
import crashlytics from '@react-native-firebase/crashlytics';
import useDanaStore from '../../app_state/store';
import CountryModals from '../../components/modals/CountryModals';
import OPTCodeModal from '../../components/modals/OPTCodeModal';
import {
  extractDigits,
  isValid,
  spaceWithUnderScore,
  toLowerCaseWordsOnly,
} from '../../utilities';
import {showMessage} from 'react-native-flash-message';
import {CommonActions} from '@react-navigation/native';
import {navigateWithNoHistory} from '../../utilities/ForgetHistroty';
import Config from 'react-native-config';
import {tiktokLogEvents} from '../../utilities/tiktok';

const PhoneNumberCheck = props => {
  const temp_user = useDanaStore(state => state.temp_user);
  const _user = useDanaStore(state => state.user);
  const setTemp = useDanaStore(state => state.setTemp);
  const saveToken = useDanaStore(state => state.saveToken);
  const saveUser = useDanaStore(state => state.saveUser);

  const [resending, setResending] = useState(false);
  const [loadingStatus, setLoadingStatus] = useState({
    checkingUser: false,
    verifyingCode: false,
  });
  const [verificationCode, setVerificationCode] = useState(null);
  const [phoneNumber, setPhoneNumber] = useState(null);
  const [user, setUser] = useState(null);
  const [errors, setError] = useState({
    phone: false,
    verificationCode: false,
  });
  const countries = useDanaStore(state => state.countries);
  const setRejectReason = useDanaStore(state => state.setRejectReason);
  const userCurrentCountry = useDanaStore(state => state.userCurrentCountry);
  const storeHeapIOUserId = useDanaStore(state => state.storeHeapIOUserId);
  const [country, setCountry] = useState(null);
  const [code, setCode] = useState(country?.country_code);
  const increaseTries = useDanaStore(state => state.increaseTries);
  const getAllActivities = useDanaStore(state => state.getAllActivities);
  const [channel, setChannel] = useState('sms');
  const [otpError, setOptError] = useState('');
  const [detecting, setDectecting] = useState(false);

  useEffect(() => {
    setI18nConfig();
  }, []);

  const modalizeRef = useRef(null);

  const modalCountriesRef = useRef(null);

  const closeCountryModal = () => {
    modalCountriesRef.current.close();
  };

  const openCountryModal = () => {
    modalCountriesRef.current.open();
  };

  const onOpen = () => {
    modalizeRef.current?.open();
  };

  const closeModalize = () => {
    modalizeRef.current?.close();
  };

  const checkIfUserExists = () => {
    if (!isValid(country?.country_code, phoneNumber, country?.code)) {
      showMessage({
        type: 'danger',
        message: translate('invalid_phone_number'),
      });
      return;
    }

    Keyboard.dismiss();
    setLoadingStatus({...loadingStatus, checkingUser: true});

    const obj = {
      phone_number: extractDigits(phoneNumber),
      country_code: country?.country_code,
    };

    Heap.identify(`${country?.country_code}${country?.country_code}`);

    checkUser(obj)
      .then(res => {
        // Check if the user is company
        if (res?.user_type !== 'individual') {
          setRejectReason('individual');
          navigateWithNoHistory(props, CommonActions, 'Rejected');
          return;
        }

        // Check if the user is rejected
        if (res?.hasOwnProperty('is_rejected') && res?.is_rejected) {
          setRejectReason('rejected');
          navigateWithNoHistory(props, CommonActions, 'Rejected');
          return;
        }

        setTemp({...res, ...obj});
        sendUserOTP(country?.country_code, extractDigits(phoneNumber));
      })
      .catch(error => {
        if (
          error?.response &&
          !error?.response?.data?.exists &&
          error?.response?.status !== 500
        ) {
          setLoadingStatus({...loadingStatus, checkingUser: false});

          setTemp({...error?.response?.data, ...obj});
          sendUserOTP(country?.country_code, extractDigits(phoneNumber));
        } else {
          setLoadingStatus({...loadingStatus, checkingUser: false});
          const err = cleanError(error);
          showMessage({
            message: error.message,
            type: 'danger',
          });
        }
      });
  };

  const sendUserOTP = (country_code, phone_number, resending = false) => {
    Alert.alert(
      translate('otp_channel'),
      translate('otp_channel_description'),
      [
        {
          text: 'SMS',
          onPress: () => {
            const obj = {
              country_code,
              phone_number,
              channel: 'sms',
            };
            setChannel('sms');
            setResending(true);
            sendCode(obj)
              .then(res => {
                if (resending) {
                  showMessage({
                    type: 'success',
                    message: translate('new_code_sent'),
                  });
                }
                onOpen();
                setLoadingStatus({...loadingStatus, checkingUser: false});
                setResending(false);
              })
              .catch(error => {
                crashlytics().recordError(error);
                const err = cleanError(error);
                setLoadingStatus({...loadingStatus, checkingUser: false});
                setResending(false);
                showMessage({
                  message: translate(toLowerCaseWordsOnly(err.main_message)),
                  type: 'danger',
                });
              });
          },
          style: 'cancel',
        },
        {
          text: 'WhatsApp',
          onPress: () => {
            const obj = {
              country_code,
              phone_number,
              channel: 'whatsapp',
            };
            setChannel('whatsapp');
            setResending(true);
            sendCode(obj)
              .then(res => {
                if (resending) {
                  showMessage({
                    type: 'success',
                    message: translate('new_code_sent'),
                  });
                }
                onOpen();
                setLoadingStatus({...loadingStatus, checkingUser: false});
                setResending(false);
              })
              .catch(error => {
                crashlytics().recordError(error);
                const err = cleanError(error);
                setLoadingStatus({...loadingStatus, checkingUser: false});
                setResending(false);
                showMessage({
                  message: translate(toLowerCaseWordsOnly(err.main_message)),
                  type: 'danger',
                });
              });
          },
        },
      ],
      {cancelable: false},
    );
  };

  const onFulfill = useCallback(code => {
    setVerificationCode(code);
  }, []);

  const handleVerifyCode = useCallback(() => {
    const obj = {
      country_code: temp_user?.country_code,
      phone_number: extractDigits(temp_user?.phone_number),
      verification_code: verificationCode,
      
    };

    if (verificationCode === null) {
      setError({...errors, verificationCode: true});
      showMessage({
        message: translate('code_required'),
        type: 'danger',
      });
      return;
    }

    setLoadingStatus({...loadingStatus, verifyingCode: true});

    verifyCode(obj)
      .then(async res => {
        //loggin in the user here
        if ('access_token' in res) {
          saveToken(res?.access_token);
          const registeredUserObj = {
            ...res.user,
            token: res.access_token,
          };
          saveUser(registeredUserObj);
        }

        // Log sign_up event with detailed properties
        await analytics().logEvent('registration', {
          method: 'phone',
        });
        await tiktokLogEvents('Registration', res.user, {}, Platform.OS);

        if (!temp_user?.exists) {
          setTimeout(() => {
            props.navigation.navigate('CreatePin');
          }, 100);
        } else {
          if (temp_user?.has_pin) {
            props.navigation.navigate('Login');
          } else {
            if (temp_user?.has_email) {
              sendCodeToEmail({
                user_id: temp_user?.user_id,
              })
                .then(res => {
                  props.navigation.navigate('AccountVerification', {
                    user: res.user,
                  });
                })
                .catch(err => {
                  // console.log('sendCodeToEmail', err.response);
                });
            } else {
              props.navigation.navigate('Setpin', {user: res.user});
            }
          }
        }
        setLoadingStatus({...loadingStatus, verifyingCode: false});
      })
      .catch(error => {
        console.log(error);
        setLoadingStatus({...loadingStatus, verifyingCode: false});
        const err = cleanError(error);
        setOptError(translate(spaceWithUnderScore(err.main_message)));
        showMessage({
          type: 'danger',
          message: translate(spaceWithUnderScore(err.main_message)),
        });
      });
  }, [loadingStatus, temp_user, verificationCode]);

  const onCountrySelected = selectedCountry => {
    setCountry(selectedCountry);
    setCode(selectedCountry.code);
    closeCountryModal();
  };

  const detectCountry = async () => {
    const detectedCountry = countries.find(
      country => country?.code?.toLowerCase() === 'fr',
    );

    if (detectedCountry) {
      setCountry(detectedCountry);
      setCode(detectedCountry.code);
      setDectecting(false);
      return;
    }
  };

  useEffect(() => {
    detectCountry();
    storeHeapIOUserId();
    getAllActivities();
  }, []);

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: '#fff',
      }}>
      <StatusBar
        translucent={false}
        barStyle="dark-content"
        backgroundColor={'#fff'}
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.content}>
            <View>
              <TextCp textType="bold" style={styles.h1}>
                {translate('Identity')}
              </TextCp>
              <TextCp>{translate('enter_phone')}</TextCp>
            </View>
            <View style={{flex: 1, paddingVertical: 16}}>
              <FastImage
                style={styles.image}
                source={require('../../images/login.jpg')}
              />
            </View>

            <View>
              <PhoneInput
                openModal={openCountryModal}
                country={country}
                code={code}
                setPhone={phone => setPhoneNumber(phone)}
                phone={phoneNumber}
                error={errors.phone}
                detecting={detecting}
              />

              <DanaButton
                onPress={checkIfUserExists}
                title={translate('validate')}
                theme="#282828"
                loading={loadingStatus.checkingUser}
                testID="loginButton"
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>

      <CountryModals
        country={country}
        onCountrySelected={val => onCountrySelected(val)}
        ref={modalCountriesRef}
        closeModal={() => closeModalize()}
      />

      <OPTCodeModal
        ref={modalizeRef}
        closeModal={() => closeModalize()}
        onFulfill={onFulfill}
        handleVerifyCode={() => handleVerifyCode()}
        loadingStatus={loadingStatus}
        sendUserOTP={() =>
          sendUserOTP(country.country_code, extractDigits(phoneNumber), true)
        }
        resending={resending}
        channel={channel}
        otpError={otpError}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {flex: 1},
  top: {flex: 2, paddingVertical: 20},
  modalContainer: {
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: 'red',
  },
  content: {
    padding: pagePadding,
    flex: 1,
  },
  title: {
    fontSize: 30,
    marginBottom: 17,
    textAlign: 'center',
  },
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    right: 0,
    left: 0,
    padding: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  modalCountry: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    padding: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
  },
  flag: {
    height: 70,
    width: 70,
  },
  image: {
    height: '100%',
    width: '100%',
    borderRadius: borderRadius,
    marginBottom: 10 * scaleRatio,
  },
  h1: {
    fontSize: titleFontSize,
    marginBottom: 7 * scaleRatio,
    textTransform: 'capitalize',
  },
  p: {
    fontSize: subtitleFontSize,
    marginBottom: 18 * scaleRatio,
  },
  subtitleCode: {
    textAlign: 'center',
    color: '#000',
    fontSize: 15,
  },
  titleCode: {
    fontSize: 30,
    color: '#010101',
    textAlign: 'center',
    textTransform: 'capitalize',
    marginBottom: 10,
  },
});

export default PhoneNumberCheck;
