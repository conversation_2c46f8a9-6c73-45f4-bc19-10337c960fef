import React, {useEffect, useState, useMemo, useCallback, useRef} from 'react';
import {
  View,
  StatusBar,
  StyleSheet,
  ScrollView,
  Platform,
  KeyboardAvoidingView,
  TouchableOpacity,
  Image,
  SafeAreaView,
  Alert,
  Linking,
  Dimensions,
} from 'react-native';
import {
  borderRadius,
  titleFontSize,
  subtitleFontSize,
  colors,
} from '../../theme';
import {setI18nConfig, translate} from '../../utilities/Translate';
import Intercom from '@intercom/intercom-react-native';
import Icon from 'react-native-vector-icons/Feather';
import MIcon from 'react-native-vector-icons/MaterialCommunityIcons';
import Heap from '@heap/react-native-heap';
import TextCp from '../../components/TextCp';
import DanaButton from '../../components/DanaButton';
import useDanaStore from '../../app_state/store';
import {showMessage} from 'react-native-flash-message';
import Input from '../../components/Input';
import SelectInput from '../../components/SelectInput';
import {extractYupErrors} from '../../utilities';
import {CustomerIO} from 'customerio-reactnative';
import Divider from '../../components/Divider';
import {
  jobWithOtherSchema,
  jobSchema,
  CountriesSchema,
  hearWitOtherSchema,
  hearWitEventSchema,
  hearSchema,
  hearWitRefSchema,
  registerSchema,
} from '../../validations';
import GooglePlaceSearch from '../../components/GooglePlaceSearch';
import {
  app_logout,
  checkUserNameIfReal,
  editProfile,
  sendCodeToEmail,
  verifyEmailCode,
} from '../../apis/auth';
import useGetLang from '../../Hooks/useGetLang';
import useGetStep from '../../Hooks/useGetStep';
import {navigateWithNoHistory} from '../../utilities/ForgetHistroty';
import {CommonActions} from '@react-navigation/native';
import EmailVerificationModal from '../../components/modals/EmailVerificationModal';
import defaultStyles from '../../defaultStyles';
import FastImage from 'react-native-fast-image';

const keyboardVerticalOffset = Platform.OS === 'ios' ? 40 : 0;
const height = Dimensions.get('screen').height;
const special_HA = ['event', 'other', 'referred'];

const special_HAMapping = {
  event: 'event',
  other: 'other',
  referred: 'referralCode',
};

const Register = props => {
  const [page, setPage] = useState('Personal Information Setting Page');
  const temp_user = useDanaStore(state => state.temp_user);
  const user = useDanaStore(state => state.user);
  const onboardingTimer = useDanaStore(state => state.onboardingTimer);
  const onboardingTries = useDanaStore(state => state.onboardingTries);
  const saveUser = useDanaStore(state => state.saveUser);
  const setMissingData = useDanaStore(state => state.setMissingData);
  const computedStep = useGetStep();
  const getUserCurrentState = useDanaStore(state => state.getUserCurrentState);
  const modalizeRef = useRef(null);

  const onOpen = () => {
    modalizeRef.current?.open();
  };

  const closeModalize = () => {
    modalizeRef.current?.close();
  };

  const updateOnboardingTimer = useDanaStore(
    state => state.updateOnboardingTimer,
  );

  const resetStore = useDanaStore(state => state.resetStore);
  const verifyEmail = useDanaStore(state => state.verifyEmail);
  const setVerifyEmail = useDanaStore(state => state.setVerifyEmail);

  const configuredActivities = useDanaStore(
    state => state.configuredActivities,
  );

  const lang = useGetLang();
  const [verification_code, setVerificationCode] = useState('');
  const [otpError, setOptError] = useState(null);
  const [loadingStatus, setLoadingStatus] = useState({
    resending: false,
    sending: false,
  });
  const setTemp = useDanaStore(state => state.setTemp);
  const countries = useDanaStore(state => state.countries);
  const [rcError, setRCError] = useState('');
  const [acceptedTerms, setAcceptTerms] = useState(
    prev => user?.monthly_revenue || user?.email,
  );

  const [userSelectedCountry, setUserSelectedCountry] = useState(null);
  const [step, setStep] = useState(() => computedStep);
  const [error, setError] = useState(null);
  const [emailTaken, setEmailTaken] = useState(null);
  const [loading, setLoading] = useState(false);

  const postal_code_required = useMemo(() => {
    return (
      userSelectedCountry?.requires_post_code ||
      userSelectedCountry?.requires_post_code === 1 ||
      userSelectedCountry?.country_code[0] !== '2'
    );
  }, [userSelectedCountry]);

  const [user_object, setUserObject] = useState({
    firstname: user?.first_name || '',
    lastname: user?.last_name || '',
    email: user?.email || '',
    monthly_revenue: user?.monthly_revenue || '',
    job: user?.job || '',
    userType: 'individual',
    reason_for_modification: 'Profile set up from the mobile app',
    city: user?.city || '',
    country: '',
    address_line: user?.address_line || '',
    activity_id: user?.activity_id || '',
    receiving_countries_ids: [...new Set(user?.receiving_countries_ids)],
    postal_code: user?.post_code || '',
    event: '',
    referralCode: '',
    other: '',
  });

  const setAvalaibleUserInfo = () => {
    if (
      typeof user?.meta?.user_origin === 'string' &&
      user?.meta?.user_origin?.includes('-')
    ) {
      const [origin, other] = user?.meta?.user_origin?.split('-') || ['', ''];
      const key =
        origin.replace(/^\s+|\s+$/gm, '') === 'referralCode'
          ? 'referred'
          : origin.replace(/^\s+|\s+$/gm, '');
      setUserObject(prev => ({
        ...prev,
        hear_about_us: key,
        [key]: other.replace(/^\s+|\s+$/gm, ''),
      }));
    } else {
      setUserObject(prev => ({
        ...prev,
        hear_about_us: user?.user_origin?.toUpperCase(),
      }));
    }
  };

  const menu = useMemo(
    () => [
      {
        name: translate('discovery'),
        subtitle: translate('discovery_text'),
      },
      {
        name: translate('identity'),
        subtitle: translate('identity_text'),
      },
      {
        name: translate('income'),
        subtitle: translate('income_text'),
      },
      {
        name: translate('sending_country'),
        subtitle: translate('sending_country_text'),
      },
      {
        name: translate('verify_residency'),
        subtitle: translate('verify_residency_text'),
      },
    ],
    [],
  );

  const googlePlacesRef = useRef(null);

  const firstChar = `${user?.country_code}`[0];

  const residencyOptions = useMemo(() => {
    return firstChar === '2'
      ? [
          {
            value: 'WAEMU/CEMAC Person',
            label: 'waemu_cemac_person',
            description: 'waemu_cemac_person_description',
          },
        ]
      : [
          {
            value: 'Non EU Person',
            label: 'non_eu_person',
            icon: 'bag-suitcase-outline',
            description: 'non_eu_person_description',
          },
          {
            value: 'EU Person',
            label: 'eu_person',
            icon: 'home-minus-outline',
            description: 'eu_person_description',
          },
        ];
  }, [user]);

  const presentError = errorObject => {
    const response = errorObject?.response?.data;
    if (errorObject?.response?.status === 422) {
      let readableErrors = Object.entries(response.errors)
        .map(([field, messages]) =>
          messages
            .map(message =>
              translate(
                `${message}_${field}`.replace(/\./g, '_').toLowerCase(),
              ),
            )
            .join(', '),
        )
        .join(', ');
      showMessage({
        type: 'danger',
        message: readableErrors,
      });
    } else {
      showMessage({
        type: 'danger',
        message: response.message,
      });
    }
  };

  const onFulfill = code => {
    setVerificationCode(code);
  };

  const collectUserBioData = useCallback(async () => {
    const _address_line = googlePlacesRef?.current?.getAddressText();
    if (!_address_line) {
      setError({address_line: 'address_is_required'});
      return;
    }

    if (!acceptedTerms) {
      showMessage({
        type: 'danger',
        message: translate('please_accept_terms'),
      });
      return;
    }
    setEmailTaken(null);
    setError(null);
    const results = {
      lastname: user_object?.lastname,
      firstname: user_object?.firstname,
      address_line: user_object?.address_line?.trim(),
      city: user_object?.city?.trim(),
      isEmailRequired:
        userSelectedCountry?.preferred_notification_channel === 'mail',
      isPostalCode: postal_code_required === 1,
    };

    if (userSelectedCountry?.preferred_notification_channel === 'mail') {
      results['email'] = user_object?.email?.trim();
    }

    if (postal_code_required === 1 || postal_code_required) {
      results['postal_code'] = user_object?.postal_code?.trim();
    }

    try {
      await registerSchema.validate(results, {abortEarly: false});
      setLoading(true);

      const _data = {
        last_name: user_object?.lastname,
        first_name: user_object?.firstname,
        address_line: user_object?.address_line?.trim(),
        city: user_object?.city?.trim(),
      };

      if (user_object.email.trim()) {
        _data['email'] = user_object?.email?.trim();
      }

      if (user_object.postal_code) {
        _data['post_code'] = user_object?.postal_code?.trim();
      }

      const full_name = `${user_object?.firstname} ${user_object?.lastname}`;

      checkUserNameIfReal({full_name})
        .then(_response => {
          if (!_response.isRealName) {
            Alert.alert('', translate('wrong_name_desc'), [
              {
                text: translate('wrong_name_cancel'),
                onPress: () => {
                  setLoading(false);
                },
                style: 'cancel',
              },
              {
                text: 'Continuer',
                text: translate('wrong_name_continue'),
                onPress: () => {
                  makeModification(_data, results);
                },
              },
            ]);
          } else {
            makeModification(_data, results);
          }
        })
        .catch(error => {
          console.log(error);
        });
    } catch (error) {
      setError(extractYupErrors(error));
    }
  }, [user_object, userSelectedCountry, acceptedTerms]);

  const makeModification = (_data, _results) => {
    editProfile(_data)
      .then(resposnse => {
        setMissingData({
          completed: resposnse.completed,
          missing_fields: resposnse.missing_fields,
        });

        if (
          resposnse?.should_send_email_verification_code ||
          (resposnse.user?.email_verified_at === null &&
            userSelectedCountry?.preferred_notification_channel === 'mail')
        ) {
          setLoading(false);
          onOpen();
        } else {
          navigateAfterEmailCheck(_results);
          setLoading(false);
        }
        saveUser(resposnse.user);
      })
      .catch(error => {
        presentError(error);
        setLoading(false);
      });
  };

  const collectRevenueData = useCallback(async () => {
    if (!user_object.activity_id) {
      setError({activity_id: 'please_select_your_activity'});
      return;
    }

    if (!user_object.monthly_revenue) {
      setError({monthly_revenue: 'monthly_revenue_is_required'});
      return;
    }

    const results = {
      activity_id: user_object?.activity_id,
      monthly_revenue: user_object?.monthly_revenue,
    };
    try {
      if (user_object.activity_id === 15) {
        results['job'] = user_object?.job;
        await jobWithOtherSchema.validate(results, {abortEarly: false});
      } else {
        await jobSchema.validate(results, {abortEarly: false});
      }
      setError(null);
      setLoading(true);
      editProfile({
        ...results,
      })
        .then(resposnse => {
          setMissingData({
            completed: resposnse.completed,
            missing_fields: resposnse.missing_fields,
          });
          saveUser(resposnse.user);
          Heap.track('Revenue information set');
          setPage('Origin Setting Page');
          setStep(prev => prev + 1);
          setLoading(false);
        })
        .catch(error => {
          presentError(error);

          setLoading(false);
        });
    } catch (error) {
      setError(extractYupErrors(error));
      setLoading(false);
    }
  }, [user_object]);

  const collectHearAboutUs = useCallback(async () => {
    try {
      if (user_object.hear_about_us === 'other') {
        await hearWitOtherSchema.validate(
          {
            hear_about_us: user_object.hear_about_us,
            other: user_object.other?.trim(),
          },
          {abortEarly: false},
        );
      }

      if (user_object.hear_about_us === 'event') {
        await hearWitEventSchema.validate(
          {
            hear_about_us: user_object.hear_about_us,
            event: user_object.event?.trim(),
          },
          {abortEarly: false},
        );
      }

      if (user_object.hear_about_us === 'referred') {
        await hearWitRefSchema.validate(
          {
            hear_about_us: user_object.hear_about_us,
            referralCode: user_object.referralCode,
          },
          {abortEarly: false},
        );
      }

      if (!['referred', 'event', 'other'].includes(user_object.hear_about_us)) {
        await hearSchema.validate(
          {
            hear_about_us: user_object.hear_about_us,
          },
          {abortEarly: false},
        );
      }

      const selectedItem = special_HA.includes(user_object.hear_about_us)
        ? {
            user_origin: special_HAMapping[user_object.hear_about_us],
            [special_HAMapping[user_object.hear_about_us]]:
              user_object[special_HAMapping[user_object.hear_about_us]],
          }
        : {user_origin: user_object.hear_about_us};

      setError(null);
      setLoading(true);

      if (
        special_HAMapping[user_object.hear_about_us]?.toLowerCase() ===
        'referralcode'
      ) {
        selectedItem['user_origin'] = 'Referral';
        selectedItem['referral_code'] = selectedItem.referralCode;
        delete selectedItem.referralCode;
      }

      editProfile(selectedItem)
        .then(resposnse => {
          setMissingData({
            completed: resposnse.completed,
            missing_fields: resposnse.missing_fields,
          });
          saveUser(resposnse.user);
          Heap.addEventProperties({'Origin Type': selectedItem});
          Heap.track('Origin Set');
          setPage('Address Setting Page');
          setStep(prev => prev + 1);
          setLoading(false);
        })
        .catch(error => {
          setLoading(false);
          if (error.response.status === 422) {
            showMessage({
              type: 'danger',
              message: 'Please add origin information',
            });
          }
        });
    } catch (error) {
      console.log(error);
      setLoading(false);
      setError(extractYupErrors(error));
    }
  }, [user_object]);

  const collectPreferences = useCallback(async () => {
    setRCError('');
    if (user_object.receiving_countries_ids.length === 0) {
      setRCError(translate('select_receiving_countries_error'));
      return;
    }
    const results = {
      receivingCountries: user_object.receiving_countries_ids,
    };
    try {
      await CountriesSchema.validate(results, {abortEarly: false});
      setError(null);
      setLoading(true);
      editProfile({
        receiving_countries_ids: user_object.receiving_countries_ids,
      })
        .then(resposnse => {
          setMissingData({
            completed: resposnse.completed,
            missing_fields: resposnse.missing_fields,
          });
          saveUser(resposnse.user);
          setLoading(false);
          Heap.track('Receiving countries set');

          if (firstChar === '2') {
            setUserObject(prev => ({
              ...prev,
              residency: 'WAEMU/CEMAC Person',
            }));
            registerResidency('WAEMU/CEMAC Person');
          } else {
            setStep(prev => prev + 1);
          }
        })
        .catch(error => {
          presentError(error);

          setLoading(false);
        });
    } catch (error) {
      setLoading(false);
      setError(extractYupErrors(error));
    }
  }, [user_object]);

  const registerResidency = (_user_residency = null) => {
    const _payload = {};

    if (_user_residency) {
      _payload['residency'] = _user_residency;
    } else {
      _payload['residency'] = user_object.residency;
    }

    if (!_payload['residency']) {
      showMessage({
        type: 'danger',
        message: translate('select_residency_error'),
      });
      return;
    }

    setLoading(true);
    editProfile(_payload)
      .then(resposnse => {
        setLoading(false);
        setMissingData({
          completed: resposnse.completed,
          missing_fields: resposnse.missing_fields,
        });
        saveUser(resposnse.user);
        setLoading(false);
        Heap.track('Receiving countries set');
        navigateWithNoHistory(props, CommonActions, 'Sumsub');
      })
      .catch(error => {
        setLoading(false);
        setLoading(false);
      });
  };

  const setHABValue = useCallback(value => {
    setUserObject(prev => ({...prev, hear_about_us: value}));
  }, []);

  const findCountry = () => {
    const country = countries.find(
      val => val.country_code === temp_user?.country_code,
    );
    setUserSelectedCountry(country);
  };

  const formatGoogleAddress = address => {
    const postal_code = address?.details?.address_components?.find(
      item =>
        item.types === 'postal_code' || item.types?.includes('postal_code'),
    );

    const city_details = address?.details?.address_components?.find(
      item => item.types === 'locality' || item.types?.includes('political'),
    );

    setUserObject(prev => ({...prev, city: city_details.short_name || ''}));

    setUserObject(prev => ({
      ...prev,
      address_line: address?.details?.name,
    }));

    setUserObject(prev => ({
      ...prev,
      postal_code: postal_code?.long_name || '',
    }));
  };

  const navigateAfterEmailCheck = results => {
    setError(null);
    setTemp({...results});
    setUserObject(prev => ({...prev, ...results}));
    CustomerIO.setProfileAttributes({
      created_at: Math.floor(Date.now() / 1000),
      email: 'email' in results ? user_object.email : null,
      'user type': 'individual',
      mobile: true,
    });
    Heap.track('Identity Information Set');
    setPage('Revenue Setting Page');
    setStep(prev => prev + 1);
  };

  const logout = () => {
    Alert.alert(
      '',
      translate('logoutText'),
      [
        {
          text: translate('no'),
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: translate('yes'),
          onPress: () => {
            app_logout()
              .then(res => {
                try {
                  Intercom.logout();
                } catch (error) {}
                resetStore();
                navigateWithNoHistory(props, CommonActions, 'Tutorial');
              })
              .catch(error => {});
          },
        },
      ],
      {cancelable: false},
    );
  };

  const handleVerifyCode = async () => {
    setOptError(null);
    try {
      setLoadingStatus(prev => ({...prev, sending: true}));
      const response = await verifyEmailCode({
        verification_code,
        email: user_object?.email?.trim(),
        should_update: true,
      });
      setLoadingStatus(prev => ({...prev, sending: false}));
      setVerifyEmail(false);
      setStep(prev => prev + 1);
      closeModalize();
    } catch (error) {
      if (error?.response?.status === 400) {
        setOptError('invalid_code');
      }
      setLoadingStatus(prev => ({...prev, sending: false}));
    }
  };

  const resend = async () => {
    try {
      setLoadingStatus(prev => ({...prev, resending: true}));
      const response = await sendCodeToEmail({user_id: user?.id});
      setLoadingStatus(prev => ({...prev, resending: false}));
    } catch (error) {
      setLoadingStatus(prev => ({...prev, resending: false}));
    }
  };

  useEffect(() => {
    setAvalaibleUserInfo();
    getUserCurrentState();
  }, []);

  useEffect(() => {
    setI18nConfig();
    findCountry();
    setStep(computedStep);
  }, [computedStep]);

  useEffect(() => {
    const timer = setInterval(() => {
      const counter = onboardingTimer + 1;
      updateOnboardingTimer('increment');
      if (counter === 120) {
        updateOnboardingTimer('reset');
        const currentDate = new Date();
        const dateString = currentDate.toLocaleString();
        CustomerIO.track('Registration Unfinished In 20 Mins', {
          userType: 'individual',
          channel: 'mobile',
          email: user_object?.email?.trim(),
          'onboarding start date': dateString,
          'phone number': `${temp_user?.country_code}${temp_user?.phone_number}`,
          'first name': user_object?.firstname?.trim(),
          'last name': user_object?.lastname?.trim(),
          page,
          tries: onboardingTries,
        });
      }
    }, 10000);

    return () => {
      clearInterval(timer);
    };
  }, [onboardingTimer]);

  useEffect(() => {
    if (verifyEmail) {
      onOpen();
    }
  }, [verifyEmail]);

  useEffect(() => {
    getUserCurrentState();
  }, []);

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: '#fff',
      }}>
      <StatusBar barStyle="dark-content" backgroundColor={'#fff'} />
      <View
        style={{
          flexDirection: 'row',
          paddingHorizontal: 16,
          marginTop: 8,
        }}>
        <View style={{flex: 1, marginRight: 30}}>
          <TextCp textType="bold" style={styles.h1}>
            {menu[step].name}
          </TextCp>
          <TextCp style={styles.p}>{menu[step].subtitle}</TextCp>
        </View>

        <TouchableOpacity onPress={logout}>
          <Icon name="x-circle" size={24} color="#333" />
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : ''}
        keyboardVerticalOffset={keyboardVerticalOffset}
        keyboardShouldPersistTaps={true}
        style={{
          flex: 1,
        }}>
        <ScrollView
          contentContainerStyle={{flexGrow: 1, paddingHorizontal: 16}}
          keyboardShouldPersistTaps="handled">
          {step === 0 && (
            <View style={{flexGrow: 1}}>
              <View style={{flex: 1}}>
                <FastImage
                  source={require('../../images/login.jpg')}
                  style={{height: '100%', width: '100%', borderRadius: 8}}
                />
              </View>
              <View>
                <Divider height={10} />
                <SelectInput
                  onValueChange={text => {
                    setHABValue(text);
                  }}
                  items={[
                    {label: 'Linkedin', value: 'Linkedin'},
                    {label: 'Facebook', value: 'Facebook'},
                    {label: 'Google', value: 'Google'},
                    {label: 'Instagram', value: 'Instagram'},
                    {label: 'TikTok', value: 'TikTok'},
                    {
                      label: translate('referred_by_someone'),
                      value: 'referred',
                    },
                    {
                      label: translate('event'),
                      value: 'event',
                    },
                    {label: translate('Other'), value: 'other'},
                  ]}
                  placeholder={{
                    label: translate('select_a_response'),
                    value: '',
                  }}
                  value={user_object.hear_about_us}
                  error={
                    error?.hear_about_us && translate(error?.hear_about_us)
                  }
                />

                <Divider height={8} />

                {user_object.hear_about_us === 'other' && (
                  <Input
                    label={translate('other')}
                    value={user_object.other}
                    onChange={text => {
                      // Sanitize input to prevent SQL injection and code manipulation
                      const sanitizedText = text.replace(
                        /[^a-zA-Z0-9\s.,\-_()\[\]&@!?]/g,
                        '',
                      );
                      setUserObject(prev => ({...prev, other: sanitizedText}));
                    }}
                    error={error?.other && translate(error?.other)}
                    backgroundColor="#fff"
                  />
                )}

                {user_object.hear_about_us === 'event' && (
                  <Input
                    label={translate('event_name')}
                    value={user_object.event}
                    onChange={text => {
                      // Sanitize input to prevent SQL injection and code manipulation
                      const sanitizedText = text.replace(
                        /[^a-zA-Z0-9\s.,\-_()\[\]&@!?]/g,
                        '',
                      );
                      setUserObject(prev => ({...prev, event: sanitizedText}));
                    }}
                    error={error?.event && translate(error?.event)}
                    backgroundColor="#fff"
                  />
                )}

                {user_object.hear_about_us === 'referred' && (
                  <Input
                    label={translate('referralCode')}
                    value={user_object.referralCode}
                    onChange={text => {
                      // Sanitize input to prevent SQL injection and code manipulation
                      const sanitizedText = text.replace(
                        /[^a-zA-Z0-9\s.,\-_()\[\]&@!?]/g,
                        '',
                      );
                      setUserObject(prev => ({
                        ...prev,
                        referralCode: sanitizedText,
                      }));
                    }}
                    error={
                      error?.referralCode && translate(error?.referralCode)
                    }
                    backgroundColor="#fff"
                  />
                )}
              </View>
            </View>
          )}

          {step === 1 && (
            <View style={{flexGrow: 1}}>
              <View style={{flex: 1, marginBottom: 16}}>
                <FastImage
                  source={require('../../images/login.jpg')}
                  style={{height: '100%', width: '100%', borderRadius: 8}}
                />
              </View>
              <View>
                <Input
                  label={translate('firstname')}
                  value={user_object.firstname}
                  onChange={text =>
                    setUserObject(prev => ({...prev, firstname: text}))
                  }
                  showLabel={false}
                  error={error?.firstname && translate(error?.firstname)}
                  backgroundColor="#fff"
                />

                <Divider height={12} />

                <Input
                  label={translate('lastname')}
                  value={user_object.lastname}
                  onChange={text =>
                    setUserObject(prev => ({...prev, lastname: text}))
                  }
                  showLabel={false}
                  error={error?.lastname && translate(error?.lastname)}
                  backgroundColor="#fff"
                />

                <Divider height={12} />

                <Input
                  label={`${translate('email')} ${
                    userSelectedCountry?.preferred_notification_channel ===
                    'sms'
                      ? `[ ${translate('Optional')} ]`
                      : '*'
                  }`}
                  value={user_object.email}
                  onChange={text => {
                    setUserObject(prev => ({...prev, email: text}));
                  }}
                  showLabel={false}
                  error={
                    (error?.email ? translate(error?.email) : '') ||
                    (emailTaken ? translate(emailTaken) : '')
                  }
                  backgroundColor="#fff"
                  keyboardType="email-address"
                  inputMode="email"
                />
                <Divider height={10} />

                <GooglePlaceSearch
                  onChange={val => formatGoogleAddress(val)}
                  address_line={user_object?.address_line}
                  error={
                    error?.address_line ? translate(error?.address_line) : ''
                  }
                  ref={googlePlacesRef}
                />
                <Divider height={10} />
                <Input
                  label={translate('city')}
                  value={user_object.city}
                  onChange={text =>
                    setUserObject(prev => ({...prev, city: text}))
                  }
                  showLabel={false}
                  error={error?.city && translate(error?.city)}
                  backgroundColor="#fff"
                />

                <Divider height={12} />

                <Input
                  label={
                    postal_code_required
                      ? translate('postal_code')
                      : translate('postal_code_optional')
                  }
                  value={user_object.postal_code}
                  onChange={text =>
                    setUserObject(prev => ({...prev, postal_code: text}))
                  }
                  showLabel={false}
                  error={error?.postal_code && translate(error?.postal_code)}
                  backgroundColor="#fff"
                />

                <Divider height={8} />
                <View style={styles.terms}>
                  <TouchableOpacity
                    onPress={() => setAcceptTerms(prev => !prev)}>
                    <View
                      style={{
                        ...styles.box,
                        borderColor: acceptedTerms ? '#037375' : '#ddd',
                        flexDirection: 'row',
                        justifyContent: acceptedTerms
                          ? 'flex-end'
                          : 'flex-start',
                      }}>
                      <View
                        style={{
                          ...styles.innerBox,
                          backgroundColor: acceptedTerms ? '#037375' : '#ddd',
                        }}
                      />
                    </View>
                  </TouchableOpacity>

                  <View style={styles.termsText}>
                    <TouchableOpacity
                      onPress={() =>
                        Linking.openURL('https://www.danapay.io/conditions')
                      }>
                      {lang === 'fr' ? (
                        <TextCp>
                          J'accepte les{' '}
                          <TextCp color="#037375" textType={'bold'}>
                            conditions générale
                          </TextCp>
                        </TextCp>
                      ) : (
                        <TextCp>
                          I accept the general{' '}
                          <TextCp color="#037375" textType={'bold'}>
                            Terms & Conditions
                          </TextCp>
                        </TextCp>
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
              <Divider height={10} />
            </View>
          )}

          {step === 2 && (
            <View style={{flexGrow: 1}}>
              <View style={{flex: 1}}>
                <FastImage
                  source={require('../../images/login.jpg')}
                  style={{height: '100%', width: '100%', borderRadius: 8}}
                />
              </View>
              <View>
                <Divider height={10} />
                <SelectInput
                  onValueChange={text =>
                    setUserObject(prev => ({...prev, activity_id: text}))
                  }
                  items={configuredActivities.map(val => ({
                    value: val.id,
                    label: translate(val.slug),
                  }))}
                  placeholder={{
                    label: translate('activity_or_employment'),
                    value: '',
                  }}
                  value={user_object.activity_id}
                  error={error?.activity_id && translate(error?.activity_id)}
                />
                {user_object.activity_id === 15 && (
                  <>
                    <Divider height={8} />
                    <Input
                      label={translate('other_job_type')}
                      value={user_object.job}
                      onChange={text =>
                        setUserObject(prev => ({...prev, job: text}))
                      }
                      showLabel={false}
                      error={error?.job && translate(error?.job)}
                      backgroundColor="#fff"
                    />
                  </>
                )}

                <SelectInput
                  onValueChange={text =>
                    setUserObject(prev => ({...prev, monthly_revenue: text}))
                  }
                  items={[
                    {label: '0 - 1400€', value: '0 - 1400€'},
                    {label: '1400€ - 2500€', value: '1400€ - 2500€'},
                    {label: '2500€ - 4000€', value: '2500€ - 4000€'},
                    {label: '> 4000€', value: '> 4000€'},
                  ]}
                  placeholder={{
                    label: translate('monthly_income_range'),
                    value: '',
                  }}
                  value={user_object.monthly_revenue}
                  error={
                    error?.monthly_revenue && translate(error?.monthly_revenue)
                  }
                />
              </View>
            </View>
          )}

          {step === 3 && (
            <View style={{flexGrow: 1}}>
              <View style={{flex: 1}}>
                <FastImage
                  source={require('../../images/login.jpg')}
                  style={{height: '100%', width: '100%', borderRadius: 8}}
                />
              </View>
              <View>
                <Divider height={16} />
                <SelectInput
                  onValueChange={text => {
                    setUserObject(prev => ({
                      ...prev,
                      receiving_countries_ids: text,
                    }));
                  }}
                  items={countries.map(val => ({
                    label: val.name,
                    value: '' + val.id,
                  }))}
                  isMultiple={true}
                  placeholder={translate('select_one_or_more_countries')}
                  value={user_object.receiving_countries_ids}
                  error={
                    (error?.receivingCountries &&
                      translate(error?.receivingCountries)) ||
                    rcError
                  }
                />
              </View>
            </View>
          )}

          {step === 4 && (
            <View style={{flexGrow: 1}}>
              <View style={{flex: 1}}>
                <FastImage
                  source={require('../../images/login.jpg')}
                  style={{height: '100%', width: '100%', borderRadius: 8}}
                />
              </View>
              <View>
                <Divider height={10} />
                {residencyOptions.map(item => (
                  <TouchableOpacity
                    onPress={() =>
                      setUserObject(prev => ({
                        ...prev,
                        residency: item.value,
                      }))
                    }
                    style={[
                      styles.menuItem,
                      {
                        borderWidth: StyleSheet.hairlineWidth,
                        borderColor:
                          item.value === user_object.residency
                            ? colors.primary
                            : '#fff',
                        ...defaultStyles.boxShow,
                        shadowColor:
                          item.value === user_object.residency
                            ? colors.primary
                            : '#000',
                      },
                    ]}>
                    <View style={styles.menuItemRow}>
                      <MIcon
                        name={item.icon}
                        size={32}
                        color={
                          item.value === user_object.residency
                            ? '#037375'
                            : '#282828'
                        }
                        style={{marginRight: 10}}
                      />
                      <View style={{flex: 1}}>
                        <TextCp textType={'bold'}>
                          {translate(item.label)}
                        </TextCp>
                      </View>
                    </View>
                    {user_object.residency &&
                      item.value === user_object.residency && (
                        <View style={styles.menuItemRowDetails}>
                          <TextCp>{translate(item.description)}</TextCp>
                        </View>
                      )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
      <View style={styles.bottomButtons}>
        {step === 0 && (
          <View style={styles.bottomButtonsRow}>
            <DanaButton
              theme="#282828"
              title={translate('next')}
              onPress={collectHearAboutUs}
              loading={loading}
            />
          </View>
        )}
        {step === 1 && (
          <View style={styles.bottomButtonsRow}>
            <View style={{flex: 1}}>
              <DanaButton
                theme="#ffff"
                textColor="#282828"
                title={translate('return')}
                onPress={() => setStep(prev => prev - 1)}
                // loading={loading}
              />
            </View>
            <View style={{width: 32}} />

            <View style={{flex: 1}}>
              <DanaButton
                theme="#282828"
                title={translate('next')}
                onPress={collectUserBioData}
                loading={loading}
              />
            </View>
          </View>
        )}
        {step === 2 && (
          <View style={styles.bottomButtonsRow}>
            <View style={{flex: 1}}>
              <DanaButton
                theme="#ffff"
                textColor="#282828"
                title={translate('return')}
                onPress={() => setStep(prev => prev - 1)}
              />
            </View>
            <View style={{width: 32}} />

            <View style={{flex: 1}}>
              <DanaButton
                theme="#282828"
                title={translate('next')}
                onPress={collectRevenueData}
                loading={loading}
              />
            </View>
          </View>
        )}
        {step === 3 && (
          <View style={styles.bottomButtonsRow}>
            <View style={{flex: 1}}>
              <DanaButton
                theme="#ffff"
                textColor="#282828"
                title={translate('return')}
                onPress={() => setStep(prev => prev - 1)}
                // loading={loading}
              />
            </View>
            <View style={{width: 32}} />

            <View style={{flex: 1}}>
              <DanaButton
                theme="#282828"
                title={translate('next')}
                onPress={collectPreferences}
                loading={loading}
              />
            </View>
          </View>
        )}
        {step === 4 && (
          <View style={styles.bottomButtonsRow}>
            <View style={{flex: 1}}>
              <DanaButton
                theme="#ffff"
                textColor="#282828"
                title={translate('return')}
                onPress={() => setStep(prev => prev - 1)}
                // loading={loading}
              />
            </View>
            <View style={{width: 16}} />
            <View style={{flex: 1}}>
              <DanaButton
                theme="#282828"
                title={translate('next')}
                onPress={() => registerResidency()}
                loading={loading}
              />
            </View>
          </View>
        )}
      </View>
      <EmailVerificationModal
        ref={modalizeRef}
        onFulfill={onFulfill}
        handleVerifyCode={handleVerifyCode}
        loadingStatus={loadingStatus}
        resend={resend}
        otpError={otpError}
        email={user_object?.email?.trim()}
        changeEmail={() => {
          closeModalize();
          setVerifyEmail(true);
        }}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  terms: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
  },
  box: {
    height: 25,
    width: 45,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    paddingHorizontal: 2,
  },
  innerBox: {
    height: 20,
    width: 20,
    borderRadius: 15,
  },
  termsText: {
    flex: 1,
    marginLeft: 16,
  },
  container: {flex: 1},
  top: {flex: 2, paddingVertical: 20},
  bottom: {
    flex: 1,
    paddingVertical: 20,
    justifyContent: 'center',
    alignContent: 'center',
  },
  modalContainer: {
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: 'red',
  },
  title: {
    fontSize: 16,
    marginBottom: 10,
    textAlign: 'center',
  },
  modal: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    padding: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  innerContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignContent: 'center',
    flex: 1,
    width: '100%',
  },
  flag: {
    height: 100,
    width: 100,
  },
  countryBox: {
    marginHorizontal: 16,
    marginVertical: 25,
  },
  image: {
    height: 150,
    // flex: 1,
    width: '100%',
    borderRadius: borderRadius,
    marginBottom: 0,
  },
  h1: {
    fontSize: titleFontSize,
    marginBottom: 7,
    textTransform: 'capitalize',
  },
  p: {
    fontSize: subtitleFontSize,
    marginBottom: 11,
  },
  subtitleCode: {
    textAlign: 'center',
    color: '#444',
  },
  titleCode: {
    fontSize: 26,
    color: '#010101',
    textAlign: 'center',
    textTransform: 'capitalize',
    marginBottom: 10,
  },
  input: {
    width: '100%',
    height: 50,
    backgroundColor: 'rgba(0, 0, 0, 0.07)',
    borderRadius: 5,
    marginBottom: 17,
    paddingHorizontal: 17,
  },
  bottomButtons: {
    paddingBottom: 16,
  },
  bottomButtonsRow: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  menuItemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  menuItemRowBtn: {
    height: 25,
    justifyContent: 'center',
    alignItems: 'center',
    width: 25,
    marginHorizontal: 10,
    borderRadius: 4,
  },
  menuItemRowDetails: {
    marginTop: 6,
  },
  menuItem: {
    borderRadius: 10,
    marginBottom: 10,
    padding: 10,
    backgroundColor: '#fff',
    // minHeight: 90,
  },
});
export default Register;
