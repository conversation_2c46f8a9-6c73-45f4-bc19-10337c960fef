import {View, Text, StyleSheet, SafeAreaView, StatusBar} from 'react-native';
import React from 'react';
import useDanaStore from '../../app_state/store';
import {translate} from '../../utilities/Translate';
import Intercom from '@intercom/intercom-react-native';
import TextCp from '../../components/TextCp';
import {app_logout} from '../../apis/auth';
import Danabutton from '../../components/DanaButton';
import {CommonActions} from '@react-navigation/native';
import {navigateWithNoHistory} from '../../utilities/ForgetHistroty';
import LottieView from 'lottie-react-native';
import Divider from '../../components/Divider';

const Rejected = props => {
  const rejectedReason = useDanaStore(state => state.rejectedReason);
  const resetStore = useDanaStore(state => state.resetStore);
  const token = useDanaStore(state => state.token);
  const [loading, setLoading] = React.useState(false);

  const logout = () => {
    setLoading(true);
    app_logout(token)
      .then(res => {
        try {
          Intercom.logout();
          resetStore();
          navigateWithNoHistory(props, CommonActions, 'Tutorial');
          setLoading(false);
        } catch (error) {}
      })
      .catch(error => {
        setLoading(false);
      });
  };

  return (
    <SafeAreaView style={styles.page}>
      <StatusBar barStyle="dark-content" backgroundColor={'#fff'} />
      <View style={styles.content}>
        <LottieView
          source={require('../../lottie/failure.json')}
          autoPlay
          style={{
            alignSelf: 'center',
            width: 80,
            height: 80,
            marginVertical: 20,
          }}
          loop={false}
        />
        <TextCp align="center" style={{fontSize: 16}}>
          {rejectedReason === 'rejected'
            ? translate('rejected')
            : translate('isIndividual')}
        </TextCp>
      </View>
      <View style={{ padding:20}}>
        <Danabutton
          theme="#282828"
          title={translate('okay')}
          onPress={logout}
          loading={loading}
        />
        <Divider height={16} />
        <TextCp align="center" textType={'semiBold'}>
          Danapay
        </TextCp>
        <TextCp align="center" style={{fontSize: 10}}>
          © {new Date().getFullYear()} Cartezi Technology.
        </TextCp>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  page: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
});

export default Rejected;
