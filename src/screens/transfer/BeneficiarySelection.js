import {
  View,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import React, {useCallback, useMemo, useState, useRef, useEffect} from 'react';
import TextCp from '../../components/TextCp';
import ContactList from '../../components/ContactList';
import SelectInput from '../../components/SelectInput';
import Input from '../../components/Input';
import UserModal from './components/UserModal';
import {checkContactExists} from '../../apis/favorites';
import {translate} from '../../utilities/Translate';
import useDanaStore from '../../app_state/store';
import {useFocusEffect} from '@react-navigation/native';
import defaultStyles from '../../defaultStyles';
import AddContactModal from '../../components/modals/AddContactModal';
import {showMessage} from 'react-native-flash-message';
import {extractDigits, isValid} from '../../utilities';

const BeneficiarySelection = ({navigation}) => {
  const state_countries = useDanaStore(state => state.countries);
  const user = useDanaStore(state => state.user);
  const fetchContacts = useDanaStore(state => state.fetchContacts);
  const contacts = useDanaStore(state => state.contacts);
  const beneficiary = useDanaStore(state => state.beneficiary);
  const saveBeneficiary = useDanaStore(state => state.saveBeneficiary);
  const removeBeneficiary = useDanaStore(state => state.removeBeneficiary);
  const [processing, setProcessing] = useState(false);
  const modalizeRef = useRef(null);
  const userModalRef = useRef(null);

  const openUserModel = () => {
    userModalRef.current?.open();
  };

  const closeUserModel = () => {
    removeBeneficiary();
    fetchContacts();
    userModalRef.current?.close();
  };

  const onOpen = () => {
    modalizeRef.current?.open();
  };

  const closeModalize = () => {
    modalizeRef.current?.close();
  };

  const countryArray = useMemo(() => {
    return state_countries.map(_country => ({
      label: _country.name,
      value: _country.code,
    }));
  }, [state_countries]);

  const goToContacts = () => {
    navigation.navigate('Contact');
  };

  const searchForUser = () => {
    if (!beneficiary?.phone_number || beneficiary?.phone_number?.length === 0) {
      showMessage({
        type: 'danger',
        message: translate('phone_number_missing'),
      });
      return;
    }
    if (
      !isValid(
        beneficiary?.country?.country_code,
        extractDigits(beneficiary?.phone_number),
        beneficiary?.country?.code,
      )
    ) {
      showMessage({
        type: 'danger',
        message: translate('invalid_phone_number'),
      });
      return;
    }

    setProcessing(true);
    checkContactExists({
      phone_number: extractDigits(beneficiary?.phone_number),
      country_code: beneficiary?.country?.country_code,
    })
      .then(response => {
        setProcessing(false);

        if (response.exists) {
          saveBeneficiary({
            ...response.customer,
            country: state_countries.find(
              val => val.country_code === response?.customer?.country_code,
            ),
          });
          openUserModel();
        } else {
          saveBeneficiary({
            phone_number: extractDigits(beneficiary?.phone_number),
            country_code: beneficiary?.country?.country_code,
          });
          onOpen();
        }
      })
      .catch(error => {
        setProcessing(false);
        showMessage({
          type: 'danger',
          message: translate(error.response.data.message),
        });
      });
  };

  const startTransferFromUser = selected_user => {
    const {favorite} = selected_user;

    const sending_country = state_countries.find(
      val => val.country_code === user?.country_code,
    );
    if (!sending_country) {
      return;
    }

    const country_object = sending_country?.receiving_countries?.find(
      val =>
        val.receiving_country.name.toLowerCase() ===
        favorite.country.toLowerCase(),
    );

    if (!country_object) {
      showMessage({
        type: 'danger',
        message: `${favorite.country} ${translate('not_receiving_country')} ${
          user?.country
        }`,
      });
      return;
    }

    saveBeneficiary({...favorite, country: country_object.receiving_country});
  };

  const handleNextClicked = () => {
    navigation.navigate('PayInfo');
  };

  const onCountrySelected = value => {
    const selectedCountry = state_countries.find(
      val => val.code.toLowerCase() === value.toLowerCase(),
    );
    saveBeneficiary({country: selectedCountry});
  };

  useFocusEffect(
    useCallback(() => {
      fetchContacts();
    }, []),
  );

  useEffect(() => {
    if (beneficiary === null) {
      saveBeneficiary({country: null});
    }
  }, [beneficiary]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />
      <View style={defaultStyles.toolbar}>
        <View style={defaultStyles.toolbarLeft}>
          <TextCp style={defaultStyles.title} textType={'bold'}>
            {translate('beneficiary_title')}
          </TextCp>
        </View>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="x-circle" size={26} color={'#000'} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={{...styles.section}}>
          <TextCp style={defaultStyles.subTitle}>
            {translate('beneficiary_subTitle')}
          </TextCp>
          <ContactList
            contacts={contacts}
            goToContacts={goToContacts}
            startTransfer={startTransferFromUser}
          />
        </View>
        <View style={styles.section}>
          <TextCp style={defaultStyles.subTitle}>
            {translate('beneficiary_subTitle_form')}
          </TextCp>

          <SelectInput
            placeholder={{
              label: translate('beneficiary_select_country'),
              value: translate('country'),
            }}
            items={countryArray}
            onValueChange={value => onCountrySelected(value)}
            value={beneficiary ? beneficiary?.country?.code : null}
          />

          <View
            style={{flexDirection: 'row', alignItems: 'center', marginTop: 8}}>
            {beneficiary?.country && (
              <View style={styles.countryCode}>
                {beneficiary?.country ? (
                  <TextCp style={styles.countryCodeText}>{`+${
                    beneficiary?.country?.country_code || ''
                  }`}</TextCp>
                ) : (
                  <TextCp style={{...styles.countryCodeText}} color="#aaa">
                    +00
                  </TextCp>
                )}
              </View>
            )}

            <View style={{flex: 1}}>
              <Input
                onChange={text => saveBeneficiary({phone_number: text})}
                label={translate('phone_number')}
                value={beneficiary?.phone_number}
                keyboardType="number-pad"
                backgroundColor="#fff"
              />
            </View>
          </View>
        </View>
      </ScrollView>

      <View style={defaultStyles.footerWithButtons}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            ...defaultStyles.button,
            borderColor: '#eee',
            backgroundColor: '#fff',
          }}>
          <TextCp color="#000" textType={'bold'}>
            {translate('return')}
          </TextCp>
        </TouchableOpacity>
        {processing ? (
          <View style={defaultStyles.button}>
            <ActivityIndicator color="#fff" size="small" />
          </View>
        ) : (
          <TouchableOpacity
            onPress={() => searchForUser()}
            style={{
              ...defaultStyles.button,
              borderColor: '#eee',
            }}>
            <TextCp color="#fff" textType={'bold'}>
              {translate('continue')}
            </TextCp>
          </TouchableOpacity>
        )}
      </View>

      <UserModal
        handleBack={() => closeUserModel()}
        handleNext={handleNextClicked}
        ref={userModalRef}
        closeUserModel={closeUserModel}
      />

      <AddContactModal
        closeModalize={() => closeModalize()}
        details={{
          phone_number: beneficiary?.phone_number,
          country_code: beneficiary?.country?.country_code,
          country: beneficiary?.country,
          email: '',
          first_name: '',
          last_name: '',
        }}
        ref={modalizeRef}
        fetchContacts={() => fetchContacts()}
        createdUser={createdBeneficiary => {
          saveBeneficiary({
            ...createdBeneficiary,
          });
        }}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    marginVertical: 16,
  },
  countryCode: {
    backgroundColor: '#fff',
    width: '25%',
    marginRight: 16,
    justifyContent: 'center',
    alignItems: 'center',
    height: 48,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.2)',
  },
  countryCodeText: {
    fontSize: 16,
  },
});

export default BeneficiarySelection;
