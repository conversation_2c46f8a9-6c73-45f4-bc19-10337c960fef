import {
  View,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Platform,
} from 'react-native';
import React, {useEffect, useState, useMemo} from 'react';
import analytics from '@react-native-firebase/analytics';
import Layout from './components/Layout';
import Icon from 'react-native-vector-icons/Ionicons';
import TextCp from '../../components/TextCp';
import {translate} from '../../utilities/Translate';
import defaultStyles from '../../defaultStyles';
import useDanaStore from '../../app_state/store';
import CashInMethodComp from './components/CashInMethodComp';
import {showMessage} from 'react-native-flash-message';
import ExternalPaymentsModal from '../../components/ExternalPaymentsModal';
import {
  calculateFees,
  makeDirectTransfer,
  makeTransfer,
} from '../../apis/Transfers';
import {
  INSTANT_SEPA,
  BALANCE,
  BANK_CARD,
  BANK_TRANSFER,
  BANK_PAYOUT_INTERNAL,
  MOBILE_MONEY_BY_HUB2,
  DELIVERY,
  MFI,
} from '../constants';
import {ActivityIndicator} from 'react-native';
import {cleanError} from '../../utilities/cleanerror';
import {tiktokLogEvents} from '../../utilities/tiktok';

const customOrder = [INSTANT_SEPA, BANK_TRANSFER, BANK_CARD, BALANCE];

const CashInMethod = ({navigation}) => {
  const user = useDanaStore(state => state.user);
  const sendingCountry = useDanaStore(state => state.sendingCountry);
  const getUserCurrentState = useDanaStore(state => state.getUserCurrentState);
  const transfer_response = useDanaStore(state => state.transfer_response);
  const beneficiary = useDanaStore(state => state.beneficiary);
  const transfer = useDanaStore(state => state.transfer);
  const [show, setShow] = useState(false);
  const [loading, setLoading] = useState(false);
  const saveTransfer = useDanaStore(state => state.saveTransfer);
  const bankTransferType = useDanaStore(state => state.bankTransferType);

  const cashinFess = useDanaStore(state => state.cashinFess);

  const rates = useMemo(() => {
    const mergedObject = Object.assign({}, ...cashinFess?.fee_calculation);
    return mergedObject;
  }, [cashinFess]);

  const saveTransferResponse = useDanaStore(
    state => state.saveTransferResponse,
  );
  const cashOutMethod = transfer?.cashoutMethod;

  const customIban = transfer?.selectedIban
    ? transfer?.selectedIban?.iban
    : transfer?.enteredIban;

  const phone_number = transfer?.MMSelectedPhone
    ? transfer?.MMSelectedPhone?.phone_number
    : transfer?.MMEnteredPhone;

  const isBankSelected = ['bank_transfer', 'manual_bank_transfer'].includes(
    cashOutMethod?.cashout_method?.payment_type?.name,
  );

  const isNeeroWallet =
    cashOutMethod?.cashout_method?.name?.toLowerCase() === 'wallet by neero';

  const isMobileSelected =
    cashOutMethod?.cashout_method?.payment_type?.name === 'mobile_money';

  const isDelivery =
    cashOutMethod?.cashout_method?.name?.trim().toLowerCase() === DELIVERY;

  const isMicroFinance =
    cashOutMethod?.cashout_method?.name?.trim().toLowerCase() === MFI;

  const presentError = errorObject => {
    const response = errorObject?.response?.data;
    if (errorObject?.response?.status === 422) {
      let readableErrors = Object.entries(response.errors)
        .map(([field, messages]) =>
          messages
            .map(message =>
              translate(
                `${message}_${field}`.replace(/\./g, '_').toLowerCase(),
              ),
            )
            .join(', '),
        )
        .join(', ');

      showMessage({
        type: 'danger',
        message: readableErrors,
      });
    } else {
      showMessage({
        type: 'danger',
        message: response.message,
      });
    }
  };

  const makePayment = () => {
    const payload = {
      amount_without_fees_in_euro: transfer?.amount_in_euro,
      beneficiary_id: beneficiary?.id,
      cashin_method_id: transfer?.cashInMethod?.cash_in_method?.id,
      country_code: beneficiary?.country.country_code,
      is_escrowed: false,
      payment_delivery: isDelivery,
      reason: transfer?.reason,
      phone_number: beneficiary?.phone_number,
      verify: false,
    };

    if (
      transfer?.cashInMethod?.cash_in_method?.name?.toLowerCase() ===
      'bank transfer'
    ) {
      if (!bankTransferType) {
        showMessage({
          message: translate('bank_transfer_option'),
          type: 'danger',
        });
        return;
      }
    }

    if (cashOutMethod) {
      if (isBankSelected) {
        payload[
          'account_holder'
        ] = `${beneficiary?.first_name} ${beneficiary?.last_name}`;
        payload['bank_name'] = transfer?.bankName?.name;
        payload['iban'] = customIban;
        payload['institution_id'] = transfer?.bankName?.id;
        payload['account_holder'] = beneficiary?.full_name;
        payload['operator'] = transfer?.bankName?.name;
        payload['cashout_method_id'] = cashOutMethod?.cashout_method?.id;
      } else if (isMobileSelected) {
        payload['operator'] = transfer?.MMOperator?.name;
        payload['phone_number'] = phone_number;
        payload['institution_id'] = transfer?.MMOperator?.id;
        payload['cashout_method_id'] = cashOutMethod?.cashout_method?.id;
      } else if (isDelivery) {
        payload['cashout_method_id'] = cashOutMethod?.cashout_method?.id;
        payload['receiver_fullname'] = beneficiary?.full_name;
        payload['receiver_country'] = beneficiary?.country.name;
        payload['receiver_phonenumber'] = beneficiary?.phone_number;
        payload['country_id'] = beneficiary?.country.id;
        payload['local_amount'] = transfer.amount_in_cfa;
        payload['local_currency'] = rates.local_currency;
        payload['operator'] = 'danapay';
      } else if (isMicroFinance) {
        payload['operator'] = 'danapay';
      } else if (isNeeroWallet) {
        payload['cashout_account_id'] = transfer.neeroWallet.account_id;
        payload['cashout_method_id'] = cashOutMethod?.cashout_method?.id;
        payload['operator'] = 'Neero';
      }

      setLoading(true);
      makeDirectTransfer(payload)
        .then(async response => {
          
          if (response) {
            await analytics().logEvent('purchase', {
              ...payload,
              platform: Platform.OS,
            }); //when the user initiates a payment
            await tiktokLogEvents(
              'Purchase',
              user,
              {
                value: transfer?.amount_in_euro,
                currency: 'EUR',
                content_id: '',
                content_type: 'transaction_id',
              },
              Platform.OS,
            );
            handleResponse(response);
          }
        })
        .catch(error => {
          setLoading(false);
          presentError(error);
        });
    } else {
      payload['sending_country_id'] = sendingCountry?.id;
      payload['receiving_country_id'] = beneficiary?.country.id;
      setLoading(true);
      makeTransfer(payload)
        .then(async response => {
          if (response) {
            await analytics().logEvent('purchase', {
              ...payload,
              platform: Platform.OS,
            }); //when the user initiates a payment
            await tiktokLogEvents(
              'Purchase',
              user,
              {
                value: transfer?.amount_in_euro,
                currency: 'EUR',
                content_id: '',
                content_type: 'trsnaction_id',
              },
              Platform.OS,
            );
            handleResponse(response);
          }
        })
        .catch(error => {
          setLoading(false);
          presentError(error);
        });
    }
  };

  const handleResponse = res => {
    setLoading(false);
    const method = transfer?.cashInMethod?.cash_in_method?.name?.toLowerCase();
    if (method === 'bank transfer') {
      saveTransferResponse({body: res, nextPage: 'BankerTransfer'});
      navigation.navigate('BankerTransfer');
    } else if (method === 'bank card') {
      saveTransferResponse({body: res, nextPage: 'TransferSuccess'});
      setShow(true);
    } else if (method === 'instant sepa') {
      saveTransferResponse({body: res, nextPage: 'TransferSuccess'});
      setShow(true);
    } else if (method === 'balance') {
      saveTransferResponse({body: res, nextPage: 'TransferSuccess'});
      navigation.navigate('TransferSuccess', {status: 'success'});
    } else if (method === 'mobile money') {
      navigation.navigate('MobileMoney', {mmResponse: res});
    }
  };

  const goBack = () => {
    saveTransfer({cashInMethod: null});
    navigation.goBack();
  };

  useEffect(() => {
    getUserCurrentState();
  }, []);

  return (
    <>
      <Layout>
        <View style={defaultStyles.toolbar}>
          <View style={defaultStyles.toolbarLeft}>
            <TextCp style={defaultStyles.title} textType={'bold'}>
              {translate('cashInTittle')}
            </TextCp>
          </View>
          <TouchableOpacity onPress={() => goBack()}>
            <Icon name="close-circle-outline" size={32} color={'#000'} />
          </TouchableOpacity>
        </View>
        <ScrollView style={styles.content}>
          <TextCp>{translate('cashInDesc')}</TextCp>
          <View
            style={{
              padding: 10,
              backgroundColor: '#DEEEFA',
              flexDirection: 'row',
              alignItems: 'center',
              borderRadius: 10,
              marginVertical: 10,
            }}>
            <Icon
              name="information-circle-outline"
              color="rgba(1,41,56,.75)"
              size={32}
            />
            <View style={{flex: 1, marginLeft: 10}}>
              <TextCp color="rgba(1,41,56,.75)">
                {translate('cash_in_alert')}
              </TextCp>
            </View>
          </View>

          {sendingCountry?.cash_in_methods
            .sort(
              (a, b) =>
                customOrder.indexOf(
                  a.cash_in_method?.name?.trim().toLowerCase(),
                ) -
                customOrder.indexOf(
                  b.cash_in_method?.name?.trim().toLowerCase(),
                ),
            )
            .map(cashInMethod => (
              <CashInMethodComp
                cashInMethod={cashInMethod}
                key={cashInMethod?.id}
              />
            ))}
        </ScrollView>
        <View style={defaultStyles.footerWithButtons}>
          <TouchableOpacity
            onPress={() => {
              goBack();
            }}
            style={{
              ...defaultStyles.button,
              borderColor: '#eee',
              backgroundColor: '#fff',
            }}>
            <TextCp color="#000" textType={'bold'}>
              {translate('return')}
            </TextCp>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={makePayment}
            disabled={loading || !transfer?.cashInMethod}
            style={{
              ...defaultStyles.button,
              borderColor: '#eee',
              backgroundColor: !transfer?.cashInMethod
                ? 'rgba(0,0,0,.5)'
                : '#000',
            }}>
            {loading ? (
              <ActivityIndicator color="#fff" size="small" />
            ) : (
              <TextCp color="#fff" textType={'bold'}>
                {translate('continue')}
              </TextCp>
            )}
          </TouchableOpacity>
        </View>
      </Layout>
      <ExternalPaymentsModal
        show={show}
        navigation={navigation}
        connect_url={transfer_response?.body?.connect_url}
        translate={translate}
        setShow={setShow}
      />
    </>
  );
};

const styles = StyleSheet.create({
  content: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
});

export default CashInMethod;
