import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  StyleSheet,
  Platform,
} from 'react-native';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import Icon from 'react-native-vector-icons/Feather';
import Layout from './components/Layout';
import defaultStyles from '../../defaultStyles';
import {translate} from '../../utilities/Translate';
import TextCp from '../../components/TextCp';
import Input from '../../components/Input';
import CashInput from './components/CashInput';
import useDanaStore from '../../app_state/store';
import {colors} from '../../theme';
import {PayInfoSchema} from '../../validations';
import {
  appsFlyerTracking,
  canUseWithdrawal,
  extractYupErrors,
} from '../../utilities';
import useCashOutLimits from '../../Hooks/useCashOutLimits';
import useGetLang from '../../Hooks/useGetLang';
import Divider from '../../components/Divider';
import {calculateFees} from '../../apis/Transfers';
import {showMessage} from 'react-native-flash-message';

const PayInfo = ({navigation}) => {
  const [processing, setProcessing] = useState(false);
  const user = useDanaStore(state => state.user);
  const getBanks = useDanaStore(state => state.getBanks);
  const lang = useGetLang();
  const transfer = useDanaStore(state => state.transfer);
  const beneficiary = useDanaStore(state => state.beneficiary);
  const saveCashinFees = useDanaStore(state => state.saveCashinFees);
  const countries = useDanaStore(state => state.countries);
  const saveTransfer = useDanaStore(state => state.saveTransfer);
  const [errors, setErrors] = useState(null);
  const [lowerLimit, upperLimit] = useCashOutLimits();
  const [feesCalculating, setFeesCalculating] = useState(true);
  const [fees, setFees] = useState(null);
  const [noRate, setNoRate] = useState(false);

  const handlenext = async () => {
    setErrors(null);
    try {
      await PayInfoSchema.validate(
        {
          reason: transfer?.reason,
          amount_in_cfa: transfer?.amount_in_cfa,
          amount_in_euro: transfer?.amount_in_euro,
        },
        {abortEarly: false},
      );

      if (transfer?.amount_in_euro && lowerLimit > +transfer?.amount_in_euro) {
        setErrors(prev => ({
          ...prev,
          amount_in_euro: 'montant_incorrect_lower',
        }));
        return;
      }

      if (transfer?.amount_in_euro && upperLimit < +transfer?.amount_in_euro) {
        setErrors(prev => ({
          ...prev,
          amount_in_euro: 'montant_incorrect_higher',
        }));
        return;
      }

      saveTransfer({
        cashoutMethod: null,
      });

      appsFlyerTracking('af_add_to_cart', {
        af_revenue: transfer?.amount_in_euro, // Transferred amount
        af_receiver_phone_number: `${beneficiary?.phone_number}`, // Receiver Phone Number
        af_payout_method: 'Bank transfer', // Payout Method Name
        af_currency: 'EUR', // Currency Code
      });

      if (beneficiary?.is_individual) {
        navigation.navigate('WithdrawalMethods');
      } else if (canUseWithdrawal(beneficiary)) {
        navigation.navigate('WithdrawalMethods');
      } else {
        navigation.navigate('TransferSummary');
      }
    } catch (error) {
      setErrors(extractYupErrors(error));
    }
  };

  const limitErrorMessage = amount => {
    if (amount && lowerLimit > +amount) {
      setErrors(prev => ({
        ...prev,
        amount_in_euro: 'montant_incorrect_lower',
      }));
      return;
    }

    if (amount && upperLimit < +amount) {
      setErrors(prev => ({
        ...prev,
        amount_in_euro: 'montant_incorrect_higher',
      }));
      return;
    }

    setErrors(prev => ({
      ...prev,
      amount_in_euro: null,
    }));
  };

  const goBack = () => {
    saveTransfer({
      amount_in_euro: null,
      reason: null,
      amount_in_cfa: null,
      witdrawal_fees: null,
      MMOperator: null,
      MMSelectedPhone: null,
      MMEnteredPhone: null,
      selectedIban: null,
      enteredIban: null,
      bankName: null,
      cashoutMethod: null,
    });
    navigation.goBack();
  };

  const getAppliedFees = async (amount, currency) => {
    try {
      setFeesCalculating(true);
      saveCashinFees(null);

      if (!amount) {
        saveTransfer({
          amount_in_cfa: '',
          amount_in_euro: '',
        });
        setFeesCalculating(false);
        return;
      }

      const euroAmount =
        currency === 'EUR'
          ? amount
          : (amount / rates?.exchange_rate_local_currency).toFixed(3);

      if (currency === 'CFA') {
        saveTransfer({
          amount_in_euro: euroAmount,
        });
      }

      const country_code = user.country_code;
      const country = countries.find(val => val.country_code === country_code);

      const payload = {
        euro_amount: euroAmount,
        sending_country_id: country.id,
        cashin_method_id: 1,
        receiving_country_id: beneficiary.country.id,
      };

      const result = await calculateFees(payload, user?.id);
      const _resultsObject = Object.assign({}, ...result?.fee_calculation);

      if (result.fee_calculation.length > 0) {
        if (
          _resultsObject.hasOwnProperty('exchange_rate_local_currency') &&
          _resultsObject.exchange_rate_local_currency
        ) {
          setNoRate(false);
        } else {
          setNoRate(true);
        }
      } else {
        setNoRate(true);
      }
      saveCashinFees(result);
      setFees(prev => result);

      if (currency === 'EUR') {
        saveTransfer({
          amount_in_cfa: _resultsObject.received_amount_local_currency
            .toFixed(2)
            .toString(),
        });
      }

      setFeesCalculating(false);
    } catch (error) {
      setFeesCalculating(false);
    }
  };

  const initGetAppliedFees = async () => {
    try {
      setFeesCalculating(true);
      setFees(null);
      const country_code = user.country_code;
      const country = countries.find(val => val.country_code === country_code);
      const payload = {
        euro_amount: 10,
        sending_country_id: country.id,
        cashin_method_id: 1,
        receiving_country_id: beneficiary.country.id,
      };
      const result = await calculateFees(payload, user?.id);
      if (result.fee_calculation.length > 0) {
        const ratesObject = Object.assign({}, ...result?.fee_calculation);
        if (
          ratesObject.hasOwnProperty('exchange_rate_local_currency') &&
          ratesObject.exchange_rate_local_currency
        ) {
          setNoRate(false);
        } else {
          setNoRate(true);
        }
      } else {
        setNoRate(true);
      }
      setFees(result);
      setFeesCalculating(false);
    } catch (error) {
      setFeesCalculating(false);
    }
  };

  const rates = useMemo(() => {
    const mergedObject = fees && Object.assign({}, ...fees?.fee_calculation);
    return mergedObject;
  }, [fees]);

  useEffect(() => {
    getBanks();
  }, []);

  useEffect(() => {
    initGetAppliedFees();
  }, []);

  return (
    <Layout>
      <View style={defaultStyles.toolbar}>
        <View style={defaultStyles.toolbarLeft}>
          <TextCp style={defaultStyles.title} textType={'bold'}>
            {translate('pay_info_title')}
          </TextCp>
        </View>
        <TouchableOpacity onPress={() => goBack()}>
          <Icon name="x-circle" size={27} color={'#000'} />
        </TouchableOpacity>
      </View>
      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <TextCp style={styles.label}>{translate('pay_info_reason')}</TextCp>
          <Divider height={4} />
          <Input
            onChange={text => {
              saveTransfer({reason: text});
            }}
            label={translate('enter_reason')}
            value={transfer?.reason?.trim().length > 0 ? transfer?.reason : ''}
            backgroundColor={'#fff'}
            testID="paymentReasonInput"
            error={errors?.reason && translate(errors?.reason)}
          />
        </View>
        <View style={styles.section}>
          {lang === 'fr' ? (
            <TextCp style={styles.label}>
              Saisissez un montant entre{' '}
              <TextCp
                color={colors.primary}
                textType={'bold'}>{`${lowerLimit}€`}</TextCp>{' '}
              et{' '}
              <TextCp
                color={colors.primary}
                textType={'bold'}>{`${upperLimit}€`}</TextCp>
            </TextCp>
          ) : (
            <TextCp style={styles.label}>
              Enter an amount between{' '}
              <TextCp
                color={colors.primary}
                textType={'bold'}>{`${lowerLimit}€`}</TextCp>{' '}
              and{' '}
              <TextCp
                color={colors.primary}
                textType={'bold'}>{`${upperLimit}€`}</TextCp>
            </TextCp>
          )}

          <CashInput
            label={translate('amount_to_send')}
            onChange={text => {
              saveTransfer({
                amount_in_euro: text,
              });
              limitErrorMessage(text);
              getAppliedFees(text, 'EUR');
            }}
            currency={'EUR'}
            value={transfer?.amount_in_euro}
            error={errors?.amount_in_euro && translate(errors.amount_in_euro)}
          />

          {noRate ? (
            <View style={{padding: 10}}>
              <TextCp color="red">{translate('no_rate')}</TextCp>
            </View>
          ) : (
            <View style={styles.exchangeContainer}>
              <View style={styles.exchangeContainer}>
                <TextCp color={colors.primary}>
                  {translate('exchange_rate')}
                  {' : '}
                </TextCp>
                {fees && (
                  <TextCp color={colors.primary} textType={'bold'}>
                    1EUR =
                    {` ${rates?.exchange_rate_local_currency} ${
                      rates?.local_currency === 'XOF'
                        ? 'CFA'
                        : rates?.local_currency
                    }`}
                  </TextCp>
                )}
              </View>

              {feesCalculating && (
                <ActivityIndicator color="#ccc" size={'small'} />
              )}
            </View>
          )}

          <CashInput
            label={translate('amount_to_receive')}
            onChange={text => {
              saveTransfer({
                amount_in_cfa: text,
              });
              limitErrorMessage(text / rates?.exchange_rate_local_currency);
              getAppliedFees(text, 'CFA');
            }}
            currency={rates?.local_currency}
            value={transfer.amount_in_cfa}
            error={errors?.amount_in_cfa && translate(errors?.amount_in_cfa)}
          />
          {/* Remove this semicolon */}
        </View>
      </ScrollView>
      <View style={defaultStyles.footerWithButtons}>
        <TouchableOpacity
          onPress={() => {
            goBack();
          }}
          style={{
            ...defaultStyles.button,
            borderColor: '#eee',
            backgroundColor: '#fff',
          }}>
          <TextCp color="#000" textType={'bold'}>
            {translate('return')}
          </TextCp>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handlenext}
          disabled={noRate}
          style={{
            ...defaultStyles.button,
            borderColor: '#eee',
            backgroundColor: noRate ? 'rgba(0,0,0,.5)' : '#000',
          }}>
          {processing ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <TextCp color="#fff" textType={'bold'}>
              {translate('continue')}
            </TextCp>
          )}
        </TouchableOpacity>
      </View>
    </Layout>
  );
};

const styles = StyleSheet.create({
  content: {
    padding: 16,
  },
  section: {
    marginVertical: 8,
  },
  exchangeContainer: {
    marginHorizontal: 8,
    marginVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    width: '80%',
    marginBottom: 10,
  },
});

export default PayInfo;
