import {
  View,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Image,
} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import React, {useState, useEffect, useMemo} from 'react';
import Layout from './components/Layout';
import TextCp from '../../components/TextCp';
import {translate} from '../../utilities/Translate';
import defaultStyles from '../../defaultStyles';
import useDanaStore from '../../app_state/store';
import {colors, scaleRatio} from '../../theme';
import {
  BANK_PAYOUT_INTERNAL,
  DELIVERY,
  MFI,
  MOBILE_MONEY_BY_HUB2,
} from '../constants';
import {calculateFees} from '../../apis/Transfers';
import useGetLang from '../../Hooks/useGetLang';
import FastImage from 'react-native-fast-image';

const TransferSummary = ({navigation}) => {
  const user = useDanaStore(state => state.user);
  const lang = useGetLang();
  const transfer = useDanaStore(state => state.transfer);
  const getBanks = useDanaStore(state => state.getBanks);
  const sendingCountry = useDanaStore(state => state.sendingCountry);
  const beneficiary = useDanaStore(state => state.beneficiary);
  const [fees, setFees] = useState(null);
  const cashinFess = useDanaStore(state => state.cashinFess);

  const cashOutMethod = transfer?.cashoutMethod;

  const customIban = transfer.selectedIban
    ? transfer?.selectedIban?.iban
    : transfer?.enteredIban;

  const phone_number = transfer?.MMSelectedPhone
    ? transfer?.MMSelectedPhone?.phone_number
    : transfer?.MMEnteredPhone;

  const isBankSelected = ['bank_transfer', 'manual_bank_transfer'].includes(
    cashOutMethod?.cashout_method?.payment_type?.name,
  );

  const isMobileSelected =
    cashOutMethod?.cashout_method?.payment_type?.name === 'mobile_money';

  const isNeeroWallet =
    cashOutMethod?.cashout_method?.name?.toLowerCase() === 'wallet by neero';

  const isDelivery =
    cashOutMethod?.cash_out_method?.name?.trim().toLowerCase() === DELIVERY;

  const isMicro =
    cashOutMethod?.cash_out_method?.name?.trim().toLowerCase() === MFI;

  const full_name = `${beneficiary.first_name} ${beneficiary.last_name}`;

  const cashInMethodName = transfer?.cashInMethod?.cash_in_method?.name
    .toLowerCase()
    .split(' ')
    .join('_');

  const cashOutmethod = transfer?.cashoutMethod?.cash_out_method?.name?.trim();

  const total_to_pay = useMemo(() => {
    // if (fees === null) return 0;
    const _fees = fees?.fee ? Number(fees?.fee) : 0;
    if (transfer?.witdrawal_fees) {
      return (
        Number(transfer?.amount_in_euro) +
        _fees +
        Number(transfer?.witdrawal_fees?.fee)
      );
    } else {
      return Number(transfer?.amount_in_euro) + _fees;
    }
  }, [fees]);

  const next = () => {
    navigation.navigate('CashInMethod');
  };

  const computedFees = useMemo(() => {
    if (transfer?.witdrawal_fees) {
      return (transfer?.witdrawal_fees.fee + fees?.fee).toFixed(2);
    } else {
      return fees?.fee.toFixed(2);
    }
  }, [fees, transfer?.witdrawal_fees]);

  const fetchAppliedFees = async () => {
    try {
      const body = {
        euro_amount: transfer?.amount_in_euro,
        sending_country_id: sendingCountry.id,
        receiving_country_id: beneficiary?.country.id,
        cashin_method_id:
          sendingCountry?.cash_in_methods[0]?.cash_in_method?.id,
      };
      const result = await calculateFees(body, user?.id);
      setFees(result);
    } catch (error) {}
  };

  useEffect(() => {
    fetchAppliedFees();
  }, []);

  const rates = useMemo(() => {
    const mergedObject = Object.assign({}, ...cashinFess?.fee_calculation);
    return mergedObject;
  }, [cashinFess]);

  return (
    <Layout>
      <View style={defaultStyles.toolbar}>
        <View style={defaultStyles.toolbarLeft}>
          <TextCp style={defaultStyles.title} textType={'bold'}>
            {translate('summary')}
          </TextCp>
        </View>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="x-circle" size={24} color={'#000'} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        <TextCp style={styles.p}>{translate('summarySubTitle')}</TextCp>
        <Image
          source={require('../../images/summary.jpg')}
          style={styles.image}
        />

        <View>
          <TextCp color="rgba(0,0,0,.5)" style={styles.itemHeader}>
            {translate('funds_sent_to')}
          </TextCp>
          <TextCp
            textType={'medium'}
            style={{fontSize: 20, textTransform: 'capitalize'}}>
            {full_name}
          </TextCp>
          {cashOutmethod && (
            <TextCp textType={'bold'}>
              {translate(cashOutmethod?.toLowerCase().split(' ').join('_')) ||
                ''}
            </TextCp>
          )}

          {isBankSelected && (
            <>
              <TextCp style={styles.providername}>
                {transfer?.bankName?.name}
              </TextCp>
              <TextCp>
                {transfer.selectedIban
                  ? transfer?.selectedIban?.iban
                  : transfer?.enteredIban}
              </TextCp>
            </>
          )}

          {!cashOutMethod && (
            <>
              <TextCp style={styles.providername}>
                {`+${beneficiary.country.country_code}${beneficiary.phone_number}`}
              </TextCp>
            </>
          )}

          {isMobileSelected && (
            <>
              <TextCp style={styles.providername}>
                {transfer.MMOperator.name}
              </TextCp>
              <TextCp>
                {`+${beneficiary.country.country_code}${
                  transfer.MMSelectedPhone
                    ? transfer?.MMSelectedPhone?.phone_number
                    : transfer?.MMEnteredPhone
                }`}
              </TextCp>
            </>
          )}

          {isNeeroWallet && (
            <TextCp style={styles.providername}>
              {`+${transfer?.neeroWallet?.country_code}${transfer?.neeroWallet?.wallet_account_number}`}
            </TextCp>
          )}
        </View>

        <View style={styles.row}>
          <View style={styles.left}>
            <TextCp color="rgba(0,0,0,.75)" style={styles.itemHeader}>
              {translate('amount_received')}
            </TextCp>
            <TextCp style={{fontSize: 18}} textType={'bold'}>
              {(+transfer.amount_in_cfa).toFixed(0)} {rates.local_currency}
            </TextCp>
          </View>
          <View style={styles.right}>
            <TextCp
              color="rgba(0,0,0,.75)"
              style={styles.itemHeader}
              align="right">
              {translate('fees')}
            </TextCp>
            <TextCp style={{fontSize: 18}} textType={'bold'} align="right">
              {isNaN(computedFees) ? 0 : computedFees}€
            </TextCp>
          </View>
        </View>

        <View style={styles.row}>
          <View style={styles.left}>
            <TextCp color="rgba(0,0,0,.75)" style={styles.itemHeader}>
              {translate('totalToPay')}
            </TextCp>
            <TextCp
              color={colors.primary}
              textType={'bold'}
              style={{fontSize: 18}}>
              {total_to_pay.toFixed(2)}€
            </TextCp>
          </View>
        </View>

        {!cashOutMethod && (
          <View>
            <View style={styles.alert}>
              <Icon
                name="info"
                color="#012938"
                size={30}
                style={{marginRight: 10}}
              />
              {lang === 'fr' ? (
                <TextCp color="rgba(1,41,56,.75)" style={{flex: 1}}>
                  Votre bénéficiaire recevra les fonds sur son compte Danapay
                  immédiatement après réception de votre paiement.
                </TextCp>
              ) : (
                <TextCp color="rgba(1,41,56,.75)" style={{flex: 1}}>
                  Your beneficiary will receive the funds in their Danapay
                  account immediately after your payment is received.
                </TextCp>
              )}
            </View>
          </View>
        )}

        {isMobileSelected && (
          <View style={styles.alert}>
            <Icon
              name="info"
              color="#012938"
              size={30}
              style={{marginRight: 10}}
            />
            {lang === 'fr' ? (
              <TextCp color="rgba(1,41,56,.75)" style={{flex: 1}}>
                Votre bénéficiaire devrait recevoir les fonds sur son compte
                mobile dans les{' '}
                <TextCp color="rgba(1,41,56,.75)" textType={'bold'}>
                  5 mn{' '}
                </TextCp>{' '}
                suivant la réception de votre paiement.
              </TextCp>
            ) : (
              <TextCp color="rgba(1,41,56,.75)" style={{flex: 1}}>
                Your beneficiary should receive the funds in their mobile
                account within{' '}
                <TextCp color="rgba(1,41,56,.75)" textType={'bold'}>
                  5 minutes{' '}
                </TextCp>{' '}
                following the receipt of your payment.
              </TextCp>
            )}
          </View>
        )}

        {(isBankSelected || isMicro) && (
          <View style={styles.alert}>
            <Icon
              name="info"
              color="#012938"
              size={30}
              style={{marginRight: 10}}
            />
            {lang === 'fr' ? (
              <TextCp color="rgba(1,41,56,.75)" style={{flex: 1}}>
                Votre bénéficiaire devrait recevoir les fonds sur son compte
                mobile dans les{' '}
                <TextCp color="rgba(1,41,56,.75)" textType={'bold'}>
                  72h{' '}
                </TextCp>
                suivant la réception de votre paiement.
              </TextCp>
            ) : (
              <TextCp color="rgba(1,41,56,.75)" style={{flex: 1}}>
                Your beneficiary should receive the funds in their mobile
                account within{' '}
                <TextCp color="rgba(1,41,56,.75)" textType={'bold'}>
                  72 hours
                </TextCp>{' '}
                following the receipt of your payment.
              </TextCp>
            )}
          </View>
        )}

        {isDelivery && (
          <View style={styles.alert}>
            <Icon
              name="info"
              color="#012938"
              size={30}
              style={{marginRight: 10}}
            />
            {lang === 'fr' ? (
              <TextCp color="rgba(1,41,56,.75)" style={{flex: 1}}>
                Votre bénéficiaire devrait recevoir les fonds sur son compte
                bancaire dans les{' '}
                <TextCp color="rgba(1,41,56,.75)" textType={'bold'}>
                  6h
                </TextCp>{' '}
                suivant la réception de votre paiement.
              </TextCp>
            ) : (
              <TextCp color="rgba(1,41,56,.75)" style={{flex: 1}}>
                Your beneficiary should receive the funds in their bank account
                within{' '}
                <TextCp textType={'bold'} color="rgba(1,41,56,.75)">
                  6 hours
                </TextCp>{' '}
                following the receipt of your payment.
              </TextCp>
            )}
          </View>
        )}

        {isNeeroWallet && (
          <View style={styles.alert}>
            <Icon
              name="info"
              color="#012938"
              size={30}
              style={{marginRight: 10}}
            />
            {lang === 'fr' ? (
              <TextCp color="rgba(1,41,56,.75)" style={{flex: 1}}>
                Votre beneficiaire recevra les fonds dans son compte Neero{' '}
                <TextCp color="rgba(1,41,56,.75)" textType={'bold'}>
                  5 min
                </TextCp>
                apres reception des fonds
              </TextCp>
            ) : (
              <TextCp color="rgba(1,41,56,.75)" style={{flex: 1}}>
                Your beneficiary will receive the funds in their Neero account{' '}
                <TextCp color="rgba(1,41,56,.75)" textType={'bold'}>
                  5 minutes{' '}
                </TextCp>{' '}
                after the funds are received.
              </TextCp>
            )}
          </View>
        )}
      </ScrollView>
      <View style={defaultStyles.footerWithButtons}>
        <TouchableOpacity
          onPress={() => {
            navigation.goBack();
          }}
          style={{
            ...defaultStyles.button,
            borderColor: '#eee',
            backgroundColor: '#fff',
          }}>
          <TextCp color="#000" textType={'bold'}>
            {translate('return')}
          </TextCp>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={next}
          style={{
            ...defaultStyles.button,
            borderColor: '#eee',
          }}>
          <TextCp color="#fff" textType={'bold'}>
            {translate('pay')}
          </TextCp>
        </TouchableOpacity>
      </View>
    </Layout>
  );
};

const styles = StyleSheet.create({
  content: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  image: {
    height: 180 * scaleRatio,
    width: '100%',
    borderRadius: 10,
    marginVertical: 16,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
  },

  userDetails: {
    marginVertical: 16,
  },

  right: {
    flex: 1,
  },
  left: {
    flex: 1,
  },
  alert: {
    backgroundColor: '#DEEEFA',
    padding: 10,
    borderRadius: 10,
    marginVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  providername: {
    fontSize: 13,
    textTransform: 'capitalize',
  },
  itemHeader: {
    fontSize: 12,
  },
});

export default TransferSummary;
