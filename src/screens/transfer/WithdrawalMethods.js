import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import React, {useState, useEffect} from 'react';
import Icon from 'react-native-vector-icons/Feather';
import Layout from './components/Layout';
import defaultStyles from '../../defaultStyles';
import {translate} from '../../utilities/Translate';
import TextCp from '../../components/TextCp';
import useDanaStore from '../../app_state/store';
import {addNeeroAccount, getCountryCashOutMethods} from '../../apis/Transfers';
import CashOutMethodComp from './components/CashOutMethodComp';
import {showMessage} from 'react-native-flash-message';
import {isValid} from '../../utilities';
import {
  BANK_PAYOUT_INTERNAL,
  MOBILE_MONEY_BY_HUB2,
  DELIVERY,
  MFI,
} from '../constants';

const customOrder = [MOBILE_MONEY_BY_HUB2, BANK_PAYOUT_INTERNAL, DELIVERY, MFI];

const WithdrawalMethods = ({navigation}) => {
  const beneficiary = useDanaStore(state => state.beneficiary);
  const [loading, setLoading] = useState(false);
  const [adding, setAdding] = useState(false);
  const saveTransfer = useDanaStore(state => state.saveTransfer);
  const transfer = useDanaStore(state => state.transfer);
  const [cashoutMethods, setCashOutMethods] = useState([]);
  const isBankSelected = ['bank_transfer', 'manual_bank_transfer'].includes(
    transfer?.cashoutMethod?.cashout_method.payment_type?.name,
  );

  const isNeeroWallet =
    transfer?.cashoutMethod?.cashout_method?.name?.toLowerCase() ===
    'wallet by neero';

  const isMobileSelected =
    transfer?.cashoutMethod?.cashout_method?.payment_type?.name ===
    'mobile_money';

  const isMobileValid = async () => {
    const phone_number = transfer?.MMSelectedPhone
      ? transfer?.MMSelectedPhone?.phone_number
      : transfer?.MMEnteredPhone;

    if (!transfer?.MMOperator) {
      showMessage({
        type: 'danger',
        message: translate('operator_required'),
      });
      return;
    }

    if (
      !isValid(
        beneficiary?.country?.country_code,
        phone_number,
        beneficiary?.country?.code,
      )
    ) {
      showMessage({
        type: 'danger',
        message: translate('invalid_phone_number'),
      });
      return;
    }

    navigation.navigate('TransferSummary');
  };

  const isBankValid = () => {
    const customIban = transfer?.selectedIban
      ? transfer?.selectedIban?.iban
      : transfer?.enteredIban;

    if (!transfer?.bankName) {
      showMessage({
        type: 'danger',
        message: translate('bank_required'),
      });
      return;
    }

    if (!customIban || customIban?.length === 0) {
      showMessage({
        type: 'danger',
        message: translate('mising_iban'),
      });
      return;
    }

    if (
      !/[a-zA-Z]{2}[0-9]{2}[a-zA-Z0-9]{4}[0-9]{7}([a-zA-Z0-9]?){0,16}/gi.test(
        customIban,
      )
    ) {
      showMessage({
        type: 'danger',
        message: translate('valid_iban'),
      });
      return;
    }

    navigation.navigate('TransferSummary');
  };

  const isNeeroValid = async () => {
    if (transfer.neeroWalletNumber) {
      try {
        setAdding(true);
        const payload = {
          title: `My Neero wallet ${transfer.neeroWalletNumber}`,
          country: beneficiary.country.name,
          owner_name: beneficiary.full_name,
          country_code: beneficiary.country?.country_code,
          wallet_account_number: transfer.neeroWalletNumber,
        };
        const response = await addNeeroAccount(payload, beneficiary.id);
        saveTransfer({neeroWallet: response.mm_account});
        setAdding(false);
        navigation.navigate('TransferSummary');
      } catch (error) {
        setAdding(false);
        showMessage({
          type: 'danger',
          message: '',
        });
        return;
      }
    } else if (transfer.neeroWallet) {
      navigation.navigate('TransferSummary');
    } else {
      showMessage({
        type: 'danger',
        message: translate('no_neero_wallet_number'),
      });
      return;
    }
  };

  const next = async () => {
    if (isBankSelected) {
      isBankValid();
      return;
    }

    if (isMobileSelected) {
      isMobileValid();
      return;
    }

    if (isNeeroWallet) {
      isNeeroValid();
      return;
    }

    navigation.navigate('TransferSummary');
  };

  const getCashOutmethod = async () => {
    try {
      setLoading(true);
      const response = await getCountryCashOutMethods(
        beneficiary?.country?.code,
      );
      if (response) {
        setCashOutMethods(
          response.filter(
            val =>
              val?.cashout_method?.name?.toLowerCase() !== 'mfi by aloohpay',
          ),
        );
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  useEffect(() => {
    getCashOutmethod();
  }, []);

  const goBack = () => {
    saveTransfer({
      cashoutMethod: null,
      witdrawal_fees: null,
      MMOperator: null,
      MMSelectedPhone: null,
      MMEnteredPhone: null,
      selectedIban: null,
      enteredIban: null,
      bankName: null,
    });
    navigation.goBack();
  };

  return (
    <Layout>
      <View style={defaultStyles.toolbar}>
        <View style={defaultStyles.toolbarLeft}>
          <TextCp style={defaultStyles.title} textType={'bold'}>
            {translate('withdrawal_option_title')}
          </TextCp>
        </View>
        <TouchableOpacity onPress={() => goBack()} style={{padding: 5}}>
          <Icon name="x-circle" size={27} color={'#000'} />
        </TouchableOpacity>
      </View>
      <ScrollView style={styles.content}>
        <TextCp>{translate('withdrawal_option_desc')}</TextCp>
        {loading && (
          <View style={styles.fetching}>
            <ActivityIndicator size="large" />
          </View>
        )}

        {cashoutMethods
          .sort(
            (a, b) =>
              customOrder.indexOf(
                a.cash_out_method?.name?.trim().toLowerCase(),
              ) -
              customOrder.indexOf(
                b.cash_out_method?.name?.trim().toLowerCase(),
              ),
          )
          .map(val => (
            <CashOutMethodComp cashOut={val} key={val.id} />
          ))}

        <View style={{height: 100}}></View>
      </ScrollView>
      <View style={defaultStyles.footerWithButtons}>
        <TouchableOpacity
          onPress={() => {
            goBack();
          }}
          style={{
            ...defaultStyles.button,
            borderColor: '#eee',
            backgroundColor: '#fff',
          }}>
          <TextCp color="#000" textType={'bold'}>
            {translate('return')}
          </TextCp>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={next}
          disabled={!transfer.cashoutMethod}
          loading={adding}
          style={{
            ...defaultStyles.button,
            borderColor: '#eee',
            backgroundColor: !transfer.cashoutMethod
              ? 'rgba(0,0,0,.5)'
              : '#000',
          }}>
          <TextCp color="#fff" textType={'bold'}>
            {translate('continue')}
          </TextCp>
        </TouchableOpacity>
      </View>
    </Layout>
  );
};

const styles = StyleSheet.create({
  content: {
    padding: 16,
  },
  fetching: {
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default WithdrawalMethods;
