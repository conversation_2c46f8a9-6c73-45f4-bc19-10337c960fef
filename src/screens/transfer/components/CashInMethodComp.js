import {View, TouchableOpacity, StyleSheet} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import Icon from 'react-native-vector-icons/Ionicons';
import useDanaStore from '../../../app_state/store';
import TextCp from '../../../components/TextCp';
import MIcon from 'react-native-vector-icons/MaterialCommunityIcons';
import {translate} from '../../../utilities/Translate';
import useGetLang from '../../../Hooks/useGetLang';
import defaultStyles from '../../../defaultStyles';
import {colors} from '../../../theme';
import useEverUsedBankTransfer from '../../../Hooks/useEverUsedBankTransfer';
import {showMessage} from 'react-native-flash-message';

const ICONS = {
  instant_sepa: 'flash-outline',
  bank_transfer: 'business-outline',
  balance: 'wallet-outline',
  bank_card: 'card-outline',
};

const method_text = {
  'bank card': {
    title: 'bank_card_instruction_title',
    description: 'bank_card_instruction_description',
  },
  'bank transfer': {
    title: 'bank_transfer_instruction_title',
    description: 'bank_transfer_instruction_description',
  },
};

const CashInMethodComp = ({cashInMethod}) => {
  const lang = useGetLang();
  const user = useDanaStore(state => state.user);
  const restricted_account = user?.activation_level?.toLowerCase() === 'yellow';
  const transfer = useDanaStore(state => state.transfer);
  const saveTransfer = useDanaStore(state => state.saveTransfer);
  const bankTransferType = useDanaStore(state => state.bankTransferType);
  const setBankTransferType = useDanaStore(state => state.setBankTransferType);
  const banks = useDanaStore(state => state.banks);

  const {cash_in_method} = cashInMethod;

  const canUseBalance = +user?.client?.euro_balance > +transfer?.amount_in_euro;

  const isAboveLimit = +transfer?.amount_in_euro > cash_in_method?.max_amount;
  const isbelowLimit = +transfer?.amount_in_euro < cash_in_method?.min_amount;

  const isSelected = cashInMethod.id === transfer?.cashInMethod?.id;

  const description =
    method_text[cash_in_method?.name?.toLowerCase()]?.description;

  const isBalance = cash_in_method?.name.toLowerCase() === 'balance';

  const isBankTransfer =
    cash_in_method?.name?.toLowerCase() === 'bank transfer';
  const isInstantSepa = cash_in_method?.name?.toLowerCase() === 'instant sepa';

  const disabled =
    (isBalance && !canUseBalance) || isAboveLimit || isbelowLimit;

  const autoCheckMethod = () => {
    if (isBalance && canUseBalance) {
      saveTransfer({cashInMethod});
    } else if (isInstantSepa) {
      saveTransfer({cashInMethod});
    }
  };

  const renderIcon = type => {
    if (type === 'bank_transfer') {
      return (
        <MIcon
          name="bank-outline"
          size={20}
          style={{marginRight: 4}}
          color="#828282"
        />
      );
    } else if (type === 'balance') {
      return (
        <Icon
          name="wallet-outline"
          size={20}
          style={{marginRight: 4}}
          color="#828282"
        />
      );
    } else if (type === 'bank_card') {
      return (
        <Icon
          name="card-outline"
          size={20}
          style={{marginRight: 4}}
          color="#828282"
        />
      );
    } else if (type === 'instant_sepa') {
      return (
        <Icon
          name="flash-outline"
          size={20}
          style={{marginRight: 4}}
          color="#828282"
        />
      );
    }
  };

  const selectBankTransfer = bank => {
    if (bankTransferType?.bank_name === bank.bank_name) {
      setBankTransferType(null);
    } else {
      saveTransfer({cashInMethod});
      setBankTransferType(bank);
    }
  };

  useEffect(() => {
    autoCheckMethod();
  }, [cashInMethod]);

  return (
    <>
      {isBalance && canUseBalance && (
        <View
          style={{
            ...styles.card,
            borderColor: isSelected ? '#049D9F' : '#fff',
            ...defaultStyles.boxShow,
            shadowColor: isSelected ? colors.primary : '#000',
          }}>
          <TouchableOpacity
            disabled={disabled}
            onPress={() => {
              if (restricted_account) {
                showMessage({
                  type: 'danger',
                  message: translate('restricted_account_desc'),
                });
                return;
              }
              saveTransfer({cashInMethod});
            }}>
            <View style={styles.cardHeader}>
              <View style={styles.cardHeaderLeft}>
                {renderIcon(
                  cash_in_method?.name?.toLowerCase().split(' ').join('_'),
                )}
                <TextCp textType={'bold'} style={{fontSize: 15}}>
                  {translate(
                    cash_in_method?.name?.toLowerCase()?.split(' ')?.join('_'),
                  )}
                </TextCp>
              </View>

              <View>
                <TextCp
                  color={'rgba(0,0,0,.5)'}
                  style={{fontSize: 12}}
                  align="right">
                  {cash_in_method?.name?.toLowerCase()}

                  {translate('max_limit')}
                </TextCp>
                <TextCp textType={'bold'} style={{fontSize: 14}} align="right">
                  {cash_in_method?.max_amount}€
                </TextCp>
              </View>
            </View>

            {/* {isSelected && description && ( */}

            {isBalance && (
              <View style={styles.cardFooter}>
                {lang === 'fr' ? (
                  <TextCp color="rgba(0,0,0,.75)">
                    Les fonds seront{' '}
                    <TextCp
                      color="#000"
                      style={styles.boldText}
                      textType={'bold'}>
                      retirés de votre solde
                    </TextCp>{' '}
                    actuel. Le transfert sera exécuté immédiatement
                  </TextCp>
                ) : (
                  <TextCp color="rgba(0,0,0,.75)">
                    The funds will be{' '}
                    <TextCp
                      color="#000"
                      style={styles.boldText}
                      textType={'bold'}>
                      deducted from your current balance
                    </TextCp>
                    .The transfer will be executed immediately.
                  </TextCp>
                )}
              </View>
            )}
          </TouchableOpacity>

          {/* )} */}
          {isBalance ? (
            <>
              {!canUseBalance && (
                <View style={styles.alert}>
                  <TextCp color="#963B3B">{translate('balance_low')}</TextCp>
                </View>
              )}
            </>
          ) : (
            <>
              {isAboveLimit && (
                <View style={styles.alert}>
                  <TextCp color="#963B3B">
                    {lang.toLowerCase() === 'fr'
                      ? `La méthode ne peut pas prendre en charge le transfert car le montant maximal de transfert est de ${cash_in_method?.max_amount}.`
                      : `This method cannot support the transfer because the maxmum transfer is ${cash_in_method?.max_amount}.`}
                  </TextCp>
                </View>
              )}
              {isbelowLimit && (
                <View style={styles.alert}>
                  <TextCp color="#963B3B">
                    {lang.toLowerCase() === 'fr'
                      ? `La méthode ne peut pas prendre en charge le transfert car le montant minimal de transfert est de ${cash_in_method?.min_amount}.`
                      : `This method cannot support the transfer because the minimum transfer is ${cash_in_method?.min_amount}.`}
                  </TextCp>
                </View>
              )}
            </>
          )}
        </View>
      )}

      {!isBalance && !isBankTransfer && (
        <View
          style={{
            ...styles.card,
            borderColor: isSelected ? '#049D9F' : '#fff',
            ...defaultStyles.boxShow,
            shadowColor: isSelected ? colors.primary : '#000',
          }}>
          <TouchableOpacity
            onPress={() => {
              if (restricted_account) {
                showMessage({
                  type: 'danger',
                  message: translate('restricted_account_desc'),
                });
                return;
              }
              saveTransfer({cashInMethod});
              setBankTransferType(null);
            }}>
            <View style={styles.cardHeader}>
              <View style={styles.cardHeaderLeft}>
                {renderIcon(
                  cash_in_method?.name?.toLowerCase().split(' ').join('_'),
                )}
                <TextCp textType={'bold'} style={{fontSize: 15}}>
                  {translate(
                    cash_in_method?.name?.toLowerCase().split(' ').join('_'),
                  )}
                </TextCp>
              </View>
              {isInstantSepa ? (
                <View style={styles.recommended}>
                  <TextCp
                    textType={'bold'}
                    color={'rgba(0,0,0,.5)'}
                    style={{fontSize: 10}}>
                    {translate('recommended')}
                  </TextCp>
                </View>
              ) : (
                <View>
                  <TextCp
                    color={'rgba(0,0,0,.5)'}
                    style={{fontSize: 12}}
                    align="right">
                    {translate('max_limit')}
                  </TextCp>
                  <TextCp
                    textType={'bold'}
                    style={{fontSize: 14}}
                    align="right">
                    {cash_in_method?.max_amount}€
                  </TextCp>
                </View>
              )}
            </View>

            {/* {isSelected && description && ( */}

            {cash_in_method?.name.toLowerCase() === 'bank card' && (
              <View style={styles.cardFooter}>
                {lang === 'fr' ? (
                  <TextCp color="rgba(0,0,0,.75)">
                    Réception des fonds en{' '}
                    <TextCp
                      color="#000"
                      style={styles.boldText}
                      textType={'bold'}>
                      5mn
                    </TextCp>
                    . Traité par notre partenaire{' '}
                    <TextCp
                      color="#000"
                      style={styles.boldText}
                      textType={'bold'}>
                      Monext Payline
                    </TextCp>
                    .
                  </TextCp>
                ) : (
                  <TextCp color="rgba(0,0,0,.75)">
                    Funds received within{' '}
                    <TextCp
                      color="#000"
                      style={styles.boldText}
                      textType={'bold'}>
                      5 minutes
                    </TextCp>
                    . Processed by our partner{' '}
                    <TextCp
                      color="#000"
                      style={styles.boldText}
                      textType={'bold'}>
                      Monext Payline
                    </TextCp>
                  </TextCp>
                )}
              </View>
            )}

            {cash_in_method?.name.toLowerCase() === 'instant sepa' && (
              <View style={styles.cardFooter}>
                {lang === 'fr' ? (
                  <TextCp color="rgba(0,0,0,.75)">
                    Réception en <TextCp textType={'bold'}>1 h</TextCp> pour les
                    montants inférieurs à{' '}
                    <TextCp textType={'bold'}> 3000€</TextCp> et{' '}
                    <TextCp textType={'bold'}> 24h</TextCp> au delà. Sécurisé
                    par notre partenaire{' '}
                    <TextCp textType={'bold'}> BridgeAPI</TextCp>
                  </TextCp>
                ) : (
                  <>
                    <TextCp style={{fontSize: 13}}>
                      Received within <TextCp textType={'bold'}>1 hour</TextCp>{' '}
                      for amounts under <TextCp textType={'bold'}>€3000</TextCp>{' '}
                      and within <TextCp textType={'bold'}>24 hours</TextCp> for
                      higher amounts. Secured by our partner{' '}
                      <TextCp textType={'bold'}>BridgeAPI</TextCp>
                    </TextCp>
                  </>
                )}
              </View>
            )}
          </TouchableOpacity>

          {/* )} */}
          {isBalance ? (
            <>
              {!canUseBalance && (
                <View style={styles.alert}>
                  <TextCp color="#963B3B">{translate('balance_low')}</TextCp>
                </View>
              )}
            </>
          ) : (
            <>
              {isAboveLimit && (
                <View style={styles.alert}>
                  <TextCp color="#963B3B">
                    {lang.toLowerCase() === 'fr'
                      ? `La méthode ne peut pas prendre en charge le transfert car le montant maximal de transfert est de ${cash_in_method?.max_amount}.`
                      : `This method cannot support the transfer because the maxmum transfer is ${cash_in_method?.max_amount}.`}
                  </TextCp>
                </View>
              )}
              {isbelowLimit && (
                <View style={styles.alert}>
                  <TextCp color="#963B3B">
                    {lang.toLowerCase() === 'fr'
                      ? `La méthode ne peut pas prendre en charge le transfert car le montant minimal de transfert est de ${cash_in_method?.min_amount}.`
                      : `This method cannot support the transfer because the minimum transfer is ${cash_in_method?.min_amount}.`}
                  </TextCp>
                </View>
              )}
            </>
          )}
        </View>
      )}

      {!isBalance && isBankTransfer && (
        <View
          style={{
            ...styles.card,
            borderColor: isSelected ? '#049D9F' : '#fff',
            ...defaultStyles.boxShow,
            shadowColor: isSelected ? colors.primary : '#000',
          }}>
          <TouchableOpacity
            disabled={disabled}
            onPress={() => {
              saveTransfer({cashInMethod});
            }}>
            <View style={styles.cardHeader}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginBottom: 10,
                }}>
                {renderIcon(
                  cash_in_method?.name.toLowerCase().split(' ').join('_'),
                )}
                <TextCp textType={'bold'} style={{fontSize: 15}}>
                  {translate(
                    cash_in_method?.name.toLowerCase().split(' ').join('_'),
                  )}
                </TextCp>
              </View>
            </View>

            <TextCp>{translate('bank_transfer_subtitle')}</TextCp>
          </TouchableOpacity>

          {isBankTransfer && isSelected && (
            <>
              {banks.map(bank => (
                <TouchableOpacity
                  onPress={() => selectBankTransfer(bank)}
                  style={styles.checkBoxContainer}>
                  <View
                    style={{
                      ...styles.checkBox,
                      backgroundColor:
                        bankTransferType?.bank_name === bank.bank_name
                          ? '#035357'
                          : '#eee',
                    }}>
                    {bankTransferType?.bank_name === bank.bank_name && (
                      <Icon name="checkmark" color="#fff" size={16} />
                    )}
                  </View>
                  <View>
                    <TextCp textType={'bold'}>{bank.bank_name}</TextCp>
                  </View>
                </TouchableOpacity>
              ))}
            </>
          )}
          <View style={{height: 6}} />
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    marginVertical: 10,
    borderRadius: 10,
    paddingHorizontal: 8,
    marginHorizontal: 3,
    borderWidth: 1,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    // alignItems: 'center',
  },
  cardHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardFooter: {
    paddingVertical: 8,
  },
  alert: {
    padding: 8,
    backgroundColor: '#F7C3C3',
    borderRadius: 8,
    marginBottom: 8,
  },
  checkBox: {
    height: 25,
    width: 25,
    borderRadius: 7,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  boldText: {
    fontWeight: 'bold',
  },
  checkBoxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    marginLeft: 10,
  },
  checkBoxInner: {
    height: 25,
    width: 25,
    borderRadius: 6,
    marginHorizontal: 20,
    justifyContent: 'center',
  },
  recommended: {
    backgroundColor: 'rgba(54,255,164,75)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
    padding: 5,
  },
});

export default CashInMethodComp;
