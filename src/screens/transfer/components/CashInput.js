import {View, TextInput, StyleSheet, Dimensions} from 'react-native';
import React from 'react';
import TextCp from '../../../components/TextCp';

const height = Dimensions.get('screen').height;

const CashInput = ({currency, onChange, value, label, error = null}) => {
  return (
    <View style={styles.outter}>
      <View style={styles.container}>
        <View style={styles.headerContainer}>
          <TextCp style={styles.text} opacity={0.5}>
            {label}
          </TextCp>
        </View>
        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            value={value}
            onChangeText={onChange}
            // onEndEditing={onChange}
            inputMode="numeric"
            keyboardType={'number-pad'}
          />
          <View style={styles.currencyContainer}>
            <TextCp textType={'regular'} style={styles.currency} opacity={0.75}>
              {currency === 'XOF' ? 'CFA' : currency}
            </TextCp>
          </View>
        </View>
      </View>
      {error && (
        <TextCp color="#c1121f" style={styles.error}>
          {error}
        </TextCp>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  outter: {
    marginVertical: 4,
  },
  container: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.2)',
    borderRadius: 10,
  },
  headerContainer: {
    paddingHorizontal: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    // backgroundColor: 'red',
  },
  input: {
    flex: 1,
    backgroundColor: '#fff',
    paddingHorizontal: 8,
    height: height * 0.05,
    fontFamily: 'Inter-Bold',
    fontWeight: 'bold',
    borderRadius: 8,
    fontSize: 17,
    opacity: 0.75,
    color: 'rgba(0,0,0,.75)',
  },
  currencyContainer: {
    paddingHorizontal: 10,
  },
  text: {
    fontSize: 13,
    marginTop: 2,
    opacity: 0.5,
  },
  currency: {},
  error: {
    fontSize: 12,
  },
});

export default CashInput;
