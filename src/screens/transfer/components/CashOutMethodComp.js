import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import React, {useEffect, useMemo, useState} from 'react';
import MIcon from 'react-native-vector-icons/MaterialCommunityIcons';
import TextCp from '../../../components/TextCp';
import {translate} from '../../../utilities/Translate';
import useDanaStore from '../../../app_state/store';
import BankCashOut from './BankCashOut';
import Icon from 'react-native-vector-icons/Ionicons';
import MobileMoneyCashOut from './MobileMoneyCashOut';
import {colors} from '../../../theme';
import useGetLang from '../../../Hooks/useGetLang';
import {
  calculateFees,
  getAppliedFeesForWithdraws,
} from '../../../apis/Transfers';
import defaultStyles from '../../../defaultStyles';
import Microfinance from './Microfinance';
import NeeroWallet from './NeeroWallet';
//
const DELAY = {
  'bank payout internal': '72h',
  'bank payout by hub 2': '72h',
  'bank payout by alooh pay': '72h',
  'bank payout external': '72h',
  'mobile money by hub2': '1h',
  'mobile money internal': '1h',
  'mobile money by alooh pay': '1h',
  'mfi by aloohpay': '72h',
  delivery: '6h',
};

const CashOutMethodComp = ({cashOut}) => {
  const lang = useGetLang();
  const transfer = useDanaStore(state => state.transfer);
  const user = useDanaStore(state => state.user);
  const beneficiary = useDanaStore(state => state.beneficiary);
  const saveTransfer = useDanaStore(state => state.saveTransfer);
  const sendingCountry = useDanaStore(state => state.sendingCountry);
  const {cash_out_method, cashout_method} = cashOut;
  const _payment_type = cashout_method.payment_type?.name;

  const method = cash_out_method.name.trim();

  const [cashOutFees, setCashOutFees] = useState(null);
  const [cashInFees, setCashInFees] = useState(null);
  const delay = DELAY[method.toLowerCase()];

  const exceedesLimit = +transfer?.amount_in_euro > cash_out_method.max_amount;
  const belowLimit = +transfer?.amount_in_euro < cash_out_method.min_amount;

  const selected = transfer?.cashoutMethod?.id === cashOut.id;

  const opacity = exceedesLimit || belowLimit ? 0.5 : selected ? 1 : 0.75;

  const getFees = async () => {
    if (exceedesLimit) return;
    if (belowLimit) return;

    const resposne = await getAppliedFeesForWithdraws(
      {
        euro_amount: transfer.amount_in_euro,
        country_id: beneficiary?.country?.id,
        cashout_method_id: cash_out_method.id,
      },
      user?.id,
    );
    setCashOutFees(resposne);
  };

  const feesTotal = useMemo(() => {
    const cin = +cashInFees?.fee || 0;
    const cout = +cashOutFees?.fee || 0;
    return (cin + cout).toFixed(2);
  }, [cashInFees, cashOutFees]);

  const getCashInFees = async () => {
    try {
      const body = {
        euro_amount: transfer?.amount_in_euro,
        sending_country_id: sendingCountry.id,
        receiving_country_id: beneficiary?.country?.id,
        cashin_method_id:
          sendingCountry?.cash_in_methods[0]?.cash_in_method?.id,
      };
      const result = await calculateFees(body, user?.id);
      setCashInFees(result);
    } catch (error) {}
  };

  const renderIcon = type => {
    if (
      [
        'bank_transfer',
        'manual_bank_transfer',
        'instant_bank_transfer',
        'mfi',
      ].includes(type)
    ) {
      return <MIcon name="bank-outline" size={18} color="#282828" />;
    } else if (type === 'delivery') {
      return <Icon name="bicycle" size={18} color="#282828" />;
    } else if (['mobile_money'].includes(type)) {
      return <Icon name="call-outline" size={18} color="#282828" />;
    } else if (['wallet'].includes(type)) {
      return <Icon name="wallet-outline" size={18} color="#282828" />;
    }
  };

  useEffect(() => {
    getFees();
    getCashInFees();
  }, []);

  if (
    _payment_type === 'wallet' &&
    beneficiary?.country?.country_code !== '237'
  ) {
    return null;
  }

  return (
    <View
      style={{
        ...styles.card,
        borderColor: selected ? '#049D9F' : '#fff',
        ...defaultStyles.boxShow,
        shadowColor: selected ? colors.primary : '#000',
      }}>
      <TouchableOpacity
        style={styles.cardHeader}
        disabled={exceedesLimit || belowLimit}
        onPress={() =>
          saveTransfer({
            cashoutMethod: cashOut,
            witdrawal_fees: cashOutFees,
            MMOperator: null,
            MMSelectedPhone: null,
            MMEnteredPhone: null,
            selectedIban: null,
            enteredIban: null,
            bankName: null,
          })
        }>
        <View style={styles.cardHeaderLeft}>
          {renderIcon(_payment_type)}
          <TextCp
            textType={'bold'}
            opacity={opacity}
            style={{marginLeft: 4, fontSize: 13}}>
            {translate(method.toLowerCase().split(' ').join('_'))}
          </TextCp>
        </View>
        <View style={styles.cardHeaderRight}>
          <View style={styles.item}>
            <TextCp color="rgba(0,0,0,.5)" style={styles.itemText}>
              {translate('fees')}
            </TextCp>
            <TextCp textType={'bold'} opacity={opacity}>
              {feesTotal || '0.0'} €
            </TextCp>
          </View>
          <View style={styles.item}>
            <TextCp color="rgba(0,0,0,.5)" style={styles.itemText}>
              {translate('delay')}
            </TextCp>
            <TextCp textType={'bold'} opacity={opacity}>
              {delay}
            </TextCp>
          </View>
          <View style={styles.item}>
            <TextCp color="rgba(0,0,0,.5)" style={styles.itemText}>
              {translate('limit')}
            </TextCp>
            <TextCp textType={'bold'} opacity={opacity}>
              {cash_out_method.max_amount}€
            </TextCp>
          </View>
          <Icon
            name={selected ? 'chevron-down-sharp' : 'chevron-forward'}
            size={20}
          />
        </View>
      </TouchableOpacity>
      {exceedesLimit && (
        <View style={styles.exceedesLimit}>
          <Icon
            name="close-circle-outline"
            color="#963B3B"
            size={30}
            style={{marginRight: 4}}
          />
          <View style={{flex: 1}}>
            {lang.toLowerCase() === 'fr' ? (
              <TextCp color="#963B3B" style={{fontSize: 12}}>
                Vous ne pouvez pas envoyer plus que {cash_out_method.max_amount}{' '}
                euros
              </TextCp>
            ) : (
              <TextCp color="#963B3B" style={{fontSize: 12}}>
                You cannot send more than {cash_out_method.max_amount} euros
              </TextCp>
            )}
          </View>
        </View>
      )}
      {belowLimit && (
        <View style={styles.exceedesLimit}>
          <Icon
            name="close-circle-outline"
            color="#963B3B"
            size={30}
            style={{marginRight: 4}}
          />
          <View style={{flex: 1}}>
            {lang.toLowerCase() === 'fr' ? (
              <TextCp color="#963B3B" style={{fontSize: 12}}>
                Vous ne pouvez envoyer moins que {cash_out_method.min_amount}{' '}
                euros
              </TextCp>
            ) : (
              <TextCp color="#963B3B" style={{fontSize: 12}}>
                You cannot send less than {cash_out_method.min_amount} euros
              </TextCp>
            )}
          </View>
        </View>
      )}
      {selected && (
        <>
          {[
            'bank_transfer',
            'manual_bank_transfer',
            'instant_bank_transfer',
          ].includes(_payment_type) && (
            <View style={styles.cardBody}>
              <BankCashOut method={cashOut} />
            </View>
          )}

          {['mobile_money'].includes(_payment_type) && (
            <View style={styles.cardBody}>
              <MobileMoneyCashOut method={cashOut} />
            </View>
          )}

          {_payment_type === 'mfi' && (
            <View style={styles.cardBody}>
              <Microfinance method={cashOut} />
            </View>
          )}

          {_payment_type === 'wallet' &&
            beneficiary?.country?.country_code == '237' && (
              <View style={styles.cardBody}>
                <NeeroWallet method={cashOut} />
              </View>
            )}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    marginVertical: 10,
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 8,
    borderWidth: 1,
    ...defaultStyles.boxShow,
    marginHorizontal: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    paddingVertical: 8,
  },
  cardHeaderLeft: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
  },
  cardHeaderRight: {
    flex: 2,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  item: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemText: {
    fontSize: 10,
  },
  exceedesLimit: {
    marginTop: 8,
    padding: 8,
    backgroundColor: '#F7C3C3',
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 6,
  },
  checkBox: {
    backgroundColor: 'rgba(0,0,0,.3)',
    height: 24,
    width: 24,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  cardBody: {
    paddingTop: 10,
  },
});
export default CashOutMethodComp;
