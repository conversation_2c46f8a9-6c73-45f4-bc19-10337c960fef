import {
  View,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import React, {useState, useEffect} from 'react';
import Icon from 'react-native-vector-icons/Feather';
import TextCp from '../../../components/TextCp';
import useDanaStore from '../../../app_state/store';
import SelectInput from '../../../components/SelectInput';
import Input from '../../../components/Input';
import {translate} from '../../../utilities/Translate';
import {getBankAccountsByUserId} from '../../../apis/auth';
import {colors} from '../../../theme';
import Divider from '../../../components/Divider';
import {getInstitutions} from '../../../apis/Transfers';

const Microfinance = ({method}) => {
  const [bankAccounts, setBankccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const transfer = useDanaStore(state => state.transfer);
  const saveTransfer = useDanaStore(state => state.saveTransfer);
  const beneficiary = useDanaStore(state => state.beneficiary);
  const selectedMethod = transfer.cashoutMethod;
  const [institutions, setInstitutions] = useState([]);

  const full_name = beneficiary.is_individual
    ? beneficiary?.full_name
    : beneficiary?.company?.name;

  const getUserBankAccount = async () => {
    try {
      setLoading(true);
      const response = await getBankAccountsByUserId(beneficiary.id);
      if (response?.data && response?.data?.length > 0) {
        setBankccounts(response.data);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const _getInstitutions = async () => {
    const res = await getInstitutions(
      method.cash_out_method.payment_provider.name,
      beneficiary.country.country_code,
    );
    setInstitutions(res.data);
  };

  useEffect(() => {
    getUserBankAccount();
    _getInstitutions();
  }, []);

  return (
    <View>
      <TextCp opacity={0.75}>{translate('bank_title')}</TextCp>

      <SelectInput
        items={institutions?.map(val => ({
          value: val.id,
          label: val.name,
        }))}
        value={transfer?.bankName?.id}
        onValueChange={text => {
          const _bank = institutions?.find(
            bank => bank?.id?.toString() === text.toString(),
          );
          saveTransfer({bankName: _bank});
        }}
        placeholder={{
          label: translate('select_bank_place_holder'),
          value: translate('select_bank_place_holder'),
        }}
      />

      <View style={styles.userBox}>
        <TextCp opacity={0.5} style={{fontSize: 12}}>
          {translate('account_owner_texr')}
        </TextCp>
        <TextCp textType={'bold'} style={styles.full_name_text}>
          {full_name}
        </TextCp>
      </View>

      {loading && (
        <View style={styles.loading}>
          <ActivityIndicator size="small" color={colors.primary} />
        </View>
      )}

      {bankAccounts?.length > 0 && (
        <View style={{marginVericla: 16, paddingVertical: 8}}>
          {bankAccounts?.map(val => (
            <TouchableOpacity
              style={styles.listItem}
              onPress={() => {
                saveTransfer({selectedIban: val, enteredIban: ''});
              }}>
              <View
                style={{
                  ...styles.checkBox,
                  backgroundColor:
                    transfer?.selectedIban &&
                    transfer?.selectedIban?.id === val.id
                      ? colors.primary
                      : 'rgba(0,0,0,.3)',
                }}>
                {transfer?.selectedIban &&
                  transfer?.selectedIban?.id === val.id && (
                    <Icon name="check" size={17} color="#fff" />
                  )}
              </View>
              <View style={{flex: 1}}>
                <TextCp style={{fontSize: 14}} textType={'bold'}>
                  {val?.iban}
                </TextCp>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      )}
      <Divider height={10} />
      <Input
        label={translate('enter_iban')}
        value={transfer?.enteredIban}
        onChange={text => {
          saveTransfer({selectedIban: null, enteredIban: text});
        }}
        backgroundColor="#fff"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  userBox: {
    backgroundColor: 'rgba(0,0,0,.07)',
    padding: 8,
    borderRadius: 8,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  checkBox: {
    height: 24,
    width: 24,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  loading: {
    marginVertical: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
  full_name_text: {
    textTransform: 'capitalize',
  },
});

export default Microfinance;
