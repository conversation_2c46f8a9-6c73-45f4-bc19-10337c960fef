import {
  View,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import React, {useState, useEffect} from 'react';
import Icon from 'react-native-vector-icons/Feather';
import TextCp from '../../../components/TextCp';
import SelectInput from '../../../components/SelectInput';
import useDanaStore from '../../../app_state/store';
import Input from '../../../components/Input';
import {translate} from '../../../utilities/Translate';
import {getMMAccountsByUserId} from '../../../apis/auth';
import {colors} from '../../../theme';
import {getInstitutions} from '../../../apis/Transfers';

const MobileMoneyCashOut = ({method}) => {
  const [mMAccounts, setMMccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const transfer = useDanaStore(state => state.transfer);
  const saveTransfer = useDanaStore(state => state.saveTransfer);
  const beneficiary = useDanaStore(state => state.beneficiary);
  const [institutions, setInstitutions] = useState([]);
  const selectedMethod = transfer?.cashoutMethod || null;

  const getUserMMAccount = async () => {
    try {
      setLoading(true);
      const resposne = await getMMAccountsByUserId(beneficiary.id);
      if (resposne.data && resposne.data.length > 0) {
        const uniqueData = Array.from(
          new Map(
            resposne.data.map(item => [item.phone_number, item]),
          ).values(),
        );
        setMMccounts(resposne.data);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const _getInstitutions = async () => {
    const res = await getInstitutions(
      method?.cash_out_method?.payment_provider?.name,
      beneficiary?.country?.country_code,
    );
    setInstitutions(
      res.data.filter(val => val?.institution?.type === 'mobile_money'),
    );
  };

  useEffect(() => {
    getUserMMAccount();
    _getInstitutions();
  }, []);

  return (
    <View>
      <TextCp opacity={0.75}>{translate('mobile_money_title')}</TextCp>
      <SelectInput
        items={institutions?.map(val => ({
          value: val.id,
          label: val.name,
        }))}
        value={transfer?.MMOperator?.id}
        onValueChange={text => {
          const _mm = institutions?.find(
            mm => mm?.id?.toString() === text?.toString(),
          );
          saveTransfer({MMOperator: _mm});
        }}
        placeholder={{
          label: translate('select_operator'),
          value: translate('select_operator'),
        }}
      />
      {loading && (
        <View style={styles.loading}>
          <ActivityIndicator size="small" color={colors.primary} />
        </View>
      )}

      {mMAccounts?.length > 0 && (
        <View style={{marginVericla: 16, paddingVertical: 4}}>
          {mMAccounts?.map(val => (
            <TouchableOpacity
              style={styles.listItem}
              onPress={() => {
                saveTransfer({MMSelectedPhone: val, MMEnteredPhone: ''});
              }}>
              <View
                style={{
                  ...styles.checkBox,
                  backgroundColor:
                    transfer?.MMSelectedPhone &&
                    transfer?.MMSelectedPhone?.id === val?.id
                      ? colors.primary
                      : 'rgba(0,0,0,.3)',
                }}>
                {transfer?.MMSelectedPhone &&
                  transfer?.MMSelectedPhone?.id === val.id && (
                    <Icon name="check" size={18} color="#fff" />
                  )}
              </View>
              <View style={{flex: 1}}>
                <TextCp
                  textType={
                    'bold'
                  }>{`+${val?.account?.country_code} ${val?.phone_number}`}</TextCp>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      )}

      <View style={styles.phoneContainer}>
        <View style={styles.code}>
          <TextCp>{`+${beneficiary?.country_code}`}</TextCp>
        </View>

        <Input
          label={translate('PhoneNumber_placeholder')}
          value={transfer?.MMEnteredPhone}
          onChange={text => {
            saveTransfer({MMSelectedPhone: null, MMEnteredPhone: text});
          }}
          style={styles.phoneInput}
          backgroundColor="#fff"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  phoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  code: {
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ccc',
    marginRight: 10,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  phoneInput: {
    flex: 1,
  },

  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },

  checkBox: {
    height: 24,
    width: 24,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 8,
  },
  loading: {
    marginVertical: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
});

export default MobileMoneyCashOut;
