import {View, Text, StyleSheet, ActivityIndicator} from 'react-native';
import React, {useEffect, useState} from 'react';
import TextCp from '../../../components/TextCp';
import {translate} from '../../../utilities/Translate';
import {
  getAllUserNeeroWallets,
  getAllUserNeeroWalletsById,
} from '../../../apis/Transfers';
import SelectInput from '../../../components/SelectInput';
import useDanaStore from '../../../app_state/store';
import Input from '../../../components/Input';
import {colors} from '../../../theme';

const NeeroWallet = () => {
  const [wallets, setWallets] = useState([]);
  const saveTransfer = useDanaStore(state => state.saveTransfer);
  const transfer = useDanaStore(state => state.transfer);
  const beneficiary = useDanaStore(state => state.beneficiary);
  const [loading, setLoading] = useState(false);

  const getNeeroWallets = async () => {
    try {
      setLoading(true);
      const resposne = await getAllUserNeeroWalletsById(beneficiary.id);
      setWallets(resposne.data.data);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };

  useEffect(() => {
    getNeeroWallets();
  }, []);

  return (
    <View>
      <TextCp>
        {translate('neero_description')}
        <TextCp textType="bold">{translate('neero_wallet')}</TextCp>
      </TextCp>
      <View>
        {loading && (
          <View>
            <ActivityIndicator size={'small'} color={colors.primary} />
          </View>
        )}
        {wallets.length > 0 && (
          <SelectInput
            items={wallets?.map(val => ({
              value: val.account_id,
              label: val.wallet_account_number,
            }))}
            value={transfer?.neeroWallet?.account_id}
            onValueChange={text => {
              const neeroWallet = wallets?.find(
                mm => mm?.account_id === text?.toString(),
              );
              saveTransfer({neeroWallet, neeroWalletNumber: null});
            }}
            placeholder={{
              label: translate('choose_phone_number'),
              value: translate('choose_phone_number'),
            }}
          />
        )}
        <View style={styles.phoneContainer}>
          <View style={styles.code}>
            <TextCp>{`+${beneficiary?.country_code}`}</TextCp>
          </View>

          <Input
            label={translate('PhoneNumber_placeholder')}
            value={transfer?.neeroWalletNumber}
            onChange={text => {
              saveTransfer({neeroWalletNumber: text, neeroWallet: null});
            }}
            style={styles.phoneInput}
            backgroundColor="#fff"
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  phoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  code: {
    paddingHorizontal: 16,
    marginRight: 10,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  phoneInput: {
    flex: 1,
  },
});

export default NeeroWallet;
