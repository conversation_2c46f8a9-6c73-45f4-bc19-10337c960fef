import {
  View,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
  Platform,
  Dimensions,
} from 'react-native';
import React, {forwardRef, useEffect, useState, useMemo, use} from 'react';
import Icon from 'react-native-vector-icons/Feather';
import {Modalize} from 'react-native-modalize';
import TextCp from '../../../components/TextCp';
import useDanaStore from '../../../app_state/store';
import {
  cancelTransfer,
  downloadInvoice,
  getInstitutions,
  getOperationsDetails,
  uploadDocs,
} from '../../../apis/Transfers';
import {translate} from '../../../utilities/Translate';
import defaultStyles from '../../../defaultStyles';
import TimelineItem from './TimelineItem';
import {blobToBase64, filterPaymentStatus} from '../../../utilities';
import RNFetchBlob from 'rn-fetch-blob';
import {showMessage} from 'react-native-flash-message';
import useGetLang from '../../../Hooks/useGetLang';
import DocumentPicker from 'react-native-document-picker';
import {ots} from '../../../transfer_data';
import Divider from '../../../components/Divider';
import Rating from './Rating';
import {ScrollView} from 'react-native-gesture-handler';
import {DELIVERY} from '../../constants';
import {set} from 'react-native-reanimated';
import {getUserProfile} from '../../../apis/auth';

const {height} = Dimensions.get('window');

const statuses = {
  failed: {color: '#F00505', background: '#F7D1CD'},
  aborted: {color: '#F00505', background: '#F7D1CD'},
  'in progress': {color: '#ED6307', background: 'rgba(229,146,21,.1)'},
  completed: {color: '#14AD00', background: '#34E00010'},
  cancelled: {color: '#F00505', background: '#F7D1CD'},
  'action required': {color: '#F00505', background: '#F7D1CD'},
  'payment aborted': {color: '#F00505', background: '#F7D1CD'},
};

const successMessage = {
  withdraw: 'withdrawal_success',
  deposit: 'deposit_success',
  'direct transfer': 'transfer_succeeded',
  transfer: 'transfer_succeeded',
  campaign_referral_reward: 'reward_success',
  campaignreferralreward: 'reward_success',
};

const OperationDetailComp = forwardRef(
  ({closeOperationModal, navigation}, ref) => {
    const user = useDanaStore(state => state.user);
    const state_countries = useDanaStore(state => state.countries);
    const selectedTransfer = useDanaStore(state => state.selectedTransfer);
    const [_selectedTransfer, setSelectedTransfer] = useState({
      ...selectedTransfer,
    });
    const saveBeneficiary = useDanaStore(state => state.saveBeneficiary);
    const saveTransfer = useDanaStore(state => state.saveTransfer);
    const [cancelling, setCancelling] = useState(false);
    const [downloading, setDownloading] = useState(false);
    const lang = useGetLang();
    const [activeTab, setActiveTab] = useState('status');
    const [uploading, setUploading] = useState(false);
    const [loading, setLoading] = useState(true);
    const [institutions, setInstitutions] = useState([]);
  const contacts = useDanaStore(state => state.contacts);

  const receiver_country = state_countries.find(
    val =>
      val.name.toLowerCase() ===
      _selectedTransfer?.receiver_country.toLowerCase(),
  );

  const sending_country = state_countries.find(
    val => val.country_code === user?.country_code,
  );

  const isActiveSender = user.id === _selectedTransfer?.sender_id;

  const isBank = ['bank account', 'bank transfer'].includes(
    _selectedTransfer?.withdraw_method_en?.toLowerCase(),
  );

  const isMobile = ['mobile money', 'mobile account'].includes(
    _selectedTransfer?.withdraw_method_en?.toLowerCase(),
  );

  const isWallet = ['neero', 'wallet'].includes(
    _selectedTransfer?.withdraw_method_en?.toLowerCase(),
  );

  const isDelivery =
    _selectedTransfer?.withdraw_method_en?.toLowerCase() === DELIVERY;

  const isDirect =
    _selectedTransfer?.operation_type?.toLowerCase() === 'direct transfer';
  const operation_type = _selectedTransfer?.operation_type
    ?.toLowerCase()
    ?.replace(/ /g, '_');

  const timeline = useMemo(() => {
    return filterPaymentStatus(_selectedTransfer?.statuses);
  }, [_selectedTransfer, filterPaymentStatus]);

  const isTransferComppleted = useMemo(() => {
    return (
      _selectedTransfer?.operation_status_en?.toLowerCase() === 'completed'
    );
  }, [_selectedTransfer]);

  const tabs = useMemo(() => ['status', 'details'], []);

  const _background =
    operation_type == 'direct_transfer'
      ? isActiveSender
        ? ots[operation_type]?.bgColor
        : ots['direct_transfer_receiver']?.bgColor
      : ots[operation_type]?.bgColor;

  const _cancelTransfer = () => {
    setCancelling(true);
    cancelTransfer(_selectedTransfer?.id)
      .then(res => {
        setCancelling(false);
        closeOperationModal();
      })
      .catch(err => {
        setCancelling(false);
      });
  };

  const _repeatTransfer = () => {
    saveBeneficiary({
      ..._selectedTransfer?.destination_user,
      country: state_countries.find(
        val =>
          val.country_code ===
          _selectedTransfer?.destination_user?.country_code,
      ),
    });

    const sendingCountry = state_countries.find(
      country => country.country_code === user?.country_code,
    );

    const recevingCountry = state_countries.find(
      country =>
        country.country_code ===
        _selectedTransfer?.destination_user?.country_code,
    );

    let cashOutMethod = null;

    if (isBank) {
      cashOutMethod = recevingCountry?.cash_out_methods?.find(
        val =>
          val.cash_out_method.payment_type?.name?.toLowerCase() ===
          'manual_bank_transfer',
      );
    } else if (isMobile) {
      cashOutMethod = recevingCountry?.cash_out_methods?.find(
        val =>
          val.cash_out_method?.payment_type?.name?.toLowerCase() ===
          'mobile_money',
      );
    }

    const cashIn = sendingCountry?.cash_in_methods?.find(
      val =>
        val.cash_in_method?.payment_type?.name?.toLowerCase() ===
        _selectedTransfer?.payment_type,
    );

    // save transfer
    saveTransfer({
      amount_in_cfa: _selectedTransfer?.transfer?.local_amount,
      amount_in_euro: _selectedTransfer?.data?.amount_without_fees_in_euro,
      reason: _selectedTransfer?.data.reason,
      cashInMethod: cashIn,
      cashoutMethod: cashOutMethod,
    });

    // send user to beneficiary screen.
    navigation.navigate('BeneficiarySelection');
  };

  const _downloadReceipr = async () => {
    setDownloading(true);
    try {
      const res = await downloadInvoice(_selectedTransfer?.id);
      const base64Data = await blobToBase64(res);

      const directory = Platform.select({
        ios: RNFetchBlob.fs.dirs.DocumentDir,
        android: RNFetchBlob.fs.dirs.DownloadDir,
      });

      const filePath = `${directory}/invoice-${_selectedTransfer?.id}.pdf`;

      const exists = await RNFetchBlob.fs.exists(filePath);
      if (exists) {
        await RNFetchBlob.fs.unlink(filePath);
      }

      await RNFetchBlob.fs.createFile(filePath, base64Data, 'base64');

      showMessage({
        type: 'success',
        message: translate('file_downloaded'),
      });
    } catch (error) {
      // Proper error handling with null checks
      if (error?.response?.status === 500) {
        showMessage({
          message: translate('invoice_available_to_senders'),
          type: 'danger',
        });
      } else {
        // Log the error for debugging
        console.error('Download invoice error:', error);

        showMessage({
          message: translate('something_went_wrong'),
          type: 'danger',
        });
      }
    } finally {
      setDownloading(false);
    }
  };

  const useAnotherMethod = async () => {
    //  beneficiary

    const _bene = contacts.find(
      val => val.favorite.id === _selectedTransfer.receiver_id,
    );

    console.log({_bene});

    saveBeneficiary({
      id: _selectedTransfer.receiver_id,
      phone_number: '',
      first_name: _selectedTransfer.receiver,
      last_name: '',
      country: receiver_country,
    });

    // transer details
    saveTransfer({
      reason: _selectedTransfer?.reason,
      amount_in_cfa: _selectedTransfer?.transfer?.local_amount,
      amount_in_euro: _selectedTransfer?.amount,
    });

    // cashin
    const _cashin = sending_country.cash_in_methods.map(
      value => value.cash_in_method.id === _selectedTransfer?.cashin_method_id,
    );

    saveTransfer({
      cashInMethod: _cashin,
    });

    // cahout if direct
    let _cashout = null;
    if (_selectedTransfer?.cashout_method_id) {
      _cashout = receiver_country?.cashout_methods.find(
        value =>
          value?.cashout_method?.id === _selectedTransfer?.cashout_method_id,
      );

      const _institution = institutions?.find(
        val =>
          val?.label?.toLowerCase() ===
            _selectedTransfer?.bank_name?.toLowerCase() ||
          val?.label?.toLowerCase() ===
            _selectedTransfer?.operator_name?.toLowerCase(),
      );

      saveTransfer({
        cashoutMethod: _cashout,
        MMOperator: isMobile
          ? {
              name: _selectedTransfer?.operator_name,
              id: _institution?.institution_id,
            }
          : null,
        MMSelectedPhone: isMobile
          ? {
              phone_number: _selectedTransfer?.phone_number,
            }
          : null,
        bankName: isBank
          ? {
              name: _selectedTransfer?.bank_name,
              id: _institution?.institution_id,
            }
          : null,
        selectedIban: isBank
          ? {
              iban: _selectedTransfer?.iban,
            }
          : null,
      });
    }
    navigation.navigate('CashInMethod');
  };

    // implement this function to upload documents to the server for the transaction that requires action from the user.
    const uploadDocuments = async () => {
      try {
        const res = await DocumentPicker.pick({
          type: [DocumentPicker.types.pdf, DocumentPicker.types.images],
          presentationStyle: 'fullScreen',
        });
        const formData = new FormData();
        formData.append('file', {
          uri: res[0].uri,
          type: res[0].type,
          name: res[0].name,
        });
        formData.append('type', 'transfer');
        setUploading(true);
        const response = await uploadDocs(user?.id, formData);
        if (response.ok) {
          showMessage({
            type: 'success',
            message: translate('file_uploaded_successfully'),
          });
        } else {
          showMessage({
            type: 'danger',
            message: translate('file_upload_failed'),
          });
        }
      } catch (err) {
        showMessage({
          type: 'danger',
          message: translate('file_upload_failed'),
        });
      } finally {
        setUploading(false);
      }
    };

    useEffect(() => {
      function getDetails() {
        setLoading(true);
        getOperationsDetails(selectedTransfer?.id)
          .then(res => {
            setSelectedTransfer(prev => ({...prev, ...res}));
          })
          .catch(err => {})
          .finally(() => {
            setLoading(false);
          });
      }
      getDetails();
    }, [selectedTransfer]);

    //get Institutios
    useEffect(() => {
      async function _getInstitutions() {
        const method = receiver_country?.cashout_methods.find(
          value =>
            value?.cashout_method?.id === _selectedTransfer?.cashout_method_id,
        );
        const res = await getInstitutions(
          method?.cashout_method?.payment_provider?.name,
          receiver_country?.country_code,
        );
        if (isBank) {
          setInstitutions(
            res.data.filter(val => val?.institution?.type === 'bank_transfer'),
          );
        } else {
          setInstitutions(
            res.data.filter(val => val?.institution?.type === 'mobile_money'),
          );
        }
      }
      _getInstitutions();
    }, [receiver_country, _selectedTransfer, isBank]);

    return (
      <Modalize ref={ref} modalHeight={height * 0.85}>
        {loading ? (
          <View
            style={{
              height: height * 0.85,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <ActivityIndicator size="large" color={'teal'} />
          </View>
        ) : (
          <>
            <ScrollView
              contentContainerStyle={{
                flex: 1,
                paddingVertical: 16,
                height: height * 0.85,
              }}>
              <View style={styles.modal}>
                <View
                  style={{
                    ...styles.status,
                    backgroundColor: _background,
                  }}>
                  {operation_type == 'direct_transfer' ? (
                    <>
                      {_selectedTransfer?.operation_direction?.toLowerCase() ===
                      'sending' ? (
                        <Icon
                          name={ots[operation_type]?.icon}
                          size={24}
                          style={styles.icon}
                          color={ots[operation_type]?.iconColor}
                        />
                      ) : (
                        <Icon
                          name={ots['direct_transfer_receiver'].icon}
                          size={24}
                          style={styles.icon}
                          color={ots['direct_transfer_receiver']?.iconColor}
                        />
                      )}
                    </>
                  ) : (
                    <>
                      <Icon
                        name={ots[operation_type]?.icon}
                        size={24}
                        color={ots[operation_type]?.iconColor}
                        style={styles.icon}
                      />
                    </>
                  )}
                </View>

                <View style={styles.details}>
                  <TextCp opacity={0.5} style={{fontSize: 12}}>
                    {[
                      'campaignreferralreward',
                      'campaign_referral_reward',
                    ].includes(operation_type)
                      ? translate('reward')
                      : isActiveSender
                      ? translate('sending')
                      : translate('receiving')}
                  </TextCp>
                  <TextCp weight="bold" style={styles.cash}>
                    {['withdraw', 'deposit'].includes(operation_type) && (
                      <>
                        {_selectedTransfer?.receiver_country.toLowerCase() ===
                        'guinea'
                          ? `${_selectedTransfer?.patch?.received_amount_in_receiver_currency}`
                          : `${_selectedTransfer?.received_amount_in_receiver_currency}`}
                      </>
                    )}

                    {[
                      'transfer',
                      'direct_transfer',
                      'instant_transfer',
                      'campaignreferralreward',
                      'campaign_referral_reward',
                    ].includes(operation_type) && (
                      <>{_selectedTransfer?.amount_in_sender_currency}</>
                    )}
                  </TextCp>
                  <TextCp weight="bold" opacity={0.5} style={styles.full_name}>
                    {isActiveSender
                      ? _selectedTransfer.receiver
                      : _selectedTransfer.sender}
                  </TextCp>
                  <View
                    style={[
                      styles.badge,
                      {
                        backgroundColor:
                          statuses[
                            _selectedTransfer?.operation_status_en?.toLowerCase()
                          ]?.background,
                      },
                    ]}>
                    <TextCp
                      textType="bold"
                      style={{
                        fontSize: 12,
                      }}
                      color={
                        statuses[
                          _selectedTransfer?.operation_status_en?.toLowerCase()
                        ]?.color
                      }>
                      {lang === 'fr'
                        ? _selectedTransfer?.operation_status_fr
                        : _selectedTransfer?.operation_status_en}
                    </TextCp>
                  </View>
                </View>

                <View style={styles.tabs}>
                  {tabs.map((_tab, index) => (
                    <TouchableOpacity
                      onPress={() => setActiveTab(_tab)}
                      key={index}
                      style={{
                        ...styles.tab,
                        backgroundColor:
                          activeTab === _tab ? '#fff' : 'transparent',
                      }}>
                      <TextCp style={{fontSize: 12}}>{translate(_tab)}</TextCp>
                    </TouchableOpacity>
                  ))}
                </View>

                {activeTab === 'details' ? (
                  <>
                    <View style={styles.section}>
                      <TextCp weight="bold" align="center" style={styles.title}>
                        {translate('transaction_detail')}
                      </TextCp>
                    </View>

                    {operation_type === 'withdraw' && (
                      <View>
                        <View style={styles.section}>
                          <View style={styles.row}>
                            <TextCp opacity={0.5} style={styles.subTitle}>
                              {translate('amount_received')}
                            </TextCp>
                            <TextCp
                              weight="bold"
                              opacity={0.75}
                              style={styles.title}>
                              {_selectedTransfer?.receiver_country.toLowerCase() ===
                              'guinea'
                                ? `${_selectedTransfer?.patch?.received_amount_in_receiver_currency} ${_selectedTransfer?.patch?.receiver_currency}`
                                : `${_selectedTransfer?.received_amount_in_receiver_currency} ${_selectedTransfer?.receiver_currency}`}
                            </TextCp>
                          </View>

                          <View style={styles.row}>
                            <TextCp textType={'bold'} color={'#037375'}>
                              {translate('mode_de_payment')}
                            </TextCp>
                            <TextCp textType={'bold'} color={'#037375'}>
                              {lang === 'fr'
                                ? _selectedTransfer?.withdraw_method_fr
                                : _selectedTransfer?.withdraw_method_en}
                            </TextCp>
                          </View>

                          {isBank && (
                            <>
                              <Divider height={20} />
                              <View style={styles.row}>
                                <TextCp opacity={0.5}>
                                  {translate('bank')}
                                </TextCp>
                                <TextCp opacity={0.5}>
                                  {_selectedTransfer?.bank_name}
                                </TextCp>
                              </View>

                              <View style={styles.row}>
                                <TextCp textType={'bold'} color={'#037375'}>
                                  IBAN
                                </TextCp>
                                <TextCp textType={'bold'} color={'#037375'}>
                                  {_selectedTransfer?.iban}
                                </TextCp>
                              </View>
                            </>
                          )}

                          {isMobile && (
                            <>
                              <Divider height={20} />

                              <View style={styles.row}>
                                <TextCp opacity={0.5}>
                                  {translate('operator')}
                                </TextCp>
                                <TextCp opacity={0.5}>
                                  {_selectedTransfer?.operator_name}
                                </TextCp>
                              </View>

                              <View style={styles.row}>
                                <TextCp
                                  opacity={0.5}
                                  textType={'bold'}
                                  color={'#037375'}>
                                  {translate('number')}
                                </TextCp>
                                <TextCp textType={'bold'} color={'#037375'}>
                                  {`${_selectedTransfer?.country_code}${_selectedTransfer?.phone_number}`}
                                </TextCp>
                              </View>
                            </>
                          )}
                        </View>
                      </View>
                    )}

                    {operation_type === 'deposit' && (
                      <View>
                        <View style={styles.section}>
                          <View style={styles.row}>
                            <TextCp opacity={0.5} style={styles.subTitle}>
                              {translate('amount')}
                            </TextCp>
                            <TextCp
                              weight="bold"
                              opacity={0.75}
                              style={styles.title}>
                              {`${_selectedTransfer?.patch?.received_amount_in_receiver_currency} ${_selectedTransfer?.patch?.receiver_currency}`}
                            </TextCp>
                          </View>

                          <View style={styles.row}>
                            <TextCp opacity={0.5}>
                              {translate('mode_de_payment')}
                            </TextCp>
                            <TextCp opacity={0.5}>
                              {translate(_selectedTransfer?.payment_type)}
                            </TextCp>
                          </View>
                        </View>
                      </View>
                    )}

                    {operation_type === 'direct_transfer' && (
                      <View>
                        <View style={styles.section}>
                          <View style={styles.row}>
                            <TextCp opacity={0.5} style={styles.subTitle}>
                              {translate('amount_sent')}
                            </TextCp>
                            <TextCp
                              weight="bold"
                              opacity={0.75}
                              style={styles.title}>
                              {_selectedTransfer?.amount_in_sender_currency}
                            </TextCp>
                          </View>

                          {_selectedTransfer?.operation_direction?.toLowerCase() ===
                            'sending' && (
                            <View style={styles.row}>
                              <TextCp opacity={0.5}>
                                {translate('fee_paid')}
                              </TextCp>
                              <TextCp opacity={0.5}>
                                {_selectedTransfer?.fee_in_sender_currency} EUR
                              </TextCp>
                            </View>
                          )}

                          {_selectedTransfer?.payment_type && (
                            <View style={styles.row}>
                              <TextCp opacity={0.5}>
                                {translate('mode_de_payment')}
                              </TextCp>
                              <TextCp opacity={0.5}>
                                {translate(_selectedTransfer?.payment_type)}
                              </TextCp>
                            </View>
                          )}

                          <Divider height={20} />

                          <View style={styles.row}>
                            <TextCp opacity={0.5}>
                              {translate('amount_received')}
                            </TextCp>
                            {}
                            <TextCp style={styles.teal}>
                              {`${_selectedTransfer?.patch?.received_amount_in_receiver_currency} ${_selectedTransfer?.patch?.receiver_currency}`}
                            </TextCp>
                          </View>

                          <View style={styles.row}>
                            <TextCp
                              style={styles.teal}
                              weight="bold"
                              color="#037375">
                              {translate('method_of_receipt')}
                            </TextCp>
                            <TextCp
                              weight="bold"
                              color="#037375"
                              style={styles.teal}>
                              {lang === 'fr'
                                ? _selectedTransfer?.withdraw_method_fr
                                : _selectedTransfer?.withdraw_method_en}
                            </TextCp>
                          </View>

                          {isMobile && (
                            <View>
                              <View style={styles.row}>
                                <TextCp opacity={0.5}>
                                  {translate('operator')}
                                </TextCp>
                                <TextCp opacity={0.5}>
                                  {_selectedTransfer?.operator_name}
                                </TextCp>
                              </View>

                              <View style={styles.row}>
                                <TextCp
                                  style={styles.teal}
                                  weight="bold"
                                  color="#037375">
                                  {translate('phone_number')}
                                </TextCp>
                                <TextCp
                                  style={styles.teal}
                                  weight="bold"
                                  color="#037375">
                                  {`+${_selectedTransfer?.country_code}${_selectedTransfer?.phone_number}`}
                                </TextCp>
                              </View>
                            </View>
                          )}

                          {isBank && (
                            <View>
                              <View style={styles.row}>
                                <TextCp opacity={0.5}>
                                  {translate('bank')}
                                </TextCp>
                                <TextCp opacity={0.5}>
                                  {_selectedTransfer?.bank_name}
                                </TextCp>
                              </View>

                              <View style={styles.row}>
                                <TextCp
                                  style={styles.teal}
                                  weight="bold"
                                  color="#037375">
                                  IBAN
                                </TextCp>
                                <TextCp
                                  style={styles.teal}
                                  weight="bold"
                                  color="#037375">
                                  {_selectedTransfer?.iban}
                                </TextCp>
                              </View>
                            </View>
                          )}

                          {isWallet && (
                            <>
                              <View style={styles.row}>
                                <TextCp opacity={0.5}>
                                  {translate('account_number')}
                                </TextCp>
                                <TextCp opacity={0.5}>
                                  {_selectedTransfer?.account_number}
                                </TextCp>
                              </View>
                            </>
                          )}
                        </View>
                      </View>
                    )}

                    {_selectedTransfer?.operation_type?.toLowerCase() ===
                      'instant_transfer' && (
                      <View>
                        <View style={styles.section}>
                          <View style={styles.row}>
                            <TextCp opacity={0.5} style={styles.subTitle}>
                              {translate('amount_sent')}
                            </TextCp>
                            <TextCp
                              weight="bold"
                              opacity={0.75}
                              style={styles.title}>
                              {
                                _selectedTransfer?.sent_amount_in_sender_currency
                              }
                            </TextCp>
                          </View>

                          {isActiveSender && (
                            <View style={styles.row}>
                              <TextCp opacity={0.5}>
                                {translate('fee_paid')}
                              </TextCp>
                              <TextCp opacity={0.5}>
                                {_selectedTransfer?.fee_in_sender_currency} EUR
                              </TextCp>
                            </View>
                          )}

                          {_selectedTransfer?.payment_type && (
                            <View style={styles.row}>
                              <TextCp opacity={0.5}>
                                {translate('mode_de_payment')}
                              </TextCp>
                              <TextCp opacity={0.5}>
                                {translate(
                                  _selectedTransfer?.payment_type || '',
                                )}
                              </TextCp>
                            </View>
                          )}
                        </View>
                      </View>
                    )}

                    {_selectedTransfer?.operation_type?.toLowerCase() ===
                      'campaignreferralreward' && (
                      <View>
                        <View style={styles.section}>
                          <View style={styles.row}>
                            <TextCp opacity={0.5} style={styles.subTitle}>
                              {translate('amount_sent')}
                            </TextCp>
                            <TextCp
                              weight="bold"
                              opacity={0.75}
                              style={styles.title}>
                              {
                                _selectedTransfer?.sent_amount_in_sender_currency
                              }
                            </TextCp>
                          </View>

                          {_selectedTransfer?.payment_type && (
                            <View style={styles.row}>
                              <TextCp opacity={0.5}>
                                {translate('mode_de_payment')}
                              </TextCp>
                              <TextCp opacity={0.5}>
                                {translate(
                                  _selectedTransfer?.payment_type || '',
                                )}
                              </TextCp>
                            </View>
                          )}
                        </View>
                      </View>
                    )}
                  </>
                ) : (
                  <>
                    {_selectedTransfer?.operation_status_en?.toLowerCase() ===
                    'action required' ? (
                      <>
                        <View style={styles.action_required}>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              marginBottom: 10,
                            }}>
                            <Icon
                              name="alert-triangle"
                              size={20}
                              color="#F5A05B"
                              style={{marginRight: 10}}
                            />
                            <TextCp textType={'bold'}>
                              {translate('action_required_title')}
                            </TextCp>
                          </View>
                          <TextCp style={{fontSize: 13}}>
                            {translate('action_required_text')}{' '}
                            <TextCp textType={'bold'}>
                              <EMAIL>
                            </TextCp>
                          </TextCp>
                        </View>
                      </>
                    ) : (
                      <View style={styles.statuses}>
                        {timeline?.map((history, index) => (
                          <TimelineItem
                            completed={history?.datetime}
                            history={history}
                            isLast={timeline.length === index + 1}
                            key={index.toString()}
                          />
                        ))}

                        {_selectedTransfer?.operation_status_en?.toLowerCase() ===
                          'completed' && (
                          <TextCp
                            style={{marginLeft: 40, marginVertical: 8}}
                            textType={'bold'}
                            color="#000">
                            {translate(
                              successMessage[
                                _selectedTransfer?.operation_type?.toLowerCase()
                              ],
                            )}
                          </TextCp>
                        )}
                      </View>
                    )}
                  </>
                )}
              </View>
            </ScrollView>

            <View style={styles.footer}>
              {activeTab === 'details' ? (
                <TouchableOpacity
                  onPress={_downloadReceipr}
                  disabled={downloading}
                  style={{
                    ...defaultStyles.button,
                    borderColor: '#eee',
                    marginHorizontal: 0,
                    flexDirection: 'row',
                  }}>
                  {downloading ? (
                    <ActivityIndicator color="#fff" size="small" />
                  ) : (
                    <>
                      <Icon
                        name="download"
                        color="#fff"
                        size={24}
                        style={{marginRight: 10}}
                      />
                      <TextCp color="#fff" textType={'bold'}>
                        {translate('download_the_receipt')}
                      </TextCp>
                    </>
                  )}
                </TouchableOpacity>
              ) : (
                <>
                  {isActiveSender ? (
                    <>
                      {_selectedTransfer?.operation_status_en?.toLowerCase() ===
                        'failed' && (
                        <View
                          style={{
                            backgroundColor: statuses['failed'].background,
                            ...styles.error_message,
                          }}>
                          <Icon
                            name="alert-circle"
                            size={30}
                            color={'#963B3B'}
                          />

                          <View style={{flex: 1, marginHorizontal: 10}}>
                            <TextCp color={'#963B3B'}>
                              {translate('transaction_failed')}
                            </TextCp>
                          </View>
                        </View>
                      )}

                      {_selectedTransfer?.operation_status_en?.toLowerCase() ==
                        'completed' && (
                        <>
                          {!isDirect && (
                            <View style={styles.progress_message}>
                              <Icon
                                name="alert-circle"
                                size={30}
                                color={'#012938'}
                              />

                              <View style={{flex: 1, marginLeft: 10}}>
                                <TextCp color={'#012938'}>
                                  {translate('transaction_progress')}
                                </TextCp>
                              </View>
                            </View>
                          )}

                          <Rating />
                        </>
                      )}

                      {_selectedTransfer?.operation_status_en?.toLowerCase() ===
                        'aborted' && (
                        <View
                          style={{
                            backgroundColor: statuses['aborted'].background,
                            ...styles.error_message,
                          }}>
                          <Icon
                            name="alert-circle"
                            size={30}
                            color={'#963B3B'}
                          />

                          <View style={{flex: 1, marginHorizontal: 10}}>
                            <TextCp color={'#963B3B'}>
                              {translate('transaction_cancelled')}
                            </TextCp>
                          </View>
                        </View>
                      )}

                      {_selectedTransfer?.operation_status_en?.toLowerCase() ===
                        'in progress' &&
                        !isDirect && (
                          <View style={styles.progress_message}>
                            <Icon
                              name="alert-circle"
                              size={30}
                              color={'#012938'}
                            />

                            <View style={{flex: 1, marginLeft: 10}}>
                              <TextCp color={'#012938'}>
                                {translate('transaction_progress')}
                              </TextCp>
                            </View>
                          </View>
                        )}
                    </>
                  ) : (
                    <>
                      {_selectedTransfer?.operation_status_en?.toLowerCase() ===
                        'completed' && <Rating />}
                    </>
                  )}
                  <Divider height={8} />
                  {_selectedTransfer?.operation_status_en?.toLowerCase() ===
                  'action required' ? (
                    <TouchableOpacity
                      onPress={uploadDocuments}
                      disabled={uploading}
                      style={{
                        ...defaultStyles.button,
                        borderColor: '#eee',
                        marginHorizontal: 0,
                        flexDirection: 'row',
                      }}>
                      {uploading ? (
                        <ActivityIndicator color="#fff" size="small" />
                      ) : (
                        <>
                          <Icon
                            name="upload"
                            color="#fff"
                            size={16}
                            style={{marginRight: 10}}
                          />
                          <TextCp color="#fff" textType={'bold'}>
                            {translate('send_the_documents')}
                          </TextCp>
                        </>
                      )}
                    </TouchableOpacity>
                  ) : (
                    <>
                      {isActiveSender && (
                        <>
                          {isTransferComppleted ? (
                            <>
                              <TouchableOpacity
                                onPress={_repeatTransfer}
                                style={{
                                  ...defaultStyles.button,
                                  borderColor: '#eee',
                                  marginHorizontal: 0,
                                }}>
                                <TextCp color="#fff" textType={'bold'}>
                                  {translate('repeat')}
                                </TextCp>
                              </TouchableOpacity>
                            </>
                          ) : (
                            <>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  alignItems: 'center',
                                }}>
                                {['failed', 'aborted'].includes(
                                  _selectedTransfer?.operation_status_en?.toLowerCase(),
                                ) ? (
                                  <TouchableOpacity
                                    onPress={useAnotherMethod}
                                    style={{
                                      ...defaultStyles.button,
                                      borderColor: '#eee',
                                      marginHorizontal: 0,
                                      flex: 2,
                                    }}>
                                    <TextCp color="#fff" textType={'bold'}>
                                      {translate('use_another_method')}
                                    </TextCp>
                                  </TouchableOpacity>
                                ) : (
                                  <TouchableOpacity
                                    onPress={_repeatTransfer}
                                    style={{
                                      ...defaultStyles.button,
                                      borderColor: '#eee',
                                      marginHorizontal: 0,
                                      flex: 1,
                                    }}>
                                    <TextCp color="#fff" textType={'bold'}>
                                      {translate('repeat')}
                                    </TextCp>
                                  </TouchableOpacity>
                                )}

                                <View style={{width: 16}}></View>
                                <TouchableOpacity
                                  onPress={_cancelTransfer}
                                  disabled={cancelling}
                                  style={{
                                    ...defaultStyles.button,
                                    ...styles.cancelButton,
                                  }}>
                                  {cancelling ? (
                                    <ActivityIndicator
                                      color="#fff"
                                      size="small"
                                    />
                                  ) : (
                                    <TextCp color="#fff" textType={'bold'}>
                                      {translate('delete_button_text')}
                                    </TextCp>
                                  )}
                                </TouchableOpacity>
                              </View>
                            </>
                          )}
                        </>
                      )}
                    </>
                  )}
                </>
              )}
            </View>
          </>
        )}
      </Modalize>
    );
  },
);

const styles = StyleSheet.create({
  modal: {
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    paddingHorizontal: 16,
    flex: 1,
  },
  status: {
    height: 65,
    width: 65,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    borderRadius: 60,
  },
  statusBox: {
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabs: {
    backgroundColor: '#eee',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,.2)',
    padding: 8,
    borderRadius: 40,
    marginTop: 10,
  },
  tab: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
    borderRadius: 40,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 4,
  },
  details: {
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 4,
  },
  section: {
    marginVertical: 10,
  },
  statuses: {
    marginVertical: 16,
  },
  cash: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  teal: {
    fontWeight: 'bold',
  },
  title: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  subTitle: {
    fontSize: 14,
  },
  badge: {
    height: 25,
    paddingHorizontal: 19,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 6,
    borderRadius: 30,
  },
  rowDetails: {
    flexDirection: 'row',
    padding: 10,
  },
  rate_us: {
    padding: 10,
    backgroundColor: '#F7E4CD',
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    marginVertical: 5,
  },
  error_message: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 10,
    marginVertical: 5,
  },
  progress_message: {
    backgroundColor: '#DEEEFA',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 10,
    marginVertical: 5,
  },
  action_required: {
    padding: 10,
    backgroundColor: '#F7E4CD',
    marginTop: 20,
    borderRadius: 10,
  },
  cancelButton: {
    borderColor: '#eee',
    marginHorizontal: 0,
    flex: 1,
    backgroundColor: '#DB1818',
  },
  full_name: {
    fontWeight: 'bold',
    fontSize: 15,
    marginBottom: 4,
    textTransform: 'capitalize',
  },
  footer: {
    position: 'absolute',
    bottom: Platform.OS === 'android' ? 10 : 26,
    left: 0,
    right: 0,
    paddingHorizontal: 16,
  },
  loader: {
    height: height * 0.7,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default OperationDetailComp;
