import {View, TouchableOpacity, StyleSheet} from 'react-native';
import Rate from 'react-native-rate';
import React from 'react';
import MIcon from 'react-native-vector-icons/Ionicons';
import TextCp from '../../../components/TextCp';
import {translate} from '../../../utilities/Translate';
import {showMessage} from 'react-native-flash-message';

const Rating = () => {
  return (
    <TouchableOpacity
      style={styles.rate_us}
      onPress={() => {
        const options = {
          AppleAppID: '6451115466',
          GooglePackageName: 'com.danapay.transfer',
          preferInApp: false,
          openAppStoreIfInAppFails: true,
          fallbackPlatformURL: 'https://www.danapay.io/',
        };
        Rate?.rate(options, (success, errorMessage) => {
          if (success) {
            showMessage({
              type: 'success',
              message: translate('thank_you'),
            });
          }
          if (errorMessage) {
            showMessage({
              type: 'danger',
              message: errorMessage,
            });
          }
        });
      }}>
      <MIcon name="star" color="orange" size={27} style={{marginRight: 8}} />
      <View style={{flex: 1}}>
        <TextCp weight="bold" style={{fontSize: 13}}>
          {translate('transaction_completed')}
        </TextCp>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  rate_us: {
    padding: 10,
    backgroundColor: '#F7E4CD',
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    marginVertical: 5,
  },
});

export default Rating;
