import {View, StyleSheet} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import MCIcon from 'react-native-vector-icons/MaterialCommunityIcons';
import React from 'react';
import TextCp from '../../../components/TextCp';
import useGetLang from '../../../Hooks/useGetLang';
import {translate} from '../../../utilities/Translate';
import {convertToUserTime} from '../../../utilities';

const TimelineItem = ({completed, isLast, history}) => {
  const lang = useGetLang();

  return (
    <>
      {['payment_failed', 'payment_aborted', 'payment_completed'].includes(
        history.status,
      ) && (
        <>
          {history.status === 'payment_completed' ? (
            <View style={styles.itemContainer}>
              <View style={styles.iconColumn}>
                <View style={[styles.icon]}>
                  {history?.datetime ? (
                    <Icon name="check" color="#037375" size={20} />
                  ) : (
                    <MCIcon name="clock-outline" color="#aaa" size={20} />
                  )}
                </View>
                {!isLast && <View style={styles.verticalLine} />}
              </View>
              <View style={styles.contentContainer}>
                <TextCp
                  weight="bold"
                  opacity={0.5}
                  color={history?.datetime ? 'rgb(3,115,117)' : '#000'}
                  style={{
                    fontWeight: 'bold',
                    marginBottom: 4,
                    textTransform: 'capitalize',
                  }}>
                  {history?.datetime
                    ? convertToUserTime(history?.datetime)
                    : translate('pending')}
                </TextCp>
                <TextCp opacity={0.5} style={{fontSize: 12}}>
                  {lang == 'fr' ? history.message_fr : history.message_en}
                </TextCp>
              </View>
            </View>
          ) : (
            <View style={styles.itemContainer}>
              <View style={styles.iconColumn}>
                <View style={[styles.icon]}>
                  {completed ? (
                    <Icon name="x" color="#DE4007" size={20} />
                  ) : (
                    <MCIcon name="clock-outline" size={20} color="#aaa" />
                  )}
                </View>
                {!isLast && <View style={styles.verticalLine} />}
              </View>
              <View style={styles.contentContainer}>
                <TextCp
                  weight="bold"
                  opacity={0.5}
                  color={completed ? '#C42704' : 'rgb(0,0,0)'}
                  style={{
                    fontWeight: 'bold',
                    marginBottom: 4,
                    textTransform: 'capitalize',
                  }}>
                  {history?.datetime
                    ? convertToUserTime(history?.datetime)
                    : translate('pending')}
                </TextCp>
                <TextCp opacity={0.5}>
                  {lang == 'fr' ? history.message_fr : history.message_en}
                </TextCp>
              </View>
            </View>
          )}
        </>
      )}

      {!['payment_failed', 'payment_aborted', 'payment_completed'].includes(
        history.status,
      ) && (
        <View style={styles.itemContainer}>
          <View style={styles.iconColumn}>
            <View style={[styles.icon]}>
              {completed ? (
                <Icon name="check" color="#037375" size={20} />
              ) : (
                <MCIcon name="clock-outline" size={20} color="#aaa" />
              )}
            </View>
            {!isLast && <View style={styles.verticalLine} />}
          </View>
          <View style={styles.contentContainer}>
            <TextCp
              weight="bold"
              opacity={0.5}
              color={completed ? 'rgb(3,115,117)' : 'rgb(0,0,0)'}
              style={{
                fontWeight: 'bold',
                marginBottom: 4,
                textTransform: 'capitalize',
              }}>
              {history?.datetime
                ? convertToUserTime(history?.datetime)
                : translate('pending')}
            </TextCp>
            <TextCp opacity={0.5} style={{fontSize: 12}}>
              {lang == 'fr' ? history.message_fr : history.message_en}
            </TextCp>
          </View>
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  iconColumn: {
    alignItems: 'center',
    width: 24,
  },
  icon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  completedIcon: {
    backgroundColor: '#8FCACD',
  },
  pendingIcon: {
    borderWidth: 2,
    borderColor: '#D3D3D3',
    borderStyle: 'dashed',
  },
  checkmark: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  verticalLine: {
    width: StyleSheet.hairlineWidth,
    flex: 1,
    backgroundColor: 'rgba(0,0,0,.5)',
    marginTop: 3,
    height: 20,
  },
  contentContainer: {
    flex: 1,
    marginLeft: 16,
  },
  title: {
    fontSize: 16,
    marginBottom: 4,
  },
  completedTitle: {
    color: 'rgba(3,115,117,.5)',
    fontWeight: 'bold',
  },
  pendingTitle: {
    color: '#808080',
  },
});

export default TimelineItem;
