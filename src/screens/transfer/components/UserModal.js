import {
  View,
  StyleSheet,
  TouchableOpacity,
  Platform,
  ActivityIndicator,
} from 'react-native';
import React, {forwardRef, useState} from 'react';
import UserAvatar from 'react-native-user-avatar';
import {Modalize} from 'react-native-modalize';
import {translate} from '../../../utilities/Translate';
import TextCp from '../../../components/TextCp';
import useDanaStore from '../../../app_state/store';
import Divider from '../../../components/Divider';
import {removeFavorite} from '../../../apis/auth';
import {showMessage} from 'react-native-flash-message';

const COLORS = ['#4a4e69'];

const UserModal = forwardRef(
  ({handleBack, handleNext, closeUserModel}, ref) => {
    const beneficiary = useDanaStore(state => state.beneficiary);
    const [bene, setBene] = useState(beneficiary);
    const [loading, setLoading] = useState(false);
    const full_name = beneficiary?.full_name;
    const company = beneficiary?.company?.name;
    const isIndividual = beneficiary?.is_individual;

    const deleteContact = async () => {
      try {
        setLoading(true);
        const response = await removeFavorite(bene?.id);
        showMessage({
          type: 'success',
          message: translate('favorited_deleted'),
        });
        closeUserModel();
        setLoading(false);
      } catch (error) {
        showMessage({
          type: 'danger',
          message: error.message,
        });
        setLoading(false);
      }
    };

    return (
      <Modalize ref={ref} adjustToContentHeight>
        <View style={styles.container}>
          {isIndividual ? (
            <View>
              <View style={styles.header}>
                <UserAvatar size={60} name={full_name} bgColors={COLORS} />
                <Divider height={10} />
                <TextCp>{translate('individual')}</TextCp>
                <TextCp style={styles.title} textType={'bold'} opacity={0.7}>
                  {full_name}
                </TextCp>
              </View>
              <View style={styles.content}>
                <View style={styles.item}>
                  <TextCp opacity={0.5}>{translate('phone_number')}</TextCp>
                  <TextCp style={styles.itemText}>
                    {beneficiary?.full_phone_number}
                  </TextCp>
                </View>
                <View style={styles.item}>
                  <TextCp style={styles.itemText} opacity={0.5}>{translate('email')}</TextCp>
                  <TextCp>{beneficiary?.email || 'N/A'}</TextCp>
                </View>
              </View>
            </View>
          ) : (
            <View>
              <View style={styles.header}>
                <UserAvatar size={60} name={company} bgColors={COLORS} />
                <Divider height={10} />
                <TextCp opacity={0.5}>{translate('company')}</TextCp>
                <TextCp style={styles.title} textType={'bold'} opacity={0.7}>
                  {company}
                </TextCp>
              </View>
              <View style={styles.content}>
                <View style={styles.item}>
                  <TextCp opacity={0.5}>{translate('contact_name')}</TextCp>
                  <TextCp
                    style={{...styles.itemText, textTransform: 'capitalize'}}>
                    {full_name}
                  </TextCp>
                </View>
                <View style={styles.item}>
                  <TextCp opacity={0.5}>{translate('phone_number')}</TextCp>
                  <TextCp style={styles.itemText}>
                    {beneficiary?.full_phone_number}
                  </TextCp>
                </View>
                <View style={styles.item}>
                  <TextCp opacity={0.5}>{translate('email')}</TextCp>
                  <TextCp style={styles.itemText}>
                    {beneficiary?.email || 'N/A'}
                  </TextCp>
                </View>
              </View>
            </View>
          )}
        </View>
        <View style={styles.footer}>
          <TouchableOpacity
            style={[styles.button, {backgroundColor: '#000'}]}
            onPress={handleNext}>
            <TextCp color="#fff" textType={'bold'}>
              {translate('confirm')}
            </TextCp>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.button}
            onPress={deleteContact}
            disabled={loading}>
            {loading ? (
              <ActivityIndicator color="#DB1818" size={'small'} />
            ) : (
              <TextCp color="#DB1818" textType={'bold'}>
                {translate('delete_contact')}
              </TextCp>
            )}
          </TouchableOpacity>
        </View>
      </Modalize>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#fff',
    borderTopRightRadius: 50,
    borderTopLeftRadius: 50,
  },
  header: {
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 8,
  },
  content: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 16,
    marginTop: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#555',
        shadowOffset: {
          width: 0,
          height: 1,
        },
        shadowOpacity: 0.25,
        shadowRadius: 1.84,
      },
      android: {
        elevation: 0.8,
      },
    }),
  },
  footer: {
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  item: {
    marginVertical: 8,
  },
  title: {
    fontSize: 24,
    textTransform: 'capitalize',
  },
  itemText: {fontSize: 14, marginTop: 4},
  button: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 48,
    marginVertical: 6,
    borderRadius: 10,
  },
});

export default UserModal;
