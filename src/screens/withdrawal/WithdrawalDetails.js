import {
  View,
  Text,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import React, {useRef} from 'react';
import useDanaStore from '../../app_state/store';
import {translate} from '../../utilities/Translate';
import TextCp from '../../components/TextCp';
import CashInput from '../transfer/components/CashInput';
import CashInMethodComp from '../transfer/components/CashInMethodComp';
import MobileOption from './components/MobileOption';
import Neerowallet from './components/Neerowallet';
import DeliveryOption from './components/DeliveryOption';
import BankOption from './components/BankOption';
import MFIOption from './components/MFIOption';
import AddNeeroWallet from './components/AddNeeroWallet';
import {getAllUserNeeroWallets, getAllUserNeeroWalletsById} from '../../apis/Transfers';
import <PERSON>Button from '../../components/DanaButton';

const WithdrawalDetails = ({navigation}) => {
  const user = useDanaStore(state => state.user);
  const countries = useDanaStore(state => state.countries);
  const [errors, setErrors] = React.useState({});
  const withdrawal_details = useDanaStore(state => state.withdrawal_details);

  const setNeeroWallets = useDanaStore(state => state.setNeeroWallets);
  const updateWithdrawalDetails = useDanaStore(
    state => state.updateWithdrawalDetails,
  );

  const modalizeRef = useRef(null);

  const onOpen = () => {
    modalizeRef.current?.open();
  };

  const closeModalize = () => {
    modalizeRef.current?.close();
  };

  const cashoutMethods = countries.find(
    country => country.country_code === user.country_code,
  ).cashout_methods;

  const getUserWallets = async () => {
    const response = await getAllUserNeeroWalletsById(user.id);
    if (response?.data && response?.data?.length > 0) {
      setNeeroWallets(response?.data);
    }
  };

  return (
    <>
      <SafeAreaView style={{flex: 1}}>
        <View style={styles.toolbar}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <TextCp>Back</TextCp>
          </TouchableOpacity>
        </View>

        <ScrollView contentContainerStyle={{padding: 16, flex: 1}}>
          <View>
            <TextCp>{translate('withdrawal_title')}</TextCp>
            <TextCp>{translate('withdrawal_description')}</TextCp>
          </View>
          {/* <Divider height={20} /> */}
          <View>
            <CashInput
              label={translate('withdraw_amount')}
              onChange={text => {
                updateWithdrawalDetails({
                  amount_in_euro: text,
                });
              }}
              currency={'EUR'}
              value={withdrawal_details?.amount_in_euro}
              error={errors?.amount_in_euro && translate(errors.amount_in_euro)}
            />
            <View>
              <TextCp>
                Vous disposez d'un solde de 1000€ sur votre compte{' '}
              </TextCp>
              <TextCp>Cliquez ici pour tout retirer</TextCp>
            </View>

            {cashoutMethods.map(val => (
              <>
                {val.cashout_method.payment_type.name === 'mobile_money' && (
                  <MobileOption val={val} />
                )}
                {val.cashout_method.payment_type.name ===
                  'manual_bank_transfer' && <BankOption val={val} />}
                {val.cashout_method.payment_type.name === 'delviery' && (
                  <DeliveryOption val={val} />
                )}
                {val.cashout_method.payment_type.name === 'wallet' &&
                  user.country_code === '237' && (
                    <Neerowallet val={val} onPress={onOpen} />
                  )}
                {val.cashout_method.payment_type.name === 'mfi' && (
                  <MFIOption val={val} />
                )}
              </>
            ))}
          </View>
        </ScrollView>
        <View style={styles.footer}>
          <DanaButton
            title={`${translate('next')}`}
            theme="#282828"
            onPress={() => navigation.navigate('WithdrawalVerification')}
          />
        </View>
      </SafeAreaView>
      <AddNeeroWallet
        closeModalize={closeModalize}
        ref={modalizeRef}
        getUserWallets={getUserWallets}
      />
    </>
  );
};

const styles = StyleSheet.create({
  toolbar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  footer: {
    padding: 16,
  },
});

export default WithdrawalDetails;
