import {
    View,
    Text,
    SafeAreaView,
    <PERSON>rollView,
    TouchableOpacity,
    StyleSheet
  } from 'react-native';
  import React from 'react';
  import useDanaStore from '../../app_state/store';
  import {translate} from '../../utilities/Translate';
  import TextCp from '../../components/TextCp';
  import CashInput from '../transfer/components/CashInput';
  
  const WithdrawalSuccess = () => {
    const user = useDanaStore(state => state.user);
    const countries = useDanaStore(state => state.countries);
  
    const cashoutMethods = countries.find(
      country => country.country_code === user.country_code,
    ).cashout_methods;
  
    return (
      <SafeAreaView>
        <View>
            <View></View>
            <View>
                <TextCp>Retrait effectué avec succès</TextCp>
                <TextCp>Vous avez retiré 1000€</TextCp>
            </View>
            <View>
                <View style={styles.row}>
                    <TextCp>fees</TextCp>
                    <TextCp>0.6£</TextCp>
                </View>

                <View style={styles.row}>
                    <TextCp>fees</TextCp>
                    <TextCp>0.6£</TextCp>
                </View>

                <View style={styles.row}>
                    <TextCp>fees</TextCp>
                    <TextCp>0.6£</TextCp>
                </View>

                <View style={styles.row}>
                    <TextCp>fees</TextCp>
                    <TextCp>0.6£</TextCp>
                </View>

                <View style={styles.row}>
                    <TextCp>fees</TextCp>
                    <TextCp>0.6£</TextCp>
                </View>
            </View>

            <View>
                <TextCp>
                    lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua
                </TextCp>
            </View>
        </View>
        <View>
            <TouchableOpacity>
                <TextCp>Retour</TextCp>
            </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  };
  
  const styles = StyleSheet.create({
    toolbar: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
    },
  });
  
  export default WithdrawalSuccess;
  