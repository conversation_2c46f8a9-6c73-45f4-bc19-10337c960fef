import {
  View,
  Text,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import React, {useMemo} from 'react';
import useDanaStore from '../../app_state/store';
import {translate} from '../../utilities/Translate';
import TextCp from '../../components/TextCp';
import CashInput from '../transfer/components/CashInput';
import Input from '../../components/Input';

const WithdrawalVerification = ({navigation}) => {
  const user = useDanaStore(state => state.user);
  const [selected, setSelected] = React.useState('sms');
  const tabs = useMemo(() => {
    return [
      {
        name: translate('sms'),
        value: 'sms',
      },
      {
        name: translate('whatsapp'),
        value: 'whatsapp',
      },
      {
        name: translate('email'),
        value: 'email',
      },
    ];
  }, []);

  const verifyCode = () => {
    navigation.navigate('WithdrawalSuccess');
  };

  return (
    <SafeAreaView>
      <View style={styles.toolbar}>
        <TouchableOpacity>
          <TextCp>Back</TextCp>
        </TouchableOpacity>
        <Text>Withdrawal</Text>
      </View>
      <ScrollView>
        <TextCp>Code de vérification</TextCp>
        <TextCp>
          Choisissez le mode de réception du code de confirmation, puis validez
        </TextCp>
        <View style={styles.tabs}>
          {tabs.map((tab, index) => {
            return (
              <TouchableOpacity
                key={index}
                onPress={() => setSelected(tab.value)}
                style={{
                  backgroundColor: selected === tab.value ? 'blue' : 'white',
                }}>
                <Text>{tab.name}</Text>
              </TouchableOpacity>
            );
          })}
        </View>

        <Input placeholder="Code de vérification" />
      </ScrollView>
      <View>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <TextCp>Back</TextCp>
        </TouchableOpacity>
        <TouchableOpacity onPress={verifyCode}>
          <TextCp>Next</TextCp>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  toolbar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  tabs: {},
});

export default WithdrawalVerification;
