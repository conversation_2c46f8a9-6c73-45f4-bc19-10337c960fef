import {View, KeyboardAvoidingView, <PERSON><PERSON>View} from 'react-native';
import React, {forwardRef} from 'react';
import {Modalize} from 'react-native-modalize';
import {translate} from '../../../utilities/Translate';
import TextCp from '../../../components/TextCp';
import Divider from '../../../components/Divider';
import Input from '../../../components/Input';
import {walletSchema} from '../../../validations';
import {extractYupErrors} from '../../../utilities';
import useDanaStore from '../../../app_state/store';
import {addNeeroAccount} from '../../../apis/Transfers';
import SelectInput from '../../../components/SelectInput';
import DanaButton from '../../../components/DanaButton';
import { scaleRatio } from '../../../theme';

const AddNeeroWallet = forwardRef(({closeModal, getUserWallets}, ref) => {
  const countries = useDanaStore(state => state.countries);
  const user = useDanaStore(state => state.user);
  const [wallet, setWallet] = React.useState({
    title: '',
    country: '',
    owner_name: '',
    country_code: '',
    wallet_account_number: '',
  });
  const [errors, setErrors] = React.useState({});
  const [loading, setLoading] = React.useState(false);

  const saveWallet = async () => {
    try {
      walletSchema.validateSync(wallet, {abortEarly: false});
      setLoading(true);
      const response = await addNeeroAccount(wallet, user.id);
      closeModal();
      getUserWallets();
      setLoading(false);
    } catch (error) {
      setErrors(prev => ({...prev, ...extractYupErrors(error)}));
      setLoading(false);
    }
  };

  return (
    <Modalize ref={ref} adjustToContentHeight>
      <KeyboardAvoidingView
        enabled
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <ScrollView style={{padding: 20}}>
          <TextCp
            textType="bold"
            align="center"
            style={{
              fontSize: 20 * scaleRatio,
            }}>
            {translate('add_neero_wallet')}
          </TextCp>

          <Divider height={20} />

          <Input
            onChange={text =>
              setWallet(prev => ({
                ...prev,
                title: text,
              }))
            }
            label={translate('title')}
            value={wallet.title}
            backgroundColor={'#fff'}
            error={errors?.title && translate(errors?.title)}
          />
          <Divider height={10} />

          <SelectInput
            items={countries.map(val => ({
              value: val.country_code,
              label: val.name,
            }))}
            onValueChange={val => {

              const selected_country = countries.find(
                country => country.country_code === val,
              );
              setWallet(prev => ({
                ...prev,
                country_code: val,
                country: selected_country.name,
              }));
            }}
            label={translate('country')}
            value={wallet.country_code}
            backgroundColor={'#fff'}
            error={errors?.country_code && translate(errors?.country_code)}
          />

          <Divider height={10} />

          <Input
            onChange={text =>
              setWallet(prev => ({
                ...prev,
                owner_name: text,
              }))
            }
            label={translate('owner_name')}
            value={wallet.owner_name}
            backgroundColor={'#fff'}
            error={errors?.owner_name && translate(errors?.owner_name)}
          />

          <Divider height={10} />

          <Input
            onChange={text =>
              setWallet(prev => ({
                ...prev,
                wallet_account_number: text,
              }))
            }
            label={translate('wallet_account_number')}
            value={wallet.wallet_account_number}
            backgroundColor={'#fff'}
            error={
              errors?.wallet_account_number &&
              translate(errors?.wallet_account_number)
            }
          />

          <Divider height={10} />

          <DanaButton
            title={`${translate('save')}`}
            theme="#282828"
            onPress={saveWallet}
            loading={loading}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </Modalize>
  );
});

export default AddNeeroWallet;
