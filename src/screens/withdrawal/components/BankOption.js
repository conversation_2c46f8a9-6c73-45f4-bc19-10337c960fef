import {View, Text, TouchableOpacity} from 'react-native';
import React, {useEffect} from 'react';
import useDanaStore from '../../../app_state/store';
import TextCp from '../../../components/TextCp';
import {translate} from '../../../utilities/Translate';
import {colors} from '../../../theme';
import {getBankAccountsByUserId} from '../../../apis/auth';
import {getInstitutions} from '../../../apis/Transfers';
import SelectInput from '../../../components/SelectInput';
import Input from '../../../components/Input';

const BankOption = ({val}) => {
  const withdrawal_details = useDanaStore(state => state.withdrawal_details);
  const updateWithdrawalDetails = useDanaStore(
    state => state.updateWithdrawalDetails,
  );
  const user = useDanaStore(state => state.user);
  const [bankccounts, setBankccounts] = React.useState([]);
  const [institutions, setInstitutions] = React.useState([]);
  const [loading, setLoading] = React.useState(false);

  const isSelected =
    withdrawal_details?.selected_method?.cashout_method?.payment_type?.name ===
    'manual_bank_transfer';

  const full_name = user?.is_individual ? user?.full_name : user?.company?.name;

  const getUserBankAccount = async () => {
    try {
      setLoading(true);
      const response = await getBankAccountsByUserId(user?.id);
      if (response?.data && response?.data?.length > 0) {
        setBankccounts(response?.data);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const _getInstitutions = async () => {
    const res = await getInstitutions(
      val?.cashout_method?.payment_provider?.name,
      user?.country_code,
    );
    setInstitutions(
      res.data.filter(val => val?.institution?.type === 'bank_transfer'),
    );
  };

  useEffect(() => {
    getUserBankAccount();
    _getInstitutions();
  }, []);

  return (
    <View
      style={{
        borderWidth: isSelected ? 1 : 0,
        borderColor: colors.primary,
        padding: 10,
        borderRadius: 10,
        backgroundColor: '#fff',
        marginVertical: 8,
      }}>
      <TouchableOpacity
        onPress={() => {
          updateWithdrawalDetails({
            selected_method: val,
          });
        }}>
        <TextCp>{translate('bank')}</TextCp>
      </TouchableOpacity>

      {isSelected && (
        <View>
          <SelectInput
            items={institutions?.map(val => ({
              value: val.id,
              label: val.name,
            }))}
            value={withdrawal_details?.bankName?.id}
            onValueChange={text => {
              const _bank = institutions?.find(
                bank => bank?.id?.toString() === text?.toString(),
              );
              updateWithdrawalDetails({bankName: _bank});
            }}
            placeholder={{
              label: translate('select_bank_place_holder'),
              value: translate('select_bank_place_holder'),
            }}
          />

          <View style={styles.userBox}>
            <TextCp opacity={0.5} style={{fontSize: 12}}>
              {translate('account_owner_texr')}
            </TextCp>
            <TextCp textType={'bold'} style={styles.full_name_text}>
              {full_name}
            </TextCp>
          </View>

          {loading && (
            <View style={styles.loading}>
              <ActivityIndicator size="small" color={colors.primary} />
            </View>
          )}

          {bankccounts?.length > 0 && (
            <View style={{marginVericla: 16, paddingVertical: 8}}>
              {bankccounts?.map(val => (
                <TouchableOpacity
                  style={styles.listItem}
                  onPress={() => {
                    updateWithdrawalDetails({
                      selectedIban: val,
                      enteredIban: '',
                    });
                  }}>
                  <View
                    style={{
                      ...styles.checkBox,
                      backgroundColor:
                        withdrawal_details?.selectedIban &&
                        withdrawal_details?.selectedIban?.id === val.id
                          ? colors.primary
                          : 'rgba(0,0,0,.3)',
                    }}>
                    {withdrawal_details?.selectedIban &&
                      withdrawal_details?.selectedIban?.id === val.id && (
                        <Icon name="check" size={17} color="#fff" />
                      )}
                  </View>
                  <View style={{flex: 1}}>
                    <TextCp style={{fontSize: 14}} textType={'bold'}>
                      {val?.iban}
                    </TextCp>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          )}
          <Divider height={10} />
          <Input
            label={translate('enter_iban')}
            value={withdrawal_details?.enteredIban}
            onChange={text => {
              updateWithdrawalDetails({selectedIban: null, enteredIban: text});
            }}
            backgroundColor="#fff"
          />
        </View>
      )}
    </View>
  );
};

export default BankOption;
