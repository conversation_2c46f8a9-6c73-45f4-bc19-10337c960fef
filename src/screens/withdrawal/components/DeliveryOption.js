import {View, Text, TouchableOpacity} from 'react-native';
import React from 'react';
import useDanaStore from '../../../app_state/store';
import TextCp from '../../../components/TextCp';
import {translate} from '../../../utilities/Translate';
import { colors } from '../../../theme';

const DeliveryOption = ({val}) => {
  const withdrawal_details = useDanaStore(state => state.withdrawal_details);
  const updateWithdrawalDetails = useDanaStore(
    state => state.updateWithdrawalDetails,
  );

  const isSelected =
    withdrawal_details?.selected_method?.cashout_method?.payment_type?.name ===
    'delivery';

  return (
    <View
      style={{
        borderWidth: isSelected ? 1 : 0,
        borderColor: colors.primary,
        padding: 10,
        marginVertical:8,
        borderRadius: 10,

      }}>
      <TouchableOpacity
        onPress={() => {
          updateWithdrawalDetails({
            selected_method: val,
          });
        }}>
        <TextCp>{translate('delivery')}</TextCp>
      </TouchableOpacity>
 
    </View>
  );
};

export default DeliveryOption;
