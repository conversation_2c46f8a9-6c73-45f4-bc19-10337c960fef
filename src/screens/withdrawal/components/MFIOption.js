import {View, StyleSheet, TouchableOpacity} from 'react-native';
import React, {useEffect} from 'react';
import useDanaStore from '../../../app_state/store';
import TextCp from '../../../components/TextCp';
import {translate} from '../../../utilities/Translate';
import {colors} from '../../../theme';
import SelectInput from '../../../components/SelectInput';
import Input from '../../../components/Input';

const MFIOption = ({val}) => {
  const withdrawal_details = useDanaStore(state => state.withdrawal_details);
  const updateWithdrawalDetails = useDanaStore(
    state => state.updateWithdrawalDetails,
  );
  const user = useDanaStore(state => state.user);
  const [mfiAccounts, setMFIAccounts] = React.useState([]);
  const [institutions, setInstitutions] = React.useState([]);
  const [loading, setLoading] = React.useState(false);

  const isSelected =
    withdrawal_details?.selected_method?.cashout_method?.payment_type?.name ===
    'mfi';

  const getUserMFIAccount = async () => {
    try {
      setLoading(true);
      const response = await getBankAccountsByUserId(user?.id);
      if (response?.data && response?.data?.length > 0) {
        setMFIAccounts(response?.data);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };

  const _getInstitutions = async () => {
    const res = await getInstitutions(
      val?.cashout_method?.payment_provider?.name,
      user?.country_code,
    );
    setInstitutions(
      res.data.filter(val => val?.institution?.type === 'bank_transfer'),
    );
  };

  useEffect(() => {
    getUserMFIAccount();
    _getInstitutions();
  }, []);

  return (
    <View
      style={{
        borderWidth: isSelected ? 1 : 0,
        borderColor: colors.primary,
        padding: 10,
        borderRadius: 10,
backgroundColor: '#fff',
        marginVertical:8
      }}>
      <TouchableOpacity
        onPress={() => {
          updateWithdrawalDetails({
            selected_method: val,
          });
        }}>
        <TextCp>{translate('mfi')}</TextCp>
      </TouchableOpacity>
      {isSelected && (
        <View>
          <SelectInput
            items={institutions?.map(val => ({
              value: val.id,
              label: val.name,
            }))}
            value={withdrawal_details?.mfiInstitute?.id}
            onValueChange={text => {
              const mfiInstitute = institutions?.find(
                bank => bank?.id?.toString() === text?.toString(),
              );
              updateWithdrawalDetails({mfiInstitute});
            }}
            placeholder={{
              label: translate('select_bank_place_holder'),
              value: translate('select_bank_place_holder'),
            }}
          />
        </View>
      )}
    </View>
  );
};


const styles = StyleSheet.create({
  item: {
    padding: 10,
  },
  details: {
    padding: 10,
  },
});


export default MFIOption;
