import {View, TouchableOpacity, StyleSheet} from 'react-native';
import React, {use, useEffect} from 'react';
import useDanaStore from '../../../app_state/store';
import Input from '../../../components/Input';
import TextCp from '../../../components/TextCp';
import Icon from 'react-native-vector-icons/Ionicons';
import {translate} from '../../../utilities/Translate';
import {colors} from '../../../theme';
import {getMMAccountsByUserId} from '../../../apis/auth';
import {getInstitutions} from '../../../apis/Transfers';
import SelectInput from '../../../components/SelectInput';

const MobileOption = ({val}) => {
  const user = useDanaStore(state => state.user);
  const withdrawal_details = useDanaStore(state => state.withdrawal_details);
  const [MMccounts, setMMccounts] = React.useState([]);
  const [institutions, setInstitutions] = React.useState([]);
  const updateWithdrawalDetails = useDanaStore(
    state => state.updateWithdrawalDetails,
  );
  const [loading, setLoading] = React.useState(false);

  const isSelected =
    withdrawal_details?.selected_method?.cashout_method?.payment_type?.name ===
    'mobile_money';

  const getUserMMAccount = async () => {
    try {
      setLoading(true);
      const resposne = await getMMAccountsByUserId(user.id);
      if (resposne.data && resposne.data.length > 0) {
        setMMccounts(resposne.data);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const _getInstitutions = async () => {
    const res = await getInstitutions(
      val?.cashout_method?.payment_provider?.name,
      user?.country_code,
    );
    setInstitutions(
      res.data.filter(val => val?.institution?.type === 'mobile_money'),
    );
  };

  useEffect(() => {
    getUserMMAccount();
    _getInstitutions();
  }, []);

  return (
    <View
      style={{
        borderWidth: isSelected ? 1 : 0,
        borderColor: colors.primary,
        padding: 10,
        borderRadius: 10,
        backgroundColor: '#fff',
        marginVertical: 8,
      }}>
      <TouchableOpacity
        onPress={() => {
          updateWithdrawalDetails({
            selected_method: val,
          });
        }}>
        <TextCp>{translate('mobile_money')}</TextCp>
      </TouchableOpacity>
      {isSelected && (
        <View>
          <SelectInput
            items={institutions?.map(val => ({
              value: val.id,
              label: val.name,
            }))}
            value={withdrawal_details?.mobile_money_institution?.id}
            onValueChange={text => {
              const mobile_money_institution = institutions?.find(
                mm => mm?.id?.toString() === text?.toString(),
              );
              updateWithdrawalDetails({mobile_money_institution});
            }}
            placeholder={{
              label: translate('select_operator'),
              value: translate('select_operator'),
            }}
          />

          {MMccounts?.length > 0 && (
            <View style={{marginVericla: 16, paddingVertical: 4}}>
              {MMccounts?.map(val => (
                <TouchableOpacity
                  style={styles.listItem}
                  onPress={() => {
                    updateWithdrawalDetails({
                      MMSelectedPhone: val,
                      MMEnteredPhone: '',
                    });
                  }}>
                  <View
                    style={{
                      ...styles.checkBox,
                      backgroundColor:
                        MMccounts?.MMSelectedPhone &&
                        withdrawal_details?.MMSelectedPhone?.id === val?.id
                          ? colors.primary
                          : 'rgba(0,0,0,.3)',
                    }}>
                    {withdrawal_details?.MMSelectedPhone &&
                      withdrawal_details?.MMSelectedPhone?.id === val.id && (
                        <Icon name="check" size={18} color="#fff" />
                      )}
                  </View>
                  <View style={{flex: 1}}>
                    <TextCp
                      textType={
                        'bold'
                      }>{`+${val?.account?.country_code} ${val?.phone_number}`}</TextCp>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          )}

          <View style={styles.phoneContainer}>
            <View style={styles.code}>
              <TextCp>{`+${user?.country_code}`}</TextCp>
            </View>

            <Input
              label={translate('PhoneNumber_placeholder')}
              value={withdrawal_details?.MMEnteredPhone}
              onChange={text => {
                updateWithdrawalDetails({
                  MMSelectedPhone: null,
                  MMEnteredPhone: text,
                });
              }}
              style={styles.phoneInput}
              backgroundColor="#fff"
            />
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  phoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  code: {
    paddingHorizontal: 16,
    backgroundColor: 'rgba(0,0,0,.07)',
    marginRight: 10,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  phoneInput: {
    flex: 1,
  },

  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },

  checkBox: {
    height: 24,
    width: 24,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 8,
  },
  loading: {
    marginVertical: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
});

export default MobileOption;
