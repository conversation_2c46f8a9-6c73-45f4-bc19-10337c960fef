import {View, TouchableOpacity, StyleSheet} from 'react-native';
import React, {useEffect, useRef} from 'react';
import useDanaStore from '../../../app_state/store';
import TextCp from '../../../components/TextCp';
import {translate} from '../../../utilities/Translate';
import {colors} from '../../../theme';
import {getAllUserNeeroWallets} from '../../../apis/Transfers';
import AddNeeroWallet from './AddNeeroWallet';

const Neerowallet = ({val, onPress}) => {
  const withdrawal_details = useDanaStore(state => state.withdrawal_details);
  const updateWithdrawalDetails = useDanaStore(
    state => state.updateWithdrawalDetails,
  );
  const setNeeroWallets = useDanaStore(state => state.setNeeroWallets);
  const neeroWallets = useDanaStore(state => state.neeroWallets);
  const [loading, setLoading] = React.useState(false);

  const isSelected =
    withdrawal_details?.selected_method?.cashout_method?.payment_type?.name ===
    'wallet';

  const getUserWallets = async () => {
    const response = await getAllUserNeeroWallets();
    if (response?.data && response?.data?.length > 0) {
      setNeeroWallets(response?.data);
    }
  };

  useEffect(() => {
    getUserWallets();
  }, []);

  return (
    <View
      style={{
        borderWidth: isSelected ? 1 : 0,
        borderColor: colors.primary,
        borderRadius: 10,
        marginVertical: 8,
        backgroundColor: '#fff',
        padding: 10,
      }}>
      <TouchableOpacity
        style={styles.item}
        onPress={() => {
          updateWithdrawalDetails({
            selected_method: val,
          });
        }}>
        <TextCp>{translate('wallet')}</TextCp>
      </TouchableOpacity>

      {isSelected && (
        <View style={styles.details}>
          {neeroWallets.map(wallet => (
            <TouchableOpacity
              style={styles.listItem}
              onPress={() => {
                updateWithdrawalDetails({
                  neeroAccount: val,
                });
              }}>
              <View
                style={{
                  ...styles.checkBox,
                  backgroundColor:
                  withdrawal_details?.neeroAccount &&
                    withdrawal_details?.neeroAccount?.id === val?.id
                      ? colors.primary
                      : 'rgba(0,0,0,.3)',
                }}>
                {withdrawal_details?.neeroAccount &&
                  withdrawal_details?.neeroAccount?.id === val.id && (
                    <Icon name="check" size={18} color="#fff" />
                  )}
              </View>
              <View style={{flex: 1}}>
                <TextCp
                  textType={'bold'}>{`${val?.neero_wallet_accounts}`}</TextCp>
              </View>
            </TouchableOpacity>
          ))}

          <TouchableOpacity onPress={onPress}>
            <TextCp>{translate('add_new_wallet')}</TextCp>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  item: {
    padding: 10,
  },
  details: {
    padding: 10,
  },
});

export default Neerowallet;
