import {StyleSheet, Dimensions} from 'react-native';
const baseHeight = 896;

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;

export const Height = windowHeight;
export const Width = windowWidth;
export const aspectRatio = Height / Width;
export const scaleRatio = (Height / baseHeight).toFixed(2);
const scaleR = (Height / baseHeight).toFixed(2);

export const borderRadius = 8;
export const pagePadding = 16;
export const titleFontSize = 22 * scaleR;
export const subtitleFontSize= 15 * scaleR;
export const tutorialTextFontSize = 20;
export const buttonTextFontSize = 15;
export const grayBackground = '#D1D1D1';
export const inputTextHeight = 56;
export const inputTextFontSize = 16;
export const iconsColor = '#CDCDCD';
export const payOptionFontSize = 36;
export const buttonBorderRadius = 10;
export const moneyFontSize = 30;
export const blueTextBtnColor = '#037375';
export const bulletInActive = '#C3C7CB';
export const toolBarHeight = 40;

export const colors = {
  darkColor: '#010101',
  primary: '#037375',
  light: '#6ea7ff',
  textColor: '#eeeeee',
  grey: '#aab7bf',
  green: '#61dd67',
  red: '#c1121f',
};

export const gs = StyleSheet.create({
  inputTextStyle: {
    height: 40,
    borderColor: '#ced4da',
    borderWidth: 1,
    borderRadius: 5,
    paddingLeft: 10,
  },
  noScrollContainer: {
    flex: 1,
  },
  flexOne: {
    flex: 1,
  },
  button: {
    borderRadius: 10,
    textTransform: 'capitalize',
  },
  title: {
    fontSize: 30 * scaleR,
    color: '#010101',
  },
  subtitle: {
    fontSize: 17,
    color: '#444',
  },
  centerText: {
    textAlign: 'center',
    fontSize: 20,
  },
  buttonLight: {
    backgroundColor: '#fff',
    elevation: 4,
    borderRadius: 10,
    paddingVertical: 10,
    paddingHorizontal: 12,
  },
  buttonBlue: {
    backgroundColor: '#0698fc',
    elevation: 4,
    borderRadius: 10,
    paddingVertical: 10,
    paddingHorizontal: 12,
  },
  buttonLightColor: {
    color: '#666',
    fontSize: 14,
    alignSelf: 'center',
    textTransform: 'capitalize',
  },
  buttonBlueTextColor: {
    color: '#fff',
    fontSize: 14,
    alignSelf: 'center',
    textTransform: 'capitalize',
  },

  normal: {
    fontSize: 14,
    color: '#101010',
    marginBottom: 16,
  },
  toolbar: {
    height: 30,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  page: {
    paddingHorizontal: 26,
    backgroundColor: '#fff',
    flex: 1,
  },
  contactList: {
    marginVertical: 20,
  },
  buttonBox: {
    flexDirection: 'row',
    marginTop: 20,
  },
  btnRight: {
    flex: 1,
    paddingLeft: 20,
  },
  btnLeft: {
    flex: 1,
    paddingRight: 20,
  },

  boxShadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 5,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6.68,
    elevation: 4,
    marginHorizontal: 5,
  },
});
