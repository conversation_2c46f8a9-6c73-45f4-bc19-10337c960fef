const statues = {
  payment_started: {bg: '#eee', text: '#777'},
  payment_completed: {bg: '#e3fcef', text: '#037375'},
  completed: {bg: '#e3fcef', text: '#037375'},
  funds_received: {bg: '#eee', text: '#777'},
  transfer_in_progress: {bg: '#fff0b3', text: '#8F7200'},
  initiated: {bg: '#fff0b3', text: '#8F7200'},
  deposit_failed: {bg: '#feeae6', text: '#9D2007'},
  deposit_completed: {bg: '#e3fcef', text: '#037375'},
  deposit_in_progress: {bg: '#fff0b3', text: '#8F7200'},
  transfer_failed: {bg: '#feeae6', text: '#9D2007'},
  transfer_completed: {bg: '#e3fcef', text: '#037375'},
  withdraw_requested: {bg: '#eee', text: '#777'},
  withdraw_in_progress: {bg: '#fff0b3', text: '#8F7200'},
  withdraw_failed: {bg: '#feeae6', text: '#9D2007'},
  error: {bg: '#feeae6', text: '#9D2007'},
  withdraw_completed: {bg: '#e3fcef', text: '#037375'},
  payment_requested: {bg: '#eee', text: '#777'},
  payment_failed: {bg: '#feeae6', text: '#9D2007'},
  withdraw_pending: {bg: '#fff0b3', text: '#8F7200'},
  pending: {bg: '#fff0b3', text: '#8F7200'},
  verified: {bg: '#fff0b3', text: '#8F7200'},
  suspicious: {bg: '#FFCF99', text: '#B86200'},
  started: {bg: '#fff0b3', text: '#8F7200'},
};

const ots = {
  instant_transfer: {
    text: 'instant',
    icon: 'arrow-up-right',
    bgColor: '#96E84A32',
    iconColor: '#17BA08',
  },
  instant_transfer_receiver: {
    text: 'instant',
    icon: 'arrow-down-left',
    iconColor: '#E37602',
    bgColor: '#E8A94A32',
  },

    direct_transfer: {
    text: 'instant',
    icon: 'arrow-up-right',
    bgColor: '#96E84A32',
    iconColor: '#17BA08',
  },
  direct_transfer_receiver: {
    text: 'instant',
    icon: 'arrow-down-left',
    iconColor: '#E37602',
    bgColor: '#E8A94A32',
  },
  payment_request: {text: 'instant', icon: 'arrow-down-right'},
  withdraw: {
    text: 'payout',
    icon: 'minus',
    iconColor: '#E37602',
    bgColor: '#E8A94A32',
  },
  deposit: {
    text: 'deposit',
    icon: 'plus',
    bgColor: '#96E84A32',
    iconColor: '#17BA08',
  },
  bulk_transfer: {
    text: 'bulkPayment',
    icon: 'zap',
  },
  direct_transfer: {
    text: 'direct_transfer',
    icon: 'arrow-up-right',
    bgColor: '#96E84A32',
    iconColor: '#17BA08',
  },
  campaign_referral_reward: {
    text: 'campaign_referral_reward',
    icon: 'gift',
    bgColor: '#E0F7F5', // Light teal background
    iconColor: '#008080', // Classic teal
  },
};

// const iconsAndColor = {
//   deposit: {icon: 'plus', color: '#17BA08', bg: '#96E84A'},
//   withdraw: {icon: 'minus', color: '#E37602', bg: '#E8A94A'},
//   transfer: {icon: 'send', color: '#17BA08', bg: '#96E84A'},
//   direct_transfer: {icon: 'send', color: '#17BA08', bg: '#96E84A'},
// };

export {statues, ots};
