import AsyncStorage from '@react-native-async-storage/async-storage';
const itemName = 'danapay_state';

export const storeData = async (value) => {
  try {
    const jsonValue = JSON.stringify(value);
    await AsyncStorage.setItem(itemName, jsonValue);
  } catch (e) {}
};

export const getData = async () => {
  const jsonValue = await AsyncStorage.getItem(itemName);
  return jsonValue != null ? JSON.parse(jsonValue) : null;
};

export const getDataWillCallBack = async (callback) => {
  try {
    const jsonValue = await AsyncStorage.getItem(itemName);
    jsonValue != null ? JSON.parse(jsonValue) : null;
    callback(jsonValue);
  } catch (e) {}
};

export const clearData = async () => {
  try {
    return await AsyncStorage.clear();
  } catch (e) {}
};

export const removeKeys = async (key) => {
  try {
    return await AsyncStorage.removeItem(key);
  } catch (e) {}
};
