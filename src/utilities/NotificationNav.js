export const Nav = (notification, navigator) => {
  const types = [
    'user_activation_status',
    'user_verification_status',
    'transfer_status',
    'payment_status',
    'cashout_status',
  ];

  const data = notification.data;

  if (data.notification_type === types[0]) {
  } else if (data.notification_type === types[1]) {
    navigator.navigate('Sumsub');
  } else if (data.notification_type === types[2]) {
    navigator.navigate('Transfers', {transaction_id: data.id});
  } else if (data.notification_type === types[3]) {
  } else if (data.notification_type === types[4]) {
  } else if (data.notification_type === types[5]) {
  }
};
