export const OrganizeRepeat = (transferDetails) => {
  const {
    amount_in_euros,
    type,
    recipient_name,
    destinationCity,
    destinationQuarter,
    payment_delivery,
    phone_number,
    country_code,
  } = transferDetails.data;

  const name = recipient_name.split(' ');
  let payMethod = '';
  let cfa = amount_in_euros * 655.957;
  if (type === 'card') {
    payMethod = 'Bank card';
  } else {
    payMethod = 'Bank Transfer';
  }

  const beneficiary = {
    firstName: name[0],
    lastName: name[1],
    country: country_code,
    phone: `${phone_number}`,
  };
  const cash = {
    cash_in_method: '',
    amount_in_euro: amount_in_euros,
    amount_in_cfa: cfa.toFixed(2),
    cash_in_method_name: payMethod,
  };
  const beneficiaryLocation = {
    city: destinationCity,
    district: destinationQuarter,
    isPaid: payment_delivery,
  };

  return {
    beneficiary,
    cash,
    beneficiaryLocation,
  };
};

export const repeatFromFavorites = (favorite) => {
  const {latest_payment, contact} = favorite;
  let cash = {euros: '', cfa: '', payMethod: ''};
  let beneficiaryLocation = {city: '', district: '', isPaid: ''};
  const beneficiary = {
    firstName: contact.first_name,
    lastName: contact.last_name,
    country: contact.country_code,
    phone: contact.phone_number,
  };

  if (favorite.latest_payment !== null) {
    cash = {
      euros: latest_payment.amount_in_euros,
      cfa: latest_payment.cfa.toFixed(2),
      payMethod: latest_payment.payMethod,
    };
    beneficiaryLocation = {
      city: '',
      district: '',
      isPaid: true,
    };
  }


  return {
    beneficiary,
    cash,
    beneficiaryLocation,
  };
};
