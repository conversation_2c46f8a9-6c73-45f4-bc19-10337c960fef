export const arrangeTransfer = (transfer) => {
  let transferData = {};
  let res = [];
  transfer.map((value) => {
    const month = getMonth(value.created_at);
    if (month in transferData) {
      res.push(value);
      transferData[month] = res;
    } else {
      res = [];
      res.push(value);
      transferData[month] = res;
    }
  });
  return transferData;
};

const getMonth = (date) => {
  const monthNames = [
    'january',
    'february',
    'march',
    'april',
    'may',
    'june',
    'july',
    'august',
    'september',
    'october',
    'november',
    'december',
  ];
  const d = new Date(date.split(' ')[0]);
  const m = parseInt(d.getMonth());
  return monthNames[m];
};
