import _ from 'lodash';
export const cleanError = (errorObj) => {
  const err = {statusCode: '', main_message: '', all_errors: ''};
  err.statusCode = _.get(errorObj, 'response.status', '');
  err.main_message = _.get(errorObj, 'response.data.message', '');
  if (err.statusCode == 422) {
    const errors = _.get(errorObj, 'response.data.errors', '');
    const errorMes = Object.values(errors);
    const err_errors = errorMes.map((val) => val[0]).join(',');

    err.all_errors = `${err_errors}`;
    
  } else if (err.statusCode == 400) {
    const error = _.get(errorObj, 'response.data.error', '');
    err.all_errors = `${error}`;
  }
  return err;
};
