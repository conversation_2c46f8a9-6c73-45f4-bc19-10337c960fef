import * as RNLocalize from 'react-native-localize';
import {countries} from './countries';
import moment from 'moment-timezone';
import {parsePhoneNumber, isValidPhoneNumber} from 'libphonenumber-js';
import {Buffer} from 'buffer';
import RNFS from 'react-native-fs';
import appsFlyer from 'react-native-appsflyer';
import useDanaStore from '../app_state/store';

export function convertCurrencyTo(currency, amount) {
  const exchangeRateToCFA = 655.957;
  if (currency.toLowerCase() === 'cfa') {
    const cash = amount * exchangeRateToCFA;
    return {status: true, amount: cash.toFixed(0)};
  } else if (currency.toLowerCase() === 'euros') {
    const cash = amount / exchangeRateToCFA;
    return {status: true, amount: cash.toFixed(2)};
  } else {
    return {status: false, amount: 'currency no supported'};
  }
}

export function getCountry() {
  let countryCode = RNLocalize.getCountry();
  let country = countries.filter(
    value => value.country_code.toUpperCase() === countryCode.toUpperCase(),
  );
  return country[0];
}

export const cleanObject = obj => {
  for (var propName in obj) {
    if (
      obj[propName] === null ||
      obj[propName] === undefined ||
      obj[propName] === 'All'
    ) {
      delete obj[propName];
    }
  }
  return obj;
};

export const checkEmptyValues = object => {
  let results = {hasEmpty: false, values: []};
  for (let attribute in object) {
    if (object[attribute] === '') {
      results = {
        ...results,
        hasEmpty: true,
        values: [...results.values, attribute],
      };
    }
  }
  return results;
};

export const getNumberAndCountry = (contact, state_countries) => {
  const data = {country: null, phone: ''};

  if (contact?.phoneNumbers[0]?.number.includes('+')) {
    const res = parsePhoneNumber(contact?.phoneNumbers[0]?.number);

    const phone_country = state_countries.find(
      val => val?.country_code === res?.countryCallingCode,
    );

    if (phone_country) {
      data['country'] = phone_country;
    }

    data['phone_number'] = res.nationalNumber;
  } else {
    data['phone_number'] = contact?.phoneNumbers[0]?.number.replace(/\s/g, '');
  }

  return data;
};

export const isValid = (country_code, phone_number, country) => {
  const cleanNUmber = `${country_code}${phone_number}`.replace(/[^x\d]/g, '');
  const isVerified = isValidPhoneNumber(cleanNUmber, country);
  return isVerified;
};

export const validateEmail = email => {
  const emailRegExp = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegExp.test(email);
};

export const extractDigits = phone_number =>
  phone_number.replace(/[^x\d]/g, '');

export const spaceWithUnderScore = string =>
  string.trim().toLowerCase().replace(/\s/g, '_');

export const capitalizeFirstLetter = str => {
  if (!str) return;
  if (Array.isArray(str)) {
    const strArray = str.join(', ');
    return strArray.length
      ? strArray.charAt(0).toUpperCase() + strArray.slice(1)
      : strArray;
  } else {
    return str.length ? str.charAt(0).toUpperCase() + str.slice(1) : str;
  }
};

export const extractYupErrors = error => {
  if (!error) return {};
  return error?.inner?.reduce((obj, e) => {
    if (!(e.path in obj)) obj[e.path] = [];
    obj[e.path] = obj[e.path].concat(e.errors);
    return obj;
  }, {});
};

export const isValidString = str => {
  return str != null && str.trim().length > 0;
};

export const arrayBufferToBase64 = buffer => {
  let binary = '';
  const bytes = new Uint8Array(buffer);
  const len = bytes.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return Buffer.from(binary, 'binary').toString('base64');
};

export const generatePDF = async (id, response) => {
  const path = `${RNFS.DocumentDirectoryPath}/invoice-${id}.pdf`;
  await RNFS.writeFile(path, response, 'base64');
};

export const blobToBase64 = blob => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(blob);
    reader.onloadend = () => {
      const base64data = reader.result.split(',')[1]; // Remove data URL prefix
      resolve(base64data);
    };
    reader.onerror = reject;
  });
};

export const canUseWithdrawal = beneficiary => {
  return !(
    beneficiary?.company?.is_active && beneficiary?.company?.is_verified
  );
};

export const toLowerCaseWordsOnly = str => {
  return str
    .toLowerCase()
    .replace(/[^a-zA-Z ]/g, '')
    .replace(/\s+/g, '_');
};

export const hideEmail = email => {
  // Split the email into username and domain
  const [username, domain] = email.split('@');

  // If username or domain is invalid, return the original email
  if (!username || !domain) return email;

  // Hide part of the username
  const visiblePart = username.slice(0, 1); // First character of the username
  const hiddenPart = '*'.repeat(Math.max(username.length - 1, 0));

  // Return the partially hidden email
  return `${visiblePart}${hiddenPart}@${domain}`;
};

export const filterPaymentStatus = statusArray => {
  const statuses = ['payment_completed', 'payment_failed', 'payment_aborted'];

  const anyPassed = statusArray
    ?.filter(val => {
      if (statuses.includes(val.status)) {
        if (val.datetime) {
          return true;
        }
      } else {
        return false;
      }
    })
    .map(val => val.status);

  const results = statusArray?.filter(val => {
    if (statuses.includes(val.status)) {
      if (val.status === 'payment_completed') {
        if (anyPassed.length === 0) {
          return true;
        } else {
          if (anyPassed.includes('payment_completed')) {
            return true;
          } else {
            return false;
          }
        }
      }

      if (val.status === 'payment_failed' && val.datetime !== null) {
        return true;
      }

      if (val.status === 'payment_aborted' && val.datetime !== null) {
        return true;
      }
    } else {
      return true;
    }
  });

  return results;
};

export const convertToUserTime = kampalaTime => {
  if (!kampalaTime) return '';

  // Parse the time as Kampala time (UTC+3)
  const kampalaTimeMoment = moment.tz(
    kampalaTime,
    'YYYY-MM-DD HH:mm:ss',
    'Africa/Kampala',
  );

  // Convert to user's local timezone
  return kampalaTimeMoment
    .clone()
    .tz(moment.tz.guess()) // Get user's timezone
    .format('DD-MM-YYYY HH:mm:ss');
};

export const parseAmountCurrency = input => {
  // Return early if input is null, undefined, or not a string/number
  if (input == null) return null;

  // Convert input to string, handling both number and string inputs
  const inputStr = String(input);
  if (!inputStr.trim()) return null;

  const match = inputStr.match(/^([\d,.]+)([A-Za-z]*)$/);
  if (!match) return null;

  return {
    amount: parseFloat(match[1]?.replace(',', ''))?.toFixed(2),
    currency: match[2] || null, // Return null if no currency is found
  };
};

export const appsFlyerTracking = (event, properties) => {
  const userAccetedDataCollection =
    useDanaStore.getState().userAccetedDataCollection;
  if (!userAccetedDataCollection) return;

  console.log({event, properties});

  appsFlyer.logEvent(
    event,
    {...properties},
    res => {
      console.log('Event logged:', res);
    },
    err => {
      console.error('Error logging event:', err);
      console.log(err.response);
    },
  );
};
