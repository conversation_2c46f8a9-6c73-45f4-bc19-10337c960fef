import Echo from 'laravel-echo';
import Config from 'react-native-config';
import Pusher from 'pusher-js/react-native';
import {broadcastingAuth} from '../apis/auth';

export const initWebSocket = token => {
  
  Pusher.logToConsole = Platform.OS === 'ios' ? false : true;
  const KEY = '1ec7db3d-24ad-62a0-b0d1-0242ac110002';
  const optionsData = {
    cluster: 'mt1',
    wssHost: Config.WEBSOCKET,
    wsHost: Config.WEBSOCKET,
    wssPort: 6002,
    wsPort: 6001,
    enabledTransports: ['ws', 'wss'],
    forceTLS: true,
    encrypted: true,
  };

  let PusherClient = new Pusher(KEY, {
    authorizer: (channel, options) => {
      return {
        authorize: (socketId, callback) => {
          broadcastingAuth(socketId, channel, token)
            .then(response => {
              callback(false, response);
            })
            .catch(error => {
              callback(true, error);
            });
        },
      };
    },
    ...optionsData,
  });

  const echo = new Echo({
    Pusher,
    broadcaster: 'pusher',
    client: PusherClient,
    ...optionsData,
  });

  return echo;
};

export default initWebSocket;
