export const arrangeTransfer = (transfer) => {
  let transferData = [];
  let firstMonth = getMonth(transfer[0].created_at);
  transferData.push({trans_type: 'month', month: firstMonth});

  transfer.map((value) => {
    const month = getMonth(value.created_at);
    if (month === firstMonth) {
      value.trans_type = 'transaction';
      transferData.push(value);
    } else {
      firstMonth = month;
      transferData.push({trans_type: 'month', month});
      value.trans_type = 'transaction';
      transferData.push(value);
    }
  });
  return transferData;
};

const getMonth = (date) => {
  const monthNames = [
    'january',
    'february',
    'march',
    'april',
    'may',
    'june',
    'july',
    'august',
    'september',
    'october',
    'november',
    'december',
  ];
  const d = new Date(date.split(' ')[0]);
  const m = parseInt(d.getMonth());
  return monthNames[m];
};
