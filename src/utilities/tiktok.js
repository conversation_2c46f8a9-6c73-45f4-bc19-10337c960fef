import Config from 'react-native-config';
import SHA256 from 'crypto-js/sha256';

export const tiktokLogEvents = async (
  event_name,
  user = null,
  properties = {},
  platform = 'android',
) => {
  const data = {
    event_source: 'web',
    event_source_id: Config.TIKTOK_PIXEL_ID,
    data: [
      {
        event: event_name,
        event_time: Date.now(),
        user: {
          external_id:
            user === null ? 'anon_user' : SHA256(user.external_id).toString(),
          email: user === null ? 'anon_user' : SHA256(user.email).toString(),
          Phone:
            user === null
              ? 'anon_user'
              : SHA256(user.full_phone_number).toString(),
          'User agent': platform,
        },
        properties,
        page: {
          url: 'https://www.danapay.com',
          referrer: 'https://www.danapay.com',
        },
      },
    ],
  };

  const response = await fetch(
    'https://business-api.tiktok.com/open_api/v1.3/event/track/',
    {
      method: 'POST',
      headers: {
        'Access-Token': Config.TIKTOK_ACCESS_TOKEN,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    },
  );

  const result = await response.json();
  console.log({result});
  return result;
};
