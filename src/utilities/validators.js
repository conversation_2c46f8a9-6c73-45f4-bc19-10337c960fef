export function validateEmail(email) {
  const expression =
    /(?!.*\.{2})^([a-z\d!#$%&'*+\-\/=?^_`{|}~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+(\.[a-z\d!#$%&'*+\-\/=?^_`{|}~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+)*|"((([\t]*\r\n)?[\t]+)?([\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|\\[\x01-\x09\x0b\x0c\x0d-\x7f\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))*(([\t]*\r\n)?[\t]+)?")@(([a-z\d\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|[a-z\d\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF][a-z\d\-._~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]*[a-z\d\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])\.)+([a-z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|[a-z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF][a-z\d\-._~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]*[a-z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])\.?$/i;
  return expression.test(email);
}

export function validateName(name) {
  const nameExp = /^[a-z ,.'-]+$/i;
  return name.test(nameExp);
}

export function validateCountry(country) {
  const countryExp = /^[a-z0-9 ,.'-]+$/i;
  return country.test(countryExp);
}

export function validateCity(city) {
  const cityExp = /^[a-z0-9 ,.'-]+$/i;
  return city.test(cityExp);
}

export function validateDistrict(district) {
  const districtExp = /^[a-z0-9 ,.'-]+$/i;
  return district.test(districtExp);
}

export function PhoneCheck(phone) {
  let phone_number = phone.replace(/[-()\s]/g, ''); //trim space
  const resExp = /^(00)\d+$/;
  if (phone_number.test(resExp)) {
    let cleanedPhone = phone_number.replace(/00/, '+');
    return cleanedPhone;
  } else {
    return phone_number;
  }
}

export const emptyFieldsInObject = (body, fields) => {
  let hasEmpty = false;
  for (let field of fields) {
    if (body[field].trim().length === 0 || body[field].trim() === '') {
      hasEmpty = true;
      break;
    }
  }
  return hasEmpty;
};
