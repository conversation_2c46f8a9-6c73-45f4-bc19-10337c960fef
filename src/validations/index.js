import * as yup from 'yup';

let EMAIL_REGX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

export const locationSchema = yup.object().shape({
  city: yup.string().required('city_is_required'),
  address_line: yup.string().required('address_is_required'),
});

export const locationWithPostalCodeSchema = yup.object().shape({
  city: yup.string().required('city_is_required'),
  address_line: yup.string().required('address_is_required'),
  postal_code: yup.string().required('postal_code_required'),
});

export const registerSchema = yup.object().shape({
  isEmailRequired: yup.boolean().default(false).required(),
  isPostalCode: yup.boolean().default(false).required(),
  email: yup.string().when('isEmailRequired', {
    is: val => val === true, // Condition
    then: schema => schema.matches(EMAIL_REGX, 'email_is_invalid'), // Validation if true
    otherwise: schema =>
      schema.notRequired().matches(EMAIL_REGX, 'email_is_invalid'), // Validation if false
  }),
  firstname: yup.string().required('first_name_required'),
  lastname: yup.string().required('last_name_required'),
  city: yup.string().required('city_is_required'),
  address_line: yup.string().required('address_is_required'),
  postal_code: yup.string().when('isPostalCode', {
    is: val => val === true, // Condition
    then: schema => schema.required('postal_code_required'), // Validation if true
    otherwise: schema => schema.notRequired(), // Validation if false
  }),
});


// Safe input pattern - allows alphanumeric, spaces, and basic punctuation but blocks special characters used in SQL injection
const SAFE_INPUT_PATTERN = /^[a-zA-Z0-9\s.,\-_()\[\]&@!?'"]+$/;
const INVALID_INPUT_MESSAGE = 'input_contains_invalid_characters';

export const hearWitEventSchema = yup.object().shape({
  hear_about_us: yup
    .string()
    .required('please_select_where_you_heard_about_us'),
  event: yup
    .string()
    .required('please_enter_event_name')
    .matches(SAFE_INPUT_PATTERN, INVALID_INPUT_MESSAGE),
});

export const hearWitOtherSchema = yup.object().shape({
  hear_about_us: yup
    .string()
    .required('please_select_where_you_heard_about_us'),
  other: yup
    .string()
    .required('please_enter_other_source')
    .matches(SAFE_INPUT_PATTERN, INVALID_INPUT_MESSAGE),
});

export const hearWitRefSchema = yup.object().shape({
  hear_about_us: yup
    .string()
    .required('please_select_where_you_heard_about_us'),
  referralCode: yup.string().required('please_enter_referral_code'),
  // .maxLength(6).minLength(6)
  // .matches(SAFE_INPUT_PATTERN, INVALID_INPUT_MESSAGE),
});

export const hearSchema = yup.object().shape({
  hear_about_us: yup
    .string()
    .required('please_select_where_you_heard_about_us'),
});

export const jobSchema = yup.object().shape({
  activity_id: yup.string().required('please_select_your_activity'),
  monthly_revenue: yup.string().required('monthly_revenue_is_required'),
});

export const jobWithOtherSchema = yup.object().shape({
  job: yup.string().required('job_is_required'),
  monthly_revenue: yup.string().required('monthly_revenue_is_required'),
  activity_id: yup.string().required('please_select_your_activity'),
});

export const CountriesSchema = yup.object().shape({
  receivingCountries: yup
    .array('should_be_an_array')
    .of(yup.string())
    .required('please_select_country'),
});


export const CompanyContactSchema = yup.object().shape({
  account_type: yup.string().required('account_type_required'),
  phone_number: yup.string().required('phone_number_required'),
  first_name: yup.string().required('first_name_required'),
  last_name: yup.string().required('last_name_required'),
  company_name: yup.string().required('company_name_required'),
  country: yup.string().required('country_required'),
});

export const ContactSchema = yup.object().shape({
  account_type: yup.string().required('account_type_required'),
  phone_number: yup.string().required('phone_number_required'),
  first_name: yup.string().required('first_name_required'),
  last_name: yup.string().required('last_name_required'),
  country: yup.string().required('country_required'),
});

export const PayInfoSchema = yup.object().shape({
  reason: yup.string().required('reason_required'),
  amount_in_cfa: yup.string().required('amount_in_cfa_required'),
  amount_in_euro: yup.string().required('amount_in_euro_required'),
});

export const walletSchema = yup.object().shape({
  title: yup.string().required('title_required'),
  country: yup.string().required('country_required'),
  owner_name: yup.string().required('owner_name_required'),
  country_code: yup.string().required('country_code_required'),
  wallet_account_number: yup
    .string()
    .required('wallet_account_number_required'),
});

